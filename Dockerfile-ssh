FROM ubuntu:22.04

RUN apt update && apt upgrade -y && apt install -y build-essential cmake gcc g++ make wget zlib1g protobuf-compiler
RUN apt update && apt install -y openssh-server vim git tmux screen

WORKDIR /root

# 配置SSH访问
COPY temp_key.pub /tmp/temp_key.pub
RUN mkdir -p .ssh /var/run/sshd \
    && cat /tmp/temp_key.pub >> .ssh/authorized_keys \
    && chmod 600 .ssh/authorized_keys \
    && chmod 700 .ssh \
    && rm /tmp/temp_key.pub

# 启用公钥认证并禁止密码认证
RUN sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config \
&& sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config \
&& sed -i 's@session\s*required\s*pam_loginuid.so@session optional pam_loginuid.so@g' /etc/pam.d/sshd

CMD ["/usr/sbin/sshd", "-D"]