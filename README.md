# 流失预测

## 运行说明 
![运行说明](docs/15EE1396-479F-40A8-B679-1A2049FA2591.png)
服务入口：`src/main.py`为主程序入口
- 读取环境对应的配置文件（dev/prod config）
- 从impala读取用户行为、广告指标数据
- 根据配置启动多个预测pipeline，每个pipeline都是完整的预测流水线，包含数据读取、预处理、模型推理、结果输出4个步骤。新推理模型上线需要继承`pipelines.base.PipelineBase`基类，创建独立pipeline实现上述步骤。可参考：`pipelines.base.PipelineRandomGuess`。
- 各方案预测结果写入统一的数据表

### impala代理服务
```bash
docker build -t docker-ssh -f Dockerfile-ssh .
docker run -d --name ssh-env --restart=unless-stopped -p 6001:22 docker-ssh
# LOGIN
apt-get update && apt-get install -y socat
socat TCP-LISTEN:21050,fork,reuseaddr TCP:************:21050 &
```


## 目录结构
- src/: 源代码目录，包含项目的主要代码实现
    - dataset/: 数据集处理
    - utils/: 工具函数
    - model/: 模型定义
    - misc/: 杂项
    - pipelines/: 方案流水线
    - main.py: 入口
- notebooks/: Jupyter notebook目录
- docs/: 项目文档目录
- logs/: 日志文件目录
- config-dev.yaml: 开发环境配置文件
- config-prod.yaml: 生产环境配置文件


## 数据库表结构
DDL参考：`misc/table_ddl.sql`

- **user_profile**：记录用户画像、用户-广告关联数据
- **user_ltv_actual**：用户真实LTV
- **user_ltv_prediction**：用户预测LTV
- **ad_ltv_actual**：广告真实LTV
- **ad_ltv_prediction**：广告预测LTV

## 相关文档
- [实验记录V0](https://dcnxwc3kjxpb.feishu.cn/wiki/IUXywbhBWiqjCjk27X1cFjg8nxd?fromScene=spaceOverview)
- [实验记录V1](https://dcnxwc3kjxpb.feishu.cn/wiki/DaWJwg2GHiFBwmkNifMc0Pz7nWg?fromScene=spaceOverview)
- [广告干预](https://dcnxwc3kjxpb.feishu.cn/wiki/WNNdwsOE3irgPUk79QpcjDrTnHc)


## 🚀 后续改进点
**数据方面**
- [ ] 更丰富的表格特征：参考心流调控给到的表格特征设计
- [ ] 原始日志序列：参考NLP的sentence embedding问题，直接使用日志序列，不被人工经验束缚
- [ ] 日志时间信息：参考time-series领域，融合事件发生的时间信息
- [ ] 数据覆盖度提升：现在只用了完整的可观测数据（例如3日预测7日），不完整的数据被遗弃（1/2日预测7日）

**模型方面**
- [ ] 特征处理的手段：连续特征的离散化，类别特征的编码，归一化
- [ ] 特征融合方案：显式特征交叉（参考deepFM），隐式特征交叉conv/attn等
- [ ] 网络架构调整，多方案的尝试

