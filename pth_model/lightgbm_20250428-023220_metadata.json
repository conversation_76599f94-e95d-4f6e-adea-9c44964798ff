{"feature_names": ["channel_ty_csite_id", "proj_main_channel", "sdk_proj_app_id", "adtrace_attribution_mode", "sdk_virtual_channel", "proj_cloud_id", "sdk_os_version", "proj_project_id", "adtrace_platform", "sdk_hardware_name", "sdk_virtual_channel_version", "sdk_systemversion", "sdk_split_ua_result", "adtrace_assist_platform", "sdk_carrier_name", "sdk_memory", "sdk_device_name", "channel_ty_video_id", "sdk_timezone", "adtrace_is_greylist", "proj_game_type", "proj_virtual_channel", "adtrace_click_match_count", "sdk_machine", "adtrace_main_platform", "sdk_package_name", "proj_client_id", "sdk_countrycode", "adtrace_pay_times", "sdk_language", "adtrace_namespace", "channel_ty_creative_id", "sdk_disk", "adtrace_reattributed", "sdk_carrierinfo", "adtrace_device_caid_main_version", "proj_sub_channel", "adtrace_ctit", "sdk_os_name", "adtrace_orderid", "adtrace_organic_traffic", "sdk_model", "adtrace_yidun_validate", "sdk_mntid", "adtrace_is_blacklist", "channel_ua", "sdk_devicename", "d1_retention", "d2_retention", "d3_retention", "d1_payment", "d2_payment", "d3_payment", "device_name", "device_price", "behav.fish_gun_fire_sum_count_0", "behav.gun_level_up_consume_count_0", "behav.recharge_count_0", "behav.fish_table_enter_count_0", "behav.skill_use_count_0", "behav.bkrpt_count_0", "behav.total_recharge_amount_0", "behav.login_count_0", "behav.shop_center_enter_count_0", "behav.achievement_reward_count_0", "behav.have_checkin_reward_0", "behav.startup_quest_finish_game_count_0", "behav.click_120215_count_0", "behav.click_120214_count_0", "behav.click_120093_count_0", "behav.click_120092_count_0", "behav.resource_total_down_count_0", "behav.resource_down_count_0", "behav.activity_midnight_0", "behav.activity_morning_0", "behav.activity_afternoon_0", "behav.activity_night_0", "behav.game_time_in_minutes_0", "behav.final_delta_0", "behav.max_delta_0", "behav.min_delta_0", "behav.max_gunlevel_0", "behav.min_gunlevel_0", "behav.max_boss_rate_0", "behav.total_catch_0", "behav.total_catch_boss_0", "behav.fish_gun_fire_sum_count_1", "behav.gun_level_up_consume_count_1", "behav.recharge_count_1", "behav.fish_table_enter_count_1", "behav.skill_use_count_1", "behav.bkrpt_count_1", "behav.total_recharge_amount_1", "behav.login_count_1", "behav.shop_center_enter_count_1", "behav.achievement_reward_count_1", "behav.have_checkin_reward_1", "behav.startup_quest_finish_game_count_1", "behav.click_120215_count_1", "behav.click_120214_count_1", "behav.click_120093_count_1", "behav.click_120092_count_1", "behav.resource_total_down_count_1", "behav.resource_down_count_1", "behav.activity_midnight_1", "behav.activity_morning_1", "behav.activity_afternoon_1", "behav.activity_night_1", "behav.game_time_in_minutes_1", "behav.final_delta_1", "behav.max_delta_1", "behav.min_delta_1", "behav.max_gunlevel_1", "behav.min_gunlevel_1", "behav.max_boss_rate_1", "behav.total_catch_1", "behav.total_catch_boss_1", "behav.fish_gun_fire_sum_count_2", "behav.gun_level_up_consume_count_2", "behav.recharge_count_2", "behav.fish_table_enter_count_2", "behav.skill_use_count_2", "behav.bkrpt_count_2", "behav.total_recharge_amount_2", "behav.login_count_2", "behav.shop_center_enter_count_2", "behav.achievement_reward_count_2", "behav.have_checkin_reward_2", "behav.startup_quest_finish_game_count_2", "behav.click_120215_count_2", "behav.click_120214_count_2", "behav.click_120093_count_2", "behav.click_120092_count_2", "behav.resource_total_down_count_2", "behav.resource_down_count_2", "behav.activity_midnight_2", "behav.activity_morning_2", "behav.activity_afternoon_2", "behav.activity_night_2", "behav.game_time_in_minutes_2", "behav.final_delta_2", "behav.max_delta_2", "behav.min_delta_2", "behav.max_gunlevel_2", "behav.min_gunlevel_2", "behav.max_boss_rate_2", "behav.total_catch_2", "behav.total_catch_boss_2"], "feature_types": ["category", "category", "float64", "category", "category", "int64", "category", "category", "category", "category", "category", "category", "category", "category", "category", "float64", "category", "float64", "float64", "float64", "int64", "category", "float64", "category", "category", "category", "category", "category", "float64", "category", "category", "category", "float64", "int64", "category", "float64", "category", "float64", "category", "float64", "int64", "category", "float64", "category", "float64", "category", "category", "int64", "int64", "int64", "float64", "float64", "float64", "category", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64"], "cat_features": ["channel_ty_csite_id", "proj_main_channel", "adtrace_attribution_mode", "sdk_virtual_channel", "sdk_os_version", "proj_project_id", "adtrace_platform", "sdk_hardware_name", "sdk_virtual_channel_version", "sdk_systemversion", "sdk_split_ua_result", "adtrace_assist_platform", "sdk_carrier_name", "sdk_device_name", "proj_virtual_channel", "sdk_machine", "adtrace_main_platform", "sdk_package_name", "proj_client_id", "sdk_countrycode", "sdk_language", "adtrace_namespace", "channel_ty_creative_id", "sdk_carrierinfo", "proj_sub_channel", "sdk_os_name", "sdk_model", "sdk_mntid", "channel_ua", "sdk_devicename", "device_name"], "text_features": ["channel_ua", "adtrace_device_ua"]}