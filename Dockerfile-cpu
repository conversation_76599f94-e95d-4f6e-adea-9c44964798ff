FROM python:3.11-slim

RUN apt-get update
RUN DEBIAN_FRONTEND=noninteractive TZ=Asia/Shanghai apt-get -y install tzdata
RUN echo "Asia/Shanghai" > /etc/timezone && rm /etc/localtime && dpkg-reconfigure -f noninteractive tzdata

RUN apt-get install -y --no-install-recommends  \
    autoconf automake bzip2 dpkg-dev file g++ gcc imagemagick \
    libbz2-dev libc6-dev libcurl4-openssl-dev libdb-dev libevent-dev libffi-dev libgdbm-dev libglib2.0-dev \
    libgmp-dev libjpeg-dev libkrb5-dev liblzma-dev libmagickcore-dev libmagickwand-dev libmaxminddb-dev \
    libncurses5-dev libncursesw5-dev libpng-dev libpq-dev libreadline-dev libsqlite3-dev libssl-dev libtool \
    libwebp-dev libxml2-dev libxslt-dev libyaml-dev make patch unzip xz-utils zlib1g-dev \
    vim netcat iputils-ping libsasl2-dev libsasl2-2 libsasl2-modules-gssapi-mit tofrodos libgl1-mesa-glx \
    tree man-db rsync cron sudo graphviz graphviz-dev screen curl xz-utils git

RUN apt install -y openssh-server net-tools wget htop

RUN pip install --upgrade pip -i https://mirrors.aliyun.com/pypi/simple
ADD requirements.txt /home/
RUN pip install --no-cache-dir -r /home/<USER>//mirrors.aliyun.com/pypi/simple

COPY . /root/ltv-prediction
WORKDIR /root/ltv-prediction


CMD ["sh", "-c", "sleep infinity"]