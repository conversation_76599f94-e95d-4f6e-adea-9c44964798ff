{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(os.path.abspath(\"..\"))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import time\n", "import os\n", "import pickle\n", "import numpy as np\n", "from collections import defaultdict\n", "from tqdm import tqdm\n", "from typing import List, Tuple, Any, Dict, Type\n", "import pandas as pd\n", "import json\n", "import traceback\n", "from src.config import config\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score\n", "from impala.dbapi import connect\n", "from datetime import datetime\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from src.pipelines.tree_inference import PipelineTreeInference\n", "from src.misc.load_data_v2 import Fish3DLTVDataset\n", "from src.pipelines.cdcm_inference import Pipelinev2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PipelineTreeInferenceTest(PipelineTreeInference):\n", "    \n", "    def _add_device_price(self, df):\n", "        \"\"\" 添加设备价格 \"\"\"\n", "        device2price = pd.read_csv('/wkspace/qiushi/ltv-prediction/data/device2price.csv')\n", "        df_with_price = pd.merge(\n", "            df,\n", "            device2price,\n", "            left_on='sdk_device_name',\n", "            right_on='device_name', \n", "            how='left' \n", "        )\n", "        df_with_price = df_with_price.rename(columns={'price': 'device_price'})\n", "        return df_with_price\n", "\n", "    def output(self, predicted_user_ltv: Dict[str, float], \n", "                predicted_campaign_ltv: Dict[str, float], \n", "                campaign_user_counts: Dict[str, int]): \n", "        \n", "        # 输出用户LTV预测值\n", "        user_ltv_prediction_data = [\n", "            {\n", "                \"sdk_yidun_device_id\": sdk_yidun_device_id,\n", "                \"attribution_day\": self.attribution_ds,\n", "                \"adtrace_platform\": self.user_adtrace_platform[sdk_yidun_device_id],\n", "                \"observed_days\": 3,\n", "                \"predicted_days\": self.days,\n", "                \"pltv\": pltv,\n", "                \"record_partition\": self.ds,\n", "            }\n", "            for sdk_yidun_device_id, pltv in predicted_user_ltv.items()\n", "        ]\n", "        user_ltv_df = pd.DataFrame(user_ltv_prediction_data)\n", "        \n", "        # 汇总用户LTV预测值，输出广告LTV预测值\n", "        campaign_ltv_prediction_data = [\n", "            {\n", "                \"channel_ty_adgroup_id\": channel_ty_adgroup_id,\n", "                \"attribution_day\": self.attribution_ds, # （当前仅包含一天）\n", "                \"adtrace_platform\": self.ad_adtrace_platform[channel_ty_adgroup_id],\n", "                \"observed_days\": 3,\n", "                \"predicted_days\": self.days,\n", "                \"pltv\": pltv,\n", "                \"user_count\": campaign_user_counts[channel_ty_adgroup_id],\n", "                \"record_partition\": self.ds,\n", "            }\n", "            for channel_ty_adgroup_id, pltv in predicted_campaign_ltv.items()\n", "        ]\n", "        campaign_ltv_df = pd.DataFrame(campaign_ltv_prediction_data)\n", "        \n", "        return user_ltv_df, campaign_ltv_df\n", "    \n", "    def execute(self) -> Dict[str, Any]:\n", "        \"\"\"执行完整流水线\"\"\"\n", "        start_time = time.time()\n", "        try:\n", "            # 模型加载\n", "            self.logger.info(\"model loading...\")\n", "            self._load_model()\n", "            \n", "            # 数据加载\n", "            self.logger.info(\"data loading...\")\n", "            campaign_df, behavior_df = self.load_data()\n", "            \n", "            # 数据预处理\n", "            self.logger.info(\"data preprocessing...\")\n", "            id_list, X, cat_features, text_features = self.data_preprocess(campaign_df, behavior_df)\n", "            self.verify_data(X, cat_features=cat_features, text_features=text_features)\n", "            \n", "            # 执行预测\n", "            self.logger.info(\"predicting...\")\n", "            predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts = self.predict(id_list, X)\n", "            \n", "            # 结果保存\n", "            self.logger.info(\"saving results...\")\n", "            user_ltv_df_m1, campaign_ltv_df_m1 = self.output(predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts)\n", "            \n", "            # 更新任务状态\n", "            self.update_task_status(flag='success')\n", "            \n", "            # 收集执行结果\n", "            result = {\n", "                \"status\": \"success\",\n", "                \"info\": f\"len(predicted_user_ltv): {len(predicted_user_ltv)}, len(predicted_campaign_ltv): {len(predicted_campaign_ltv)}\",\n", "            }\n", "            \n", "        except Exception as e:\n", "            self.logger.error(f\"pipeline {self.name} failed: {str(e)}\", exc_info=True)\n", "            exc_type, exc_value, exc_traceback = sys.exc_info()\n", "            tb = traceback.extract_tb(exc_traceback)[-1] \n", "            result = {\n", "                \"status\": \"failed\",\n", "                \"error\": str(e),\n", "                \"error_line\": f'{tb.filename}:{tb.lineno}',\n", "            }\n", "            self.update_task_status(flag='failure')\n", "            \n", "        return user_ltv_df_m1, campaign_ltv_df_m1\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# 显示所有列\n", "pd.set_option('display.max_columns', None)\n", "\n", "# 显示所有行\n", "pd.set_option('display.max_rows', None)\n", "\n", "# 设置显示宽度更大（不换行）\n", "pd.set_option('display.width', 1000)\n", "\n", "# 防止列内容被省略\n", "pd.set_option('display.max_colwidth', None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_ltv_df_m1, campaign_ltv_df_m1 = PipelineTreeInferenceTest(**config[\"pipelines\"][0]).execute()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.pipelines.lgb_inference import PipelineLGBInference"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PipelineLGBInferenceTest(PipelineLGBInference):\n", "\n", "    def output(self, predicted_user_ltv: Dict[str, float], \n", "                predicted_campaign_ltv: Dict[str, float], \n", "                campaign_user_counts: Dict[str, int]): \n", "        \n", "        # 输出用户LTV预测值\n", "        user_ltv_prediction_data = [\n", "            {\n", "                \"sdk_yidun_device_id\": sdk_yidun_device_id,\n", "                \"attribution_day\": self.attribution_ds,\n", "                \"adtrace_platform\": self.user_adtrace_platform[sdk_yidun_device_id],\n", "                \"observed_days\": 3,\n", "                \"predicted_days\": self.days,\n", "                \"pltv\": pltv,\n", "                \"record_partition\": self.ds,\n", "            }\n", "            for sdk_yidun_device_id, pltv in predicted_user_ltv.items()\n", "        ]\n", "        user_ltv_df = pd.DataFrame(user_ltv_prediction_data)\n", "        \n", "        # 汇总用户LTV预测值，输出广告LTV预测值\n", "        campaign_ltv_prediction_data = [\n", "            {\n", "                \"channel_ty_adgroup_id\": channel_ty_adgroup_id,\n", "                \"attribution_day\": self.attribution_ds, # （当前仅包含一天）\n", "                \"adtrace_platform\": self.ad_adtrace_platform[channel_ty_adgroup_id],\n", "                \"observed_days\": 3,\n", "                \"predicted_days\": self.days,\n", "                \"pltv\": pltv,\n", "                \"user_count\": campaign_user_counts[channel_ty_adgroup_id],\n", "                \"record_partition\": self.ds,\n", "            }\n", "            for channel_ty_adgroup_id, pltv in predicted_campaign_ltv.items()\n", "        ]\n", "        campaign_ltv_df = pd.DataFrame(campaign_ltv_prediction_data)\n", "        \n", "        return user_ltv_df, campaign_ltv_df\n", "    \n", "    def execute(self) -> Dict[str, Any]:\n", "        \"\"\"执行完整流水线\"\"\"\n", "        start_time = time.time()\n", "        try:\n", "            # 模型加载\n", "            self.logger.info(\"model loading...\")\n", "            self._load_model()\n", "            \n", "            # 数据加载\n", "            self.logger.info(\"data loading...\")\n", "            campaign_df, behavior_df = self.load_data()\n", "            \n", "            # 数据预处理\n", "            self.logger.info(\"data preprocessing...\")\n", "            id_list, X, cat_features, text_features = self.data_preprocess(campaign_df, behavior_df)\n", "            self.verify_data(X, cat_features=cat_features, text_features=text_features)\n", "            \n", "            # 执行预测\n", "            self.logger.info(\"predicting...\")\n", "            predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts = self.predict(id_list, X)\n", "            \n", "            # 结果保存\n", "            self.logger.info(\"saving results...\")\n", "            user_ltv_df_m3, campaign_ltv_df_m3 = self.output(predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts)\n", "            \n", "            # 更新任务状态\n", "            self.update_task_status(flag='success')\n", "            \n", "            # 收集执行结果\n", "            result = {\n", "                \"status\": \"success\",\n", "                \"info\": f\"len(predicted_user_ltv): {len(predicted_user_ltv)}, len(predicted_campaign_ltv): {len(predicted_campaign_ltv)}\",\n", "            }\n", "            \n", "        except Exception as e:\n", "            self.logger.error(f\"pipeline {self.name} failed: {str(e)}\", exc_info=True)\n", "            exc_type, exc_value, exc_traceback = sys.exc_info()\n", "            tb = traceback.extract_tb(exc_traceback)[-1] \n", "            result = {\n", "                \"status\": \"failed\",\n", "                \"error\": str(e),\n", "                \"error_line\": f'{tb.filename}:{tb.lineno}',\n", "            }\n", "            self.update_task_status(flag='failure')\n", "            \n", "        return user_ltv_df_m3, campaign_ltv_df_m3\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_ltv_df_m3, campaign_ltv_df_m3 = PipelineLGBInferenceTest(**config[\"pipelines\"][2]).execute()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class Pipelinev2Test(Pipelinev2):\n", "\n", "    def output(self, \n", "                predicted_user_ltv: Dict[str, float], \n", "                predicted_campaign_ltv: Dict[str, float], \n", "                campaign_user_counts: Dict[str, int], \n", "                user_adtrace_platform: Dict[str, str], \n", "                ad_adtrace_platform: Dict[str, str]): \n", "        \"\"\" 输出预测结果 \"\"\"\n", "        \n", "        # 输出用户LTV预测值\n", "        user_ltv_prediction_data = [\n", "            {\n", "                \"sdk_yidun_device_id\": did,\n", "                \"attribution_day\": self.attribution_ds,\n", "                \"adtrace_platform\": user_adtrace_platform[did],\n", "                \"observed_days\": 3,\n", "                \"predicted_days\": self.days,\n", "                \"pltv\": pltv,\n", "                \"record_partition\": self.ds,\n", "            }\n", "            for did, pltv in predicted_user_ltv.items()\n", "        ]\n", "        \n", "        user_ltv_df = pd.DataFrame(user_ltv_prediction_data).fillna(-1)\n", "        self.logger.info(\"User LTV Prediction Example:\")\n", "        self.logger.info(user_ltv_df.head().to_string())\n", "                \n", "        # 输出广告LTV预测值\n", "        ad_ltv_prediction_data = [\n", "            {\n", "                \"channel_ty_adgroup_id\": channel_ty_adgroup_id,\n", "                \"attribution_day\": self.attribution_ds, # （当前仅包含一天）\n", "                \"adtrace_platform\": ad_adtrace_platform[channel_ty_adgroup_id],\n", "                \"observed_days\": 3,\n", "                \"predicted_days\": self.days,\n", "                \"pltv\": pltv,\n", "                \"user_count\": campaign_user_counts[channel_ty_adgroup_id],\n", "                \"record_partition\": self.ds,\n", "            }\n", "            for channel_ty_adgroup_id, pltv in predicted_campaign_ltv.items()\n", "        ]\n", "        \n", "        ad_ltv_df = pd.DataFrame(ad_ltv_prediction_data).fillna(-1)\n", "        self.logger.info(\"Campaign LTV Prediction Example:\")\n", "        self.logger.info(ad_ltv_df.head().to_string())\n", "                \n", "        return user_ltv_df, ad_ltv_df\n", "            \n", "    def execute(self) -> Dict[str, Any]:\n", "        \"\"\"执行完整流水线\"\"\"\n", "        start_time = time.time()\n", "        # if self.load_task_status(self.ds, self.name) is not None:\n", "        #     self.logger.info(f\"pipeline {self.name} already run, skip\")\n", "        #     return {\n", "        #         \"status\": \"skip\",\n", "        #         \"info\": f\"pipeline {self.name} already run, skip\",\n", "        #     }\n", "        try:\n", "            # 数据加载\n", "            self.logger.info(\"data loading...\")\n", "            ltv_dataset, user_adtrace_platform, ad_adtrace_platform = self.load_data()\n", "            \n", "            # 执行预测\n", "            self.logger.info(\"predicting...\")\n", "            predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts = self.predict(ltv_dataset)\n", "            \n", "            # 结果保存\n", "            self.logger.info(\"saving results...\")\n", "            user_ltv_df_m2, ad_ltv_df_m2 = self.output(predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts, user_adtrace_platform, ad_adtrace_platform)\n", "\n", "            # 更新任务状态\n", "            # self.update_task_status(flag='success')\n", "            \n", "        except Exception as e:\n", "            self.logger.error(f\"pipeline {self.name} failed: {str(e)}\", exc_info=True)\n", "            exc_type, exc_value, exc_traceback = sys.exc_info()\n", "            tb = traceback.extract_tb(exc_traceback)[-1] \n", "            result = {\n", "                \"status\": \"failed\",\n", "                \"error\": str(e),\n", "                \"error_line\": f'{tb.filename}:{tb.lineno}',\n", "            }\n", "            self.update_task_status(flag='failure')\n", "            \n", "            \n", "        return user_ltv_df_m2, ad_ltv_df_m2"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-04-28 07:23:02,369 INFO pipeline v2_origin_pipline initialized\n", "2025-04-28 07:23:02,392 INFO data loading...\n", "2025-04-28 07:23:28,955 INFO predicting...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Time taken: 0:00:26.552133\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/17299 [00:00<?, ?it/s]\n", "2025-04-28 07:23:29,990 ERROR pipeline v2_origin_pipline failed: 'UMKkECVH2aFBSFQQQAbFIaOpMC5DRAFV'\n", "Traceback (most recent call last):\n", "  File \"/tmp/ipykernel_24774/2109840479.py\", line 66, in execute\n", "    predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts = self.predict(ltv_dataset)\n", "                                                                       ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/wkspace/sunyi/LTV_prediction/src/pipelines/cdcm_inference.py\", line 78, in predict\n", "    seq_feature, user_feature = ltv_dataset.transform(did)\n", "                                ^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/wkspace/sunyi/LTV_prediction/src/misc/load_data_v2.py\", line 349, in transform\n", "    data = self.behavior_stats[did]\n", "           ~~~~~~~~~~~~~~~~~~~^^^^^\n", "KeyError: 'UMKkECVH2aFBSFQQQAbFIaOpMC5DRAFV'\n"]}, {"ename": "UnboundLocalError", "evalue": "cannot access local variable 'user_ltv_df_m2' where it is not associated with a value", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mUnboundLocalError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m user_ltv_df_m2, ad_ltv_df_m2 \u001b[38;5;241m=\u001b[39m \u001b[43mPipelinev2Test\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mpipelines\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[4], line 87\u001b[0m, in \u001b[0;36mPipelinev2Test.execute\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     79\u001b[0m     result \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m     80\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstatus\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfailed\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     81\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124merror\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mstr\u001b[39m(e),\n\u001b[1;32m     82\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124merror_line\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtb\u001b[38;5;241m.\u001b[39mfilename\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m:\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtb\u001b[38;5;241m.\u001b[39mlineno\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m     83\u001b[0m     }\n\u001b[1;32m     84\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mupdate_task_status(flag\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfailure\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m---> 87\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43muser_ltv_df_m2\u001b[49m, ad_ltv_df_m2\n", "\u001b[0;31mUnboundLocalError\u001b[0m: cannot access local variable 'user_ltv_df_m2' where it is not associated with a value"]}], "source": ["user_ltv_df_m2, ad_ltv_df_m2 = Pipelinev2Test(**config[\"pipelines\"][1]).execute()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.misc.load_data_v2 import _load_data_from_impala\n", "campaign_df, behavior_df = _load_data_from_impala()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["campaign_df['real_ltv_7_days'] = campaign_df[['d1_payment','d2_payment','d3_payment','d4_payment','d5_payment','d6_payment','d7_payment']].sum(axis=1)\n", "campaign_df['real_ltv_3_days'] = campaign_df[['d1_payment','d2_payment','d3_payment']].sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["real_ltv_df = campaign_df[['sdk_yidun_device_id', 'channel_ty_adgroup_id', 'real_ltv_3_days', 'real_ltv_7_days']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_ltv_df_m3_ = user_ltv_df_m3[['sdk_yidun_device_id', 'pltv']].rename(columns={'pltv': 'pltv_m3'})\n", "user_ltv_df_m2_ = user_ltv_df_m2[['sdk_yidun_device_id', 'pltv']].rename(columns={'pltv': 'pltv_m2'})\n", "user_ltv_df_m1_ = user_ltv_df_m1[['sdk_yidun_device_id', 'pltv']].rename(columns={'pltv': 'pltv_m1'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_evaluation = real_ltv_df.merge(user_ltv_df_m1_, on='sdk_yidun_device_id', how='outer') \\\n", "                            .merge(user_ltv_df_m2_, on='sdk_yidun_device_id', how='outer') \\\n", "                            .merge(user_ltv_df_m3_, on='sdk_yidun_device_id', how='outer')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_evaluation['base_ltv_7_days'] = df_evaluation['real_ltv_3_days'] * 2.06"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["campaign_df_no_nans = campaign_df[~campaign_df['channel_ty_adgroup_id'].isna()]\n", "available_sdk_id = campaign_df_no_nans['sdk_yidun_device_id'].tolist()\n", "df_evaluation = df_evaluation[df_evaluation['sdk_yidun_device_id'].isin(available_sdk_id)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_evaluation.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_evaluation.isna().sum().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(df_evaluation)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mae = (df_evaluation['real_ltv_7_days'] - df_evaluation['base_ltv_7_days']).abs().mean()\n", "    \n", "# 计算其他指标\n", "mse = ((df_evaluation['real_ltv_7_days'] - df_evaluation['base_ltv_7_days']) ** 2).mean()\n", "rmse = np.sqrt(mse)\n", "\n", "r2 = r2_score(df_evaluation['real_ltv_7_days'], df_evaluation['base_ltv_7_days'])\n", "\n", "# 打印结果\n", "print(\"Base User\")\n", "print(f\"MAE: {mae:.4f}\")\n", "print(f\"RMSE: {rmse:.4f}\")\n", "print(f\"R²: {r2:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mae = (df_evaluation['real_ltv_7_days'] - df_evaluation['pltv_m1']).abs().mean()\n", "    \n", "# 计算其他指标\n", "mse = ((df_evaluation['real_ltv_7_days'] - df_evaluation['pltv_m1']) ** 2).mean()\n", "rmse = np.sqrt(mse)\n", "\n", "r2 = r2_score(df_evaluation['real_ltv_7_days'], df_evaluation['pltv_m1'])\n", "\n", "# 打印结果\n", "print(\"Model1 User\")\n", "print(f\"MAE: {mae:.4f}\")\n", "print(f\"RMSE: {rmse:.4f}\")\n", "print(f\"R²: {r2:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mae = (df_evaluation['real_ltv_7_days'] - df_evaluation['pltv_m2']).abs().mean()\n", "    \n", "# 计算其他指标\n", "mse = ((df_evaluation['real_ltv_7_days'] - df_evaluation['pltv_m2']) ** 2).mean()\n", "rmse = np.sqrt(mse)\n", "\n", "r2 = r2_score(df_evaluation['real_ltv_7_days'], df_evaluation['pltv_m2'])\n", "\n", "# 打印结果\n", "print(\"Model2 User\")\n", "print(f\"MAE: {mae:.4f}\")\n", "print(f\"RMSE: {rmse:.4f}\")\n", "print(f\"R²: {r2:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mae = (df_evaluation['real_ltv_7_days'] - df_evaluation['pltv_m3']).abs().mean()\n", "    \n", "# 计算其他指标\n", "mse = ((df_evaluation['real_ltv_7_days'] - df_evaluation['pltv_m3']) ** 2).mean()\n", "rmse = np.sqrt(mse)\n", "\n", "r2 = r2_score(df_evaluation['real_ltv_7_days'], df_evaluation['pltv_m3'])\n", "\n", "# 打印结果\n", "print(\"Model3 User\")\n", "print(f\"MAE: {mae:.4f}\")\n", "print(f\"RMSE: {rmse:.4f}\")\n", "print(f\"R²: {r2:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(df_evaluation)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pltv_m1_map = df_evaluation.set_index('sdk_yidun_device_id')['pltv_m1'].to_dict()\n", "pltv_m2_map = df_evaluation.set_index('sdk_yidun_device_id')['pltv_m2'].to_dict()\n", "pltv_m3_map = df_evaluation.set_index('sdk_yidun_device_id')['pltv_m3'].to_dict()\n", "\n", "real_ltv_map = df_evaluation.set_index('sdk_yidun_device_id')['real_ltv_7_days'].to_dict()\n", "base_ltv_map = df_evaluation.set_index('sdk_yidun_device_id')['base_ltv_7_days'].to_dict()\n", "\n", "ad_ltv_prediction_data = []\n", "for ad_id, user_list in tqdm(ltv_dataset.c2did.items()):\n", "    values_m1 = [pltv_m1_map.get(uid, np.nan) for uid in user_list]\n", "    values_m2 = [pltv_m2_map.get(uid, np.nan) for uid in user_list]\n", "    values_m3 = [pltv_m3_map.get(uid, np.nan) for uid in user_list]\n", "    \n", "    values_real = [real_ltv_map.get(uid, np.nan) for uid in user_list]\n", "    values_base = [base_ltv_map.get(uid, np.nan) for uid in user_list]\n", "    \n", "    d = {\n", "        \"channel_ty_adgroup_id\": ad_id,\n", "        \"real_ltv_7_days\": sum(values_real),\n", "        \"base_ltv_7_days\": sum(values_base), \n", "        \"pltv_m1\": sum(values_m1),\n", "        \"user_count_m1\": len(values_m1),\n", "        \"pltv_m2\": sum(values_m2),\n", "        \"user_count_m2\": len(values_m2),\n", "        \"pltv_m3\": sum(values_m3),\n", "        \"user_count_m3\": len(values_m3),\n", "    }\n", "    ad_ltv_prediction_data.append(d)\n", "\n", "ad_ltv_df = pd.DataFrame(ad_ltv_prediction_data)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ad_ltv_df.isna().sum().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mae = (ad_ltv_df['real_ltv_7_days'] - ad_ltv_df['base_ltv_7_days']).abs().mean()\n", "    \n", "# 计算其他指标\n", "mse = ((ad_ltv_df['real_ltv_7_days'] - ad_ltv_df['base_ltv_7_days']) ** 2).mean()\n", "rmse = np.sqrt(mse)\n", "\n", "r2 = r2_score(ad_ltv_df['real_ltv_7_days'], ad_ltv_df['base_ltv_7_days'])\n", "\n", "# 打印结果\n", "print(\"Base Campaign\")\n", "print(f\"MAE: {mae:.4f}\")\n", "print(f\"RMSE: {rmse:.4f}\")\n", "print(f\"R²: {r2:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mae = (ad_ltv_df['real_ltv_7_days'] - ad_ltv_df['pltv_m1']).abs().mean()\n", "    \n", "# 计算其他指标\n", "mse = ((ad_ltv_df['real_ltv_7_days'] - ad_ltv_df['pltv_m1']) ** 2).mean()\n", "rmse = np.sqrt(mse)\n", "\n", "r2 = r2_score(ad_ltv_df['real_ltv_7_days'], ad_ltv_df['pltv_m1'])\n", "\n", "# 打印结果\n", "print(\"Model1 Campaign\")\n", "print(f\"MAE: {mae:.4f}\")\n", "print(f\"RMSE: {rmse:.4f}\")\n", "print(f\"R²: {r2:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mae = (ad_ltv_df['real_ltv_7_days'] - ad_ltv_df['pltv_m2']).abs().mean()\n", "    \n", "# 计算其他指标\n", "mse = ((ad_ltv_df['real_ltv_7_days'] - ad_ltv_df['pltv_m2']) ** 2).mean()\n", "rmse = np.sqrt(mse)\n", "\n", "r2 = r2_score(ad_ltv_df['real_ltv_7_days'], ad_ltv_df['pltv_m2'])\n", "\n", "# 打印结果\n", "print(\"Model2 Campaign\")\n", "print(f\"MAE: {mae:.4f}\")\n", "print(f\"RMSE: {rmse:.4f}\")\n", "print(f\"R²: {r2:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mae = (ad_ltv_df['real_ltv_7_days'] - ad_ltv_df['pltv_m3']).abs().mean()\n", "    \n", "# 计算其他指标\n", "mse = ((ad_ltv_df['real_ltv_7_days'] - ad_ltv_df['pltv_m3']) ** 2).mean()\n", "rmse = np.sqrt(mse)\n", "\n", "r2 = r2_score(ad_ltv_df['real_ltv_7_days'], ad_ltv_df['pltv_m3'])\n", "\n", "# 打印结果\n", "print(\"Model3 Campaign\")\n", "print(f\"MAE: {mae:.4f}\")\n", "print(f\"RMSE: {rmse:.4f}\")\n", "print(f\"R²: {r2:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_evaluation['m2_diff'] = abs(df_evaluation['real_ltv_7_days'] - df_evaluation['pltv_m2'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_sorted = df_evaluation.sort_values(by='m2_diff', ascending=False)\n", "df_sorted.head(20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}