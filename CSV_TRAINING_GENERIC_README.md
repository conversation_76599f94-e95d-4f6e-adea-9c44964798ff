# 通用CSV数据处理和训练指南

本指南介绍如何使用通用CSV数据处理流程，自动适配新项目的数据格式，从头开始建立pickle文件，进行编码，并训练any day模型。

## 主要改进

相比原来的版本，新的通用处理流程有以下改进：

1. **自动列类型识别**：自动分析CSV文件结构，识别关键列、分类列、数值列、日期列等
2. **不依赖特定行为特征**：不再硬编码行为特征列名，而是自动识别
3. **灵活的数据适配**：可以处理不同格式的CSV文件
4. **智能数据清洗**：只对关键列进行缺失值检查
5. **详细的分析报告**：生成数据分析和处理报告

## 文件结构

```
src/
├── dataset/
│   ├── generate_from_csv_new.py      # 通用CSV数据处理
│   └── light_data_any_day_prediction.py  # 数据加载模块
├── model/
│   └── torch_model_any_day_prediction.py  # 模型定义
├── train_from_csv_generic.py         # 通用训练脚本
├── test_csv_generic.py               # 通用测试脚本
└── run_csv_training.py               # 完整训练流程脚本
```

## 快速开始

### 1. 测试数据处理

首先测试通用数据处理流程：

```bash
cd src
python test_csv_generic.py
```

### 2. 处理数据并训练

使用通用训练脚本：

```bash
cd src
python train_from_csv_generic.py
```

### 3. 完整流程

或者使用完整的工作流程脚本：

```bash
cd src
python run_csv_training.py
```

## 通用数据处理特性

### 自动列类型识别

系统会自动识别以下类型的列：

- **关键列**：包含 `id`, `device`, `user`, `attribution` 关键词的列
- **日期列**：包含 `date`, `day`, `time` 关键词的列，或明确包含 `attribution_day`, `execute_date` 的列
- **数值列**：可以转换为数值的列
- **分类列**：唯一值比例小于50%的非数值列
- **目标列**：包含 `payment`, `pay`, `revenue`, `amount`, `money` 关键词的数值列
- **行为列**：非目标的数值列

### 智能数据清洗

1. **重复用户删除**：基于设备ID删除重复用户
2. **关键列缺失值处理**：只检查最重要的列（`sdk_yidun_device_id`, `attribution_day`, `channel_ty_adgroup_id`）
3. **数据类型转换**：自动转换日期和数值类型
4. **分类特征编码**：使用自定义编码器处理分类变量

### 行为序列处理

如果数据中包含行为特征和归因日期，系统会自动创建14天的行为序列。如果没有行为数据，会跳过序列创建步骤。

## 详细使用说明

### 数据处理脚本

#### `generate_from_csv_new.py`

通用的CSV数据处理脚本，主要功能：

- `analyze_csv_structure()`: 分析CSV文件结构
- `load_csv_data_generic()`: 通用数据加载
- `preprocess_data_generic()`: 通用数据预处理
- `create_behavior_sequences_generic()`: 创建行为序列
- `process_csv_data_generic()`: 完整数据处理流程

**使用方法：**
```python
from dataset.generate_from_csv_new import process_csv_data_generic

# 处理CSV数据
merged_df, analysis = process_csv_data_generic(
    csv_file_path="csv_data/your_data.csv",
    output_dir="../data",
    new_data=True,
    max_days=14
)
```

### 训练脚本

#### `train_from_csv_generic.py`

通用训练脚本，支持以下参数：

- `csv_file_path`: CSV文件路径
- `batch_size`: 批次大小 (默认: 2048)
- `num_workers`: 数据加载器工作进程数 (默认: 6)
- `max_epochs`: 最大训练轮数 (默认: 500)
- `learning_rate`: 学习率 (默认: 1e-5)
- `weight_decay`: 权重衰减 (默认: 1e-3)
- `hidden_dim`: 隐藏层维度 (默认: 64)
- `max_days`: 最大天数 (默认: 14)

## 工作流程

### 步骤1: 数据分析和加载
1. 自动分析CSV文件结构
2. 识别列类型和特征
3. 加载完整数据

### 步骤2: 数据清洗和预处理
1. 删除重复用户
2. 处理关键列的缺失值
3. 转换数据类型
4. 填充缺失值

### 步骤3: 特征工程
1. 创建行为序列（如果有行为数据）
2. 类别特征编码
3. 生成处理报告

### 步骤4: 模型训练
1. 设置数据模块
2. 创建模型
3. 配置训练器
4. 开始训练

## 输出文件

### 数据处理输出
- `../data/any_day_data.pkl`: 处理后的数据
- `../data/encoding_map.json`: 类别特征编码映射
- `../data/data_analysis.json`: 数据分析报告

### 训练输出
- `../pth_model/any_day_prediction_model_csv_generic_final.pth`: 最终模型
- `../pth_model/any_day_prediction_model_csv_generic_*.pth`: 检查点模型
- `../logs/adp_csv_generic/`: TensorBoard日志

## 数据分析报告

`data_analysis.json` 包含详细的数据分析信息：

```json
{
  "total_rows": 1496588,
  "total_columns": 123,
  "key_columns": ["sdk_yidun_device_id", "attribution_day", ...],
  "categorical_columns": [...],
  "numerical_columns": [...],
  "date_columns": [...],
  "target_columns": [...],
  "behavior_columns": [...],
  "column_types": {...}
}
```

## 适配新项目

### 1. 数据格式要求

确保CSV文件包含以下关键列：
- 设备ID列（包含 `id` 或 `device` 关键词）
- 归因日期列（包含 `attribution_day` 或类似关键词）
- 广告组ID列（包含 `adgroup` 或类似关键词）

### 2. 自定义列识别

如果需要自定义列识别规则，可以修改 `analyze_csv_structure()` 函数中的关键词匹配逻辑。

### 3. 行为数据

如果项目包含行为数据，确保：
- 有归因日期列
- 有设备ID列
- 行为特征为数值类型

## 注意事项

1. **内存使用**: 大文件处理可能需要大量内存，建议：
   - 使用较小的批次大小
   - 减少数据加载器工作进程数
   - 分批处理数据

2. **GPU使用**: 确保有足够的GPU内存，可以通过以下方式调整：
   - 减少批次大小
   - 减少隐藏层维度
   - 使用梯度累积

3. **数据质量**: 系统会自动处理常见的数据质量问题，但建议：
   - 检查关键列的完整性
   - 验证日期格式
   - 确保数值列的数据类型正确

## 故障排除

### 常见问题

1. **没有找到归因日期列**
   - 检查列名是否包含 `attribution_day` 或类似关键词
   - 确保日期格式正确

2. **数据清洗后为空**
   - 检查关键列是否有大量缺失值
   - 调整数据清洗策略

3. **行为序列创建失败**
   - 确认有归因日期列
   - 确认有设备ID列
   - 检查行为特征列的数据类型

### 调试模式

启用详细日志输出：
```bash
python test_csv_generic.py
```

## 性能优化

1. **数据加载优化**:
   - 使用SSD存储
   - 增加数据加载器工作进程数
   - 使用内存映射文件

2. **训练优化**:
   - 使用混合精度训练
   - 启用梯度累积
   - 使用分布式训练

3. **模型优化**:
   - 调整学习率调度
   - 使用更复杂的正则化
   - 尝试不同的损失函数

## 示例

### 基本使用

```bash
# 测试数据处理
python test_csv_generic.py

# 训练模型
python train_from_csv_generic.py

# 使用自定义参数
python train_from_csv_generic.py --batch_size 1024 --max_epochs 300
```

### 自定义配置

```python
from dataset.generate_from_csv_new import process_csv_data_generic

# 自定义处理参数
merged_df, analysis = process_csv_data_generic(
    csv_file_path="your_data.csv",
    output_dir="custom_output",
    new_data=True,
    max_days=30  # 自定义天数
)
```

这个通用框架可以轻松适配不同项目的数据格式，无需修改代码即可处理各种CSV文件。 