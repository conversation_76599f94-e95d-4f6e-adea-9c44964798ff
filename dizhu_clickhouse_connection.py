from clickhouse_driver import Client

cdw_koi_conf = {
    'host': 'tenant-2100386442-cn-beijing.bytehouse.ivolces.com',
    'port': 19000,
    'user': 'bytehouse',
    'password': '6SVcwUyFu8:idesC6wjR3',
    'database': "koi_data",
    'secure': True,
    'settings': {
        'session_timezone': 'Etc/UTC',
        'virtual_warehouse': 'vw-2100386442-olap-vw',
        'dialect_type': 'ANSI',
        'cte_mode': 'INLINED',
        'match_name_ignore_case': 1,
        'rewrite_complex_predicate_by_domain': 1,
        'handle_division_by_zero': 1,
        'enable_group_by_keys_pruning': 0,
        'enable_implicit_arg_type_convert': 1,
        'enable_plan_cache': 1,
        'send_cacheable_table_definitions': 1,
        'catalog_enable_multiple_threads': 1,
        'enable_share_common_plan_node': 1,
        'send_plan_segment_timeout_ms': 30000,
        'max_distributed_connections': 16,
        'input_format_parquet_use_footer_cache': 1,
        'input_format_parquet_max_block_size': 65409,
        'enable_cost_calculator_table_scan_weight_consider_filter': 1,
        'cost_calculator_cte_weight': 0.1,
        'input_format_parquet_use_native_reader': 1,
        'concat_ws_skips_null': 1,
        'cascades_optimizer_timeout': 5000
    }
}


def main():
    conn = Client(**cdw_koi_conf)
    # 1, 简单查询
    sql_01 = "select toInt32(now()),now(), toDateTime(now()), toString(toDateTime(now())), toString(toDateTime(1749797121))"
    res_01 = conn.execute(sql_01)
    print(res_01)
    # 2, 巨量数据查询
    # sql_02 = """
    # select xxxxxx
    # from ga_view.dwd_event_xxxx
    # where day ='2025-07-23'
    # limit 1000000
    # """
    # res_02 = conn.execute_iter(
    #     query=sql_02,
    #     chunk_size=10000  # 批次大小
    # )
    # for batch in res_02:
    #     if not batch:
    #         break
    #     print(f"{len(batch)}")
    conn.disconnect()


if __name__ == '__main__':
    main()