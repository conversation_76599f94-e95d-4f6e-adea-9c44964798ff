import pandas as pd
import numpy as np
import json
from collections import defaultdict

def analyze_payment_users():
    print("正在加载数据...")
    # 加载数据
    df = pd.read_pickle('data/any_day_data.pkl')
    
    print(f"数据形状: {df.shape}")
    print(f"总用户数: {len(df):,}")
    
    # 获取付费相关列
    payment_cols = [col for col in df.columns if 'payment' in col]
    print(f"付费列数量: {len(payment_cols)}")
    
    # 1. 总体付费用户分析
    print("\n" + "="*60)
    print("总体付费用户分析")
    print("="*60)
    
    # 计算每个用户的总付费金额
    total_payment = df[payment_cols].sum(axis=1)
    
    # 付费用户筛选
    paid_users = total_payment > 0
    paid_df = df[paid_users]
    unpaid_df = df[~paid_users]
    
    print(f"付费用户数: {paid_users.sum():,} ({paid_users.mean()*100:.2f}%)")
    print(f"非付费用户数: {(~paid_users).sum():,} ({(~paid_users).mean()*100:.2f}%)")
    print(f"总体付费金额: {total_payment.sum():,.2f}")
    print(f"付费用户平均付费: {total_payment[paid_users].mean():.2f}")
    print(f"付费用户付费中位数: {total_payment[paid_users].median():.2f}")
    print(f"付费用户付费标准差: {total_payment[paid_users].std():.2f}")
    
    # 付费金额分布
    print(f"\n付费金额分布:")
    percentiles = [25, 50, 75, 90, 95, 99]
    for p in percentiles:
        value = total_payment[paid_users].quantile(p/100)
        print(f"  {p}%分位数: {value:.2f}")
    
    # 2. 各天付费用户分析
    print("\n" + "="*60)
    print("各天付费用户分析")
    print("="*60)
    
    print("Day | 付费用户数 | 付费率(%) | 总金额    | 平均付费 | 付费中位数 | 付费标准差")
    print("-" * 80)
    
    for i in range(1, 31):
        col = f'd{i}_payment'
        if col in df.columns:
            day_payment = df[col]
            paid_users_count = (day_payment > 0).sum()
            paid_rate = (day_payment > 0).mean() * 100
            total_amount = day_payment.sum()
            
            # 只对付费用户计算统计量
            paid_amounts = day_payment[day_payment > 0]
            if len(paid_amounts) > 0:
                avg_amount = paid_amounts.mean()
                median_amount = paid_amounts.median()
                std_amount = paid_amounts.std()
            else:
                avg_amount = median_amount = std_amount = 0
            
            print(f"{i:3d} | {paid_users_count:8,} | {paid_rate:8.2f} | {total_amount:8,.2f} | {avg_amount:8.2f} | {median_amount:10.2f} | {std_amount:10.2f}")
    
    # 3. 付费用户付费模式分析
    print("\n" + "="*60)
    print("付费用户付费模式分析")
    print("="*60)
    
    # 分析付费用户的付费天数
    payment_days = (df[payment_cols] > 0).sum(axis=1)
    paid_users_payment_days = payment_days[paid_users]
    
    print(f"付费用户平均付费天数: {paid_users_payment_days.mean():.2f}")
    print(f"付费用户付费天数中位数: {paid_users_payment_days.median():.2f}")
    
    print(f"\n付费天数分布:")
    for days in range(1, 11):  # 显示1-10天的分布
        count = (paid_users_payment_days == days).sum()
        percentage = count / len(paid_users_payment_days) * 100
        print(f"  {days}天: {count:,} 用户 ({percentage:.2f}%)")
    
    # 4. 高价值用户分析
    print("\n" + "="*60)
    print("高价值用户分析")
    print("="*60)
    
    # 定义高价值用户（付费金额前10%）
    high_value_threshold = total_payment[paid_users].quantile(0.9)
    high_value_users = total_payment[paid_users] >= high_value_threshold
    high_value_df = paid_df[high_value_users]
    
    print(f"高价值用户数: {high_value_users.sum():,} ({high_value_users.mean()*100:.2f}% of 付费用户)")
    print(f"高价值用户平均付费: {total_payment[paid_users][high_value_users].mean():.2f}")
    print(f"高价值用户付费中位数: {total_payment[paid_users][high_value_users].median():.2f}")
    
    # 5. 付费用户特征分析
    print("\n" + "="*60)
    print("付费用户特征分析")
    print("="*60)
    
    # 对比付费用户和非付费用户的关键特征
    key_features = ['sdk_disk', 'sdk_memory', 'proj_game_type', 'channel_ua']
    
    print("特征对比 (付费用户 vs 非付费用户):")
    print("特征名称           | 付费用户均值 | 非付费用户均值 | 差异")
    print("-" * 60)
    
    for feature in key_features:
        if feature in df.columns:
            paid_mean = paid_df[feature].mean()
            unpaid_mean = unpaid_df[feature].mean()
            diff = paid_mean - unpaid_mean
            print(f"{feature:15} | {paid_mean:11.2f} | {unpaid_mean:13.2f} | {diff:6.2f}")
    
    # 6. 付费用户留存分析
    print("\n" + "="*60)
    print("付费用户留存分析")
    print("="*60)
    
    retention_cols = [col for col in df.columns if 'retention' in col]
    
    print("付费用户留存情况:")
    print("Day | 留存用户数 | 留存率(%) | 留存且付费 | 留存率(%)")
    print("-" * 50)
    
    for i in range(1, 31):
        retention_col = f'd{i}_retention'
        payment_col = f'd{i}_payment'
        
        if retention_col in df.columns and payment_col in df.columns:
            # 所有用户的留存情况
            retained = df[retention_col] > 0
            retained_count = retained.sum()
            retention_rate = retained.mean() * 100
            
            # 留存且付费的情况
            retained_and_paid = (retained & (df[payment_col] > 0)).sum()
            retained_and_paid_rate = retained_and_paid / len(df) * 100
            
            print(f"{i:3d} | {retained_count:10,} | {retention_rate:8.2f} | {retained_and_paid:10,} | {retained_and_paid_rate:8.2f}")
    
    # 7. 付费用户付费金额分布详情
    print("\n" + "="*60)
    print("付费用户付费金额分布详情")
    print("="*60)
    
    paid_amounts = total_payment[paid_users]
    
    # 按金额区间统计
    amount_ranges = [
        (0, 10, "0-10"),
        (10, 50, "10-50"),
        (50, 100, "50-100"),
        (100, 500, "100-500"),
        (500, 1000, "500-1000"),
        (1000, float('inf'), "1000+")
    ]
    
    print("付费金额区间分布:")
    print("区间      | 用户数   | 占比(%) | 总金额    | 平均金额")
    print("-" * 50)
    
    for min_val, max_val, label in amount_ranges:
        if max_val == float('inf'):
            mask = (paid_amounts >= min_val)
        else:
            mask = (paid_amounts >= min_val) & (paid_amounts < max_val)
        
        count = mask.sum()
        percentage = count / len(paid_amounts) * 100
        total_amount = paid_amounts[mask].sum()
        avg_amount = paid_amounts[mask].mean() if count > 0 else 0
        
        print(f"{label:8} | {count:7,} | {percentage:6.2f} | {total_amount:8,.2f} | {avg_amount:8.2f}")
    
    # 8. 保存详细统计结果
    print("\n" + "="*60)
    print("保存分析结果")
    print("="*60)
    
    # 创建详细的分析结果
    analysis_results = {
        "总体统计": {
            "总用户数": int(len(df)),
            "付费用户数": int(paid_users.sum()),
            "付费率": float(paid_users.mean() * 100),
            "总体付费金额": float(total_payment.sum()),
            "付费用户平均付费": float(total_payment[paid_users].mean()),
            "付费用户付费中位数": float(total_payment[paid_users].median()),
            "付费用户付费标准差": float(total_payment[paid_users].std())
        },
        "各天付费统计": {},
        "付费金额分布": {
            "percentiles": {f"{p}%": float(total_payment[paid_users].quantile(p/100)) for p in [25, 50, 75, 90, 95, 99]}
        }
    }
    
    # 添加各天统计
    for i in range(1, 31):
        col = f'd{i}_payment'
        if col in df.columns:
            day_payment = df[col]
            paid_users_count = (day_payment > 0).sum()
            paid_rate = (day_payment > 0).mean() * 100
            total_amount = day_payment.sum()
            paid_amounts = day_payment[day_payment > 0]
            
            analysis_results["各天付费统计"][f"Day_{i}"] = {
                "付费用户数": int(paid_users_count),
                "付费率": float(paid_rate),
                "总金额": float(total_amount),
                "平均付费": float(paid_amounts.mean()) if len(paid_amounts) > 0 else 0.0,
                "付费中位数": float(paid_amounts.median()) if len(paid_amounts) > 0 else 0.0,
                "付费标准差": float(paid_amounts.std()) if len(paid_amounts) > 0 else 0.0
            }
    
    # 保存到JSON文件
    with open('data/payment_users_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, ensure_ascii=False, indent=2)
    
    print("分析结果已保存到 data/payment_users_analysis.json")
    
    return analysis_results

if __name__ == "__main__":
    results = analyze_payment_users() 