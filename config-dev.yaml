# 环境配置
env: "dev"

# 数据配置
path:
  feature_data_path: "base_data.pt"

  user_stats_folder: "/wkspace/sunyi/LTV_prediction/pipeline_test_data/user_data"
  game_stats_folder: "/wkspace/sunyi/LTV_prediction/pipeline_test_data/game_data"
  campaign_relation_path: "/wkspace/sunyi/LTV_prediction/pipeline_test_data/user_data"

  user_stats_cache: "user_stats.pkl"
  game_stats_cache: "game_stats.pkl"
  campaign_relation_cache: "campaign_info.pkl"

  whether_payment_model_path: "pth_model/whether_payment_model.pth"
  payment_buckets_model_path: "pth_model/payment_buckets_model.pth"
  payment_regression_model_path: "pth_model/payment_regression_model.pth"
  adp_model_path: "pth_model/any_day_prediction_model.pth"
  log_path: "logs/user_ltv_prediction.log"

  # # 数据中转服务
  # data_server_private_key: "temp_key"
  # wget_behavior_path: "/wkspace/3D_buyu_data/behavior_data"
  # wget_pay_path: "/wkspace/3D_buyu_data/pay_data"

  labelencoder_path: "data/label_encoder_proj_main_channel.json"
  device_price_path: "data/device2price.csv"

# 流水线配置
pipelines:
  # - name: "cbm_v20250416"
  #   class_path: "pipelines.tree_inference.PipelineTreeInference"
  #   log_file_path: "logs/cbm_v20250416.log"
  #   model_metadata_filepath: "pth_model/catboost_20250416-145641_metadata.json"
  #   model_filepath: "pth_model/catboost_20250416-145641.cbm"
  
  # - name: "v2_origin_pipline"
  #   class_path: "pipelines.mdme_inference.Pipelinev2"
  #   log_file_path: "logs/v2_origin_pipline.log"
  
  # - name: "lgb_v202504"
  #   class_path: "pipelines.lgb_inference.PipelineLGBInference"
  #   log_file_path: "logs/lgb_v202504.log"
  #   model_metadata_filepath: "pth_model/lightgbm_20250428-023220_metadata.json"
  #   model_filepath: "pth_model/lightgbm_20250428-023220.cbm"

  - name: "adp_v202505"
    class_path: "pipelines.adp_inference.PipelineADPInference"
    log_file_path: "logs/adp_v202505.log"
    model_filepath: "pth_model/any_day_prediction_model.pth"
    normalize_dict: "data/normalize_dict.json"
    encoding_map: "data/encoding_map.json"

# MySQL配置
mysql:
  host: "**************"
  port: 6011
  user: "root"
  password: "Ao1JvHBIiiwkSYq5"
  database: "ltv_prediction_fish_3d"

# # 数据中转服务配置
# data_server:
#   host: "*************"
#   port: 6001
#   user: "root"

# impala代理配置
impala:
  proxy_host: "**********"
  proxy_port: 5006
  user: "koi_user"
