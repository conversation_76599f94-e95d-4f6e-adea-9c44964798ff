#!/usr/bin/env python3
"""
完整的CSV数据处理和训练工作流程
模仿generate_any_day和train_any_day_prediction的代码结构
"""

import os
import sys
import argparse
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

import pandas as pd
import numpy as np
import torch
import pytorch_lightning as pl
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.callbacks import EarlyStopping, ModelCheckpoint

from dataset.generate_from_csv import process_csv_data
from dataset.light_data_any_day_prediction import AnyDayDataModule
from model.torch_model_any_day_prediction import AnyDayModel


def setup_environment():
    """设置环境"""
    print("=" * 60)
    print("CSV Data Processing and Training Pipeline")
    print("=" * 60)
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        print(f"CUDA is available. Found {torch.cuda.device_count()} device(s)")
        for i in range(torch.cuda.device_count()):
            print(f"  Device {i}: {torch.cuda.get_device_name(i)}")
    else:
        print("CUDA is not available. Using CPU")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    pl.seed_everything(42)
    
    print()


def find_csv_file():
    """查找CSV文件"""
    csv_files = [
        "csv_data/subtask_2624_20250715105822.csv",
        "csv_data/subtask_2625_20250716120249.csv"
    ]
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"Found CSV file: {csv_file}")
            return csv_file
    
    print("No valid CSV files found in csv_data directory")
    return None


def process_data_step(csv_file: str, output_dir: str = "../data"):
    """步骤1: 数据处理"""
    print("\n" + "=" * 40)
    print("STEP 1: Data Processing")
    print("=" * 40)
    
    try:
        print(f"Processing CSV file: {csv_file}")
        merged_df = process_csv_data(csv_file, output_dir, new_data=True)
        
        if merged_df is not None and len(merged_df) > 0:
            print(f"✓ Data processing completed successfully!")
            print(f"  Final data shape: {merged_df.shape}")
            print(f"  Output directory: {output_dir}")
            return True
        else:
            print("✗ Data processing failed: Empty dataframe")
            return False
            
    except Exception as e:
        print(f"✗ Data processing failed: {e}")
        return False


def setup_data_module_step(output_dir: str = "../data", batch_size: int = 2048, num_workers: int = 6):
    """步骤2: 设置数据模块"""
    print("\n" + "=" * 40)
    print("STEP 2: Data Module Setup")
    print("=" * 40)
    
    try:
        pickle_path = os.path.join(output_dir, 'any_day_data.pkl')
        
        if not os.path.exists(pickle_path):
            print(f"✗ Pickle file not found: {pickle_path}")
            return None
        
        print(f"Loading data from: {pickle_path}")
        dm = AnyDayDataModule(
            pickle_path=pickle_path,
            batch_size=batch_size,
            num_workers=num_workers,
        )
        
        dm.setup()
        print(f"✓ Data module setup completed!")
        print(f"  Training samples: {len(dm.train_set)}")
        print(f"  Validation samples: {len(dm.val_set)}")
        print(f"  Payment cumsum mean shape: {len(dm.payment_cumsum_mean)}")
        
        return dm
        
    except Exception as e:
        print(f"✗ Data module setup failed: {e}")
        return None


def create_model_step(dm, hidden_dim: int = 64, learning_rate: float = 1e-5, 
                     weight_decay: float = 1e-3, use_mse: bool = False, fixed_sigma: float = 1.0):
    """步骤3: 创建模型"""
    print("\n" + "=" * 40)
    print("STEP 3: Model Creation")
    print("=" * 40)
    
    try:
        model = AnyDayModel(
            hidden_dim=hidden_dim,
            payment_cumsum_mean=dm.payment_cumsum_mean,
            weight_decay=weight_decay,
            use_mse=use_mse,
            fixed_sigma=fixed_sigma,
            lr=learning_rate
        )
        
        print(f"✓ Model created successfully!")
        print(f"  Hidden dimension: {hidden_dim}")
        print(f"  Learning rate: {learning_rate}")
        print(f"  Weight decay: {weight_decay}")
        print(f"  Use MSE: {use_mse}")
        print(f"  Fixed sigma: {fixed_sigma}")
        
        return model
        
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return None


def setup_trainer_step(model_save_dir: str = "../pth_model", max_epochs: int = 500, devices: list = [0]):
    """步骤4: 设置训练器"""
    print("\n" + "=" * 40)
    print("STEP 4: Trainer Setup")
    print("=" * 40)
    
    try:
        # 创建目录
        log_dir = os.path.join(os.path.dirname(__file__), '../logs/')
        os.makedirs(log_dir, exist_ok=True)
        os.makedirs(model_save_dir, exist_ok=True)
        
        # 设置日志记录器
        logger = TensorBoardLogger(
            save_dir=log_dir,
            name='adp_csv',
            version=f"v1-{datetime.now().strftime('%m%d-%H%M')}",
            default_hp_metric=False,
        )
        
        # 设置回调函数
        callbacks = []
        
        # 早停回调
        early_stop_callback = EarlyStopping(
            monitor="val/total_loss",
            mode="min",
            patience=15,
            verbose=True
        )
        callbacks.append(early_stop_callback)
        
        # 模型检查点回调
        checkpoint_callback = ModelCheckpoint(
            dirpath=model_save_dir,
            filename='any_day_prediction_model_csv_{epoch:02d}_{val_total_loss:.4f}',
            monitor='val/total_loss',
            mode='min',
            save_top_k=3,
            save_last=True
        )
        callbacks.append(checkpoint_callback)
        
        # 创建训练器
        trainer = pl.Trainer(
            max_epochs=max_epochs,
            devices=devices,
            accelerator="gpu" if torch.cuda.is_available() else "cpu",
            logger=logger,
            callbacks=callbacks,
            log_every_n_steps=1,
            gradient_clip_val=0.1,
            gradient_clip_algorithm="norm",
        )
        
        print(f"✓ Trainer setup completed!")
        print(f"  Max epochs: {max_epochs}")
        print(f"  Devices: {devices}")
        print(f"  Log directory: {log_dir}")
        print(f"  Model save directory: {model_save_dir}")
        
        return trainer, checkpoint_callback
        
    except Exception as e:
        print(f"✗ Trainer setup failed: {e}")
        return None, None


def training_step(model, dm, trainer, checkpoint_callback, model_save_dir: str = "../pth_model"):
    """步骤5: 训练模型"""
    print("\n" + "=" * 40)
    print("STEP 5: Model Training")
    print("=" * 40)
    
    try:
        print("Starting training...")
        trainer.fit(model, datamodule=dm)
        
        print(f"✓ Training completed successfully!")
        
        # 保存最终模型
        final_model_path = os.path.join(model_save_dir, 'any_day_prediction_model_csv_final.pth')
        torch.save(model.state_dict(), final_model_path)
        print(f"  Final model saved to: {final_model_path}")
        
        # 打印最佳模型路径
        if checkpoint_callback.best_model_path:
            print(f"  Best model saved to: {checkpoint_callback.best_model_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ Training failed: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CSV Data Processing and Training Pipeline')
    parser.add_argument('--csv_file', type=str, help='Path to CSV file')
    parser.add_argument('--output_dir', type=str, default='../data', help='Output directory for processed data')
    parser.add_argument('--model_save_dir', type=str, default='../pth_model', help='Directory to save models')
    parser.add_argument('--batch_size', type=int, default=2048, help='Batch size for training')
    parser.add_argument('--num_workers', type=int, default=6, help='Number of workers for data loading')
    parser.add_argument('--max_epochs', type=int, default=500, help='Maximum number of training epochs')
    parser.add_argument('--devices', type=int, nargs='+', default=[0], help='GPU devices to use')
    parser.add_argument('--learning_rate', type=float, default=1e-5, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-3, help='Weight decay')
    parser.add_argument('--hidden_dim', type=int, default=64, help='Hidden dimension')
    parser.add_argument('--use_mse', action='store_true', help='Use MSE loss')
    parser.add_argument('--fixed_sigma', type=float, default=1.0, help='Fixed sigma value')
    
    args = parser.parse_args()
    
    # 设置环境
    setup_environment()
    
    # 查找CSV文件
    csv_file = args.csv_file if args.csv_file else find_csv_file()
    if not csv_file:
        return
    
    # 步骤1: 数据处理
    if not process_data_step(csv_file, args.output_dir):
        print("Data processing failed. Exiting.")
        return
    
    # 步骤2: 设置数据模块
    dm = setup_data_module_step(args.output_dir, args.batch_size, args.num_workers)
    if dm is None:
        print("Data module setup failed. Exiting.")
        return
    
    # 步骤3: 创建模型
    model = create_model_step(dm, args.hidden_dim, args.learning_rate, 
                             args.weight_decay, args.use_mse, args.fixed_sigma)
    if model is None:
        print("Model creation failed. Exiting.")
        return
    
    # 步骤4: 设置训练器
    trainer, checkpoint_callback = setup_trainer_step(args.model_save_dir, args.max_epochs, args.devices)
    if trainer is None:
        print("Trainer setup failed. Exiting.")
        return
    
    # 步骤5: 训练模型
    if not training_step(model, dm, trainer, checkpoint_callback, args.model_save_dir):
        print("Training failed. Exiting.")
        return
    
    print("\n" + "=" * 60)
    print("✓ All steps completed successfully!")
    print("=" * 60)


if __name__ == "__main__":
    main() 