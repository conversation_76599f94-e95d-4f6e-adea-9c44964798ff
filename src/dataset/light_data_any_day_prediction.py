import numpy as np
import pandas as pd
from tqdm import tqdm
import json
import os
from pprint import pprint
import random
import pickle
from deprecated import deprecated
from datetime import datetime

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset, random_split

import pytorch_lightning as pl
from sklearn.model_selection import train_test_split

    
class AnyDayDataModule(pl.LightningDataModule):
    def __init__(self, pickle_path, batch_size=4096, num_workers=4, max_seq_len=14):
        super().__init__()
        self.pickle_path = pickle_path
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.max_seq_len = max_seq_len
        
        self.train_data = None
        self.val_data = None
        self.payment_cumsum_mean = None


    def setup(self, stage=None):
        full_dataset = PreprocessedDatasetV2(self.pickle_path, max_seq_len=14)

        total_size = len(full_dataset)
        val_size = int(total_size * 0.2)
        train_size = total_size - val_size

        self.train_set, self.val_set = random_split(
            full_dataset, [train_size, val_size],
            generator=torch.Generator().manual_seed(42)
        )
        self.payment_cumsum_mean = full_dataset.payment_cumsum_mean
        
    def train_dataloader(self):
        return DataLoader(self.train_set, batch_size=self.batch_size, pin_memory=True, num_workers=self.num_workers, shuffle=True)

    def val_dataloader(self):
        return DataLoader(self.val_set, batch_size=self.batch_size, pin_memory=True, num_workers=self.num_workers)
    
    def test_dataloader(self):
        raise NotImplementedError
    
    def predict_dataloader(self):
        raise NotImplementedError
    

    
class PreprocessedDatasetV2(Dataset):
    def __init__(self, pickle_path, max_seq_len=14):
        super().__init__()
        self.max_seq_len = max_seq_len
        df_raw = pd.read_pickle(pickle_path)
        merged_df_raw = df_raw.copy()

        drop_cols = ['sdk_yidun_device_id', 'attribution_day', 'attribution_day_a', 
                    'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']  + [f'd{i}_retention' for i in range(1, 31)]
        payment_cols = [f'd{i}_payment' for i in range(1, 31)]
        existing_payment_cols = list(set(payment_cols) & set(merged_df_raw.columns))
        
        negative_mask = (merged_df_raw[existing_payment_cols].apply(pd.to_numeric, errors='coerce') < 0).any(axis=1)
        if negative_mask.any():
            merged_df_raw = merged_df_raw.loc[~negative_mask]
            merged_df_raw.reset_index(drop=True, inplace=True)
        
        self.device_ids = merged_df_raw['sdk_yidun_device_id'].values
        self.payment_data = merged_df_raw[existing_payment_cols][payment_cols].astype(float).values
        merged_df = merged_df_raw.drop(columns=drop_cols + payment_cols, errors='ignore')
        
        behav_cols = [col for col in merged_df.columns if col.startswith('behav')]
        campaign_cols = [col for col in merged_df.columns if not col.startswith('behav')]
        self.behavior_data = np.array([np.stack(merged_df[col].values) for col in behav_cols]).swapaxes(0, 1)
        self.campaign_data = np.array([merged_df[col].values for col in campaign_cols]).swapaxes(0, 1)
        self.payment_cumsum_mean = self.payment_data.cumsum(axis=1).mean(axis=0)
        

    def __len__(self):
        return len(self.device_ids)
    

    def __getitem__(self, idx):
        payment_feat = self.payment_data[idx]
        behavior_feat = self.behavior_data[idx]
        campagin_feat = self.campaign_data[idx]
        
        # to tensor and do transpose
        behavior_feat = torch.tensor(behavior_feat.T, dtype=torch.float32)[:self.max_seq_len, :]  # (L, D_b)
        campagin_feat = torch.tensor(campagin_feat, dtype=torch.long)  # (D_c, )
        payment_feat = torch.tensor(payment_feat, dtype=torch.float32)[:self.max_seq_len] # (L, )
        have_paid = payment_feat.cumsum(dim=0) != 0
        
        behavior_feat = torch.cat([behavior_feat, payment_feat.unsqueeze(1)], dim=1)
        item = {
            'device_id': self.device_ids[idx],
            'behavior': behavior_feat,
            'campaign': campagin_feat,
            'payment': payment_feat,
            'have_paid': have_paid,
        }
        return item
    
    
if __name__ == "__main__":
    dm = AnyDayDataModule(
        pickle_path = os.path.join(os.path.dirname(__file__), '../../data/any_day_data.pkl'),
        batch_size=1, 
    )
    dm.setup()
    train_loader = dm.train_dataloader()
    for batch in train_loader:
        for k, v in batch.items():
            print(k)
            print(v)
            if type(v) == torch.Tensor:
                print(v.shape)
        break
    
    # with open(os.path.join(os.path.dirname(__file__), "../../data/encoding_map.json"), "r") as f:
    #     encoder_dict = json.load(f)
    #     pprint(encoder_dict)
    #     # guiyin_input_dims = [
        #     max(mapping.values()) + 1
        #     for field, mapping in encoder_dict.items()
        # ]

