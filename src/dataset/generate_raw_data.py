import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

import subprocess
import os
import logging
from datetime import datetime
import traceback
import paramiko
from scp import SCPClient
from config import config
from utils.mysql import MySQLConnector


def load_commands(ds: str, logger: logging.Logger = None) -> list: 
    """从数据库获取待处理的wget命令"""
    pending_commands, success_commands, failed_commands = [], [], []
    try:
        conn = MySQLConnector(user=config['mysql']['user'], 
                              password=config['mysql']['password'], 
                              host=config['mysql']['host'], 
                              port=config['mysql']['port'], 
                              db=config['mysql']['database'])
        conn.connect()
        query = """
            SELECT id, ds, behavior_wget, pay_wget
            FROM wget_command_history 
            WHERE ds = '{}' and behavior_wget is not null and pay_wget is not null
        """.format(ds)
        result = conn.execute_select(query)
        for row in result:
            if row['status'] == 'pending':
                pending_commands.append(row)
            elif row['status'] == 'success':
                success_commands.append(row)
            else:
                failed_commands.append(row)
    except Exception as e:
        if logger is not None:
            logger.error("load wget commands failed")
            logger.error(f'{e}, {traceback.format_exc()}')
        else:
            print("load wget commands failed")
            print(f'{e}, {traceback.format_exc()}')
    return pending_commands, success_commands, failed_commands


def transfer_data_via_ssh(remote_cmd: str, local_save_path: str):
    # 创建SSH客户端
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        private_key = paramiko.RSAKey.from_private_key_file(config['path']['data_server_private_key'])
        ssh.connect(hostname=config['data_server']['host'], 
                    port=config['data_server']['port'], 
                    username=config['data_server']['user'], pkey=private_key)
        
        # 在数据服务器执行wget命令
        stdin, stdout, stderr = ssh.exec_command(remote_cmd)
        exit_status = stdout.channel.recv_exit_status()
        print(stdout.read().decode())
        
        if exit_status != 0:
            # raise Exception(f"wget failed with error: {stderr.read().decode()}")
            return 1
        # 通过SCP传输文件到计算服务器
        if '-O "' in remote_cmd:
            filename = remote_cmd.split('-O "')[1].split('"')[0]
            
        with SCPClient(ssh.get_transport()) as scp:
            scp.get(filename, local_save_path)
        
    finally:
        ssh.close()
    return 0
            

def execute_wget(pending_commands: list, dest_path: str, logger: logging.Logger = None):
    """执行wget命令并处理结果"""
    
    results = []
    for _id, ds, behavior_wget, pay_wget in pending_commands:
        logger.info(f"执行命令ID {_id}: {behavior_wget}")
        try:
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            os.chdir(os.path.dirname(dest_path))
            behavior_dest_path = os.path.join(dest_path, 'behavior')
            pay_dest_path = os.path.join(dest_path, 'pay')
            flag = transfer_data_via_ssh(behavior_wget, behavior_dest_path) 
            flag |= transfer_data_via_ssh(pay_wget, pay_dest_path)
            
            file_size_a = os.path.getsize(behavior_dest_path) if os.path.exists(behavior_dest_path) else 0
            file_size_b = os.path.getsize(pay_dest_path) if os.path.exists(pay_dest_path) else 0
            file_size = file_size_a + file_size_b
            status = 'success' if flag == 0 else 'failed'
        except Exception as e:
            file_size = 0
            status = 'failed'
            
        results.append({
                'id': _id,
                'ds': ds,
                'file_size': file_size,
                'dest_path': dest_path,
                'last_executed': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status': status
            })
        
    conn = MySQLConnector(user=config['mysql']['user'], 
                        password=config['mysql']['password'], 
                        host=config['mysql']['host'], 
                        port=config['mysql']['port'], 
                        db=config['mysql']['database'])
    conn.connect()
    conn.execute_update(table_name='wget_command_history', key_columns=['id'], 
                        update_columns=['status', 'file_size', 'last_executed', 'destination_path'], 
                        key_values=[(item['id'], ) for item in results], 
                        update_values=[(item['status'], item['file_size'], item['last_executed'], item['destination_path']) for item in results])


if __name__ == "__main__":
    # load_commands()

    # 配置信息
    LOCAL_SAVE_PATH = "./data_file"
    REMOTE_CMD = 'wget "http://dataset-inner.tuyoo.com/hermes/job_60_10010/task_60_10010_20250331102701.tar.gz?response-content-disposition=attachment%3B%20filename%3D%22task_60_10010_20250331102701.tar.gz%22&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=hermes%2F20250401%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250401T140902Z&X-Amz-Expires=120&X-Amz-SignedHeaders=host&X-Amz-Signature=eb77c65c05fae4c33f0c398b91f4e9d6b939fbd70df36b957805d7911ca28236" -O "task_60_10010_20250331102701.tar.gz"'
    # REMOTE_CMD = 'ls -lha'
            
    transfer_data_via_ssh()