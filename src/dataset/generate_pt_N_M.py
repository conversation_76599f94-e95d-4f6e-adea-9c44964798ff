import zipfile
import pandas as pd
from io import BytesIO
from tqdm import tqdm
import numpy as np
import os
import torch
import numpy as np

def create_complete_sequences(df1, df2):
    
    date_ranges = df1[['sdk_yidun_device_id', 'day']]
    date_ranges.columns = ['sdk_yidun_device_id', 'min_day']
    date_ranges = date_ranges[date_ranges['sdk_yidun_device_id'].isin(df2['sdk_yidun_device_id'])].reset_index(drop=True)
    date_ranges = date_ranges.drop_duplicates().reset_index(drop=True)
    # print('stage1')
    # print(date_ranges.head())
    
    date_ranges = date_ranges.copy()
    # 计算最大日期（最小日期+7天）
    date_ranges['max_day'] = date_ranges['min_day'] + pd.Timedelta(days=6)
    
    # 创建日期范围索引
    all_dates = pd.DataFrame({
        'sdk_yidun_device_id': date_ranges['sdk_yidun_device_id'].repeat(7).reset_index(drop=True), 
        'min_day': date_ranges['min_day'].repeat(7).reset_index(drop=True),
        'day': np.concatenate([
            pd.date_range(start=min_day, end=max_day, freq='D')
            for min_day, max_day in zip(date_ranges['min_day'], date_ranges['max_day'])
        ])
    })
    
    # print('stage2')
    # print(all_dates.head())
    
    features = ['min_delta', 'max_delta', 'final_delta', 'game_time_in_minutes', 'total_catch', 'fish_gun_fire_sum_count', 'gun_level_up_consume_count', 
                'recharge_count', 'fish_table_enter_count', 'skill_use_count', 'total_recharge_amount']
    complete_df = pd.merge(all_dates, df2, on=['sdk_yidun_device_id', 'day'], how='left')
    
    for feature in features:
        complete_df[feature] = complete_df[feature].fillna(0)
    
    # print('stage3')
    # print(complete_df)

    result = complete_df.sort_values(['sdk_yidun_device_id', 'min_day']).groupby(['sdk_yidun_device_id', 'min_day'])[features].agg(list)
    return result

def generate_pt_file(N, M):
    final_df_game = pd.read_parquet("data/final_df_game.parquet", engine="pyarrow")
    final_df_game['day'] = pd.to_datetime(final_df_game['day'])
    
    user_data_folder = '/wkspace/3D_buyu_data/campaign_data/raw_data/final/'
    
    print('Reading User Information CSVs......')
    all_user_dfs = []
    for user_filename in tqdm(os.listdir(user_data_folder)):
        if user_filename.endswith(".csv"):
            user_file_path = os.path.join(user_data_folder, user_filename)
            df_user = pd.read_csv(user_file_path)
            all_user_dfs.append(df_user)

    final_df_user = pd.concat(all_user_dfs, ignore_index=True)
    final_df_user['day'] = pd.to_datetime(final_df_user['day'])
    
    columns_to_keep = [
        'sdk_yidun_device_id',
        'day',
        'proj_main_channel',
        'sdk_device_name',
        'adtrace_reattributed',
        'pay_sum'
    ] + [f'stay{i}' for i in range(1, N + 1)] + [f'pay{i}' for i in range(1, N + 1)]

    final_df_user = final_df_user.drop_duplicates().reset_index(drop=True)
    final_df_game = final_df_game.drop_duplicates().reset_index(drop=True)
    print('Duplicated Data Deleted!')
    
    mid_folder = '/wkspace/3D_buyu_data/campaign_data/raw_data/fish_merged.zip'
    
    print('Reading Mid CSVs......')
    dfs_mid = []
    with zipfile.ZipFile(mid_folder, 'r') as zip_file:
        mid_list = zip_file.namelist()
        
        for mid_file in tqdm(mid_list):
            if mid_file.endswith('.csv'):
                with zip_file.open(mid_file) as f:
                    df_mid = pd.read_csv(BytesIO(f.read()))
                    dfs_mid.append(df_mid)  # 将DataFrame加入列表
    if dfs_mid:
        final_df_mid = pd.concat(dfs_mid, ignore_index=True)
        # print(f"总行数: {len(final_df_game)}")
    else:
        print("没有找到CSV文件")
        
    final_df_mid = final_df_mid[['sdk_yidun_device_id', 'adtrace_did']]
    final_df_user_merge = final_df_user.merge(final_df_mid, on = 'adtrace_did', how = 'inner')
    
    pay_columns = [f'pay{i}' for i in range(N + 1, M + 1)]
    # print(pay_columns)
    final_df_user_merge["pay_sum"] = final_df_user_merge[pay_columns].sum(axis=1)
    
    print('Data Processing.....')
    delta_list = create_complete_sequences(final_df_user_merge, final_df_game)
    df1 = pd.merge(final_df_user_merge[columns_to_keep], delta_list.reset_index(), 
                left_on = ['sdk_yidun_device_id', 'day'], right_on = ['sdk_yidun_device_id', 'min_day'], how='right')
    
    df1 = df1.drop_duplicates(subset=['sdk_yidun_device_id', 'day']).reset_index(drop=True)
    
    device_price_path = '/wkspace/3D_buyu_data/campaign_data/device_price.csv'
    print('Reading Device Price.....')
    device_price_df = pd.read_csv(device_price_path)
    device_price_df = device_price_df.drop_duplicates('sdk_device_name').reset_index(drop=True)
    
    df1['price'] = df1['sdk_device_name'].map(device_price_df.set_index('sdk_device_name')['price'])
    df1 = df1.fillna(0)
    
    processed_data = [(
        
            np.nan_to_num(np.array([
            np.nan_to_num(row['stay1':f'stay{N}'].tolist()),  
            np.nan_to_num(np.array(row['pay1':f'pay{N}'].tolist()) / 5000),

            (np.log10(np.nan_to_num(np.array(row['min_delta'][0:N])) + 1) / 10).tolist(),
            (np.log10(np.nan_to_num(np.array(row['max_delta'][0:N])) + 1) / 10).tolist(),
            (np.log10(np.nan_to_num(np.array(row['final_delta'][0:N])) + 1) / 10).tolist(),
            (np.log10(np.nan_to_num(np.array(row['fish_gun_fire_sum_count'][0:N])) + 1) / 10).tolist(),

            (np.log10(np.nan_to_num(np.array(row['gun_level_up_consume_count'][0:N]), nan=0) + 1) / 10).tolist(),
            (np.log10(np.nan_to_num(np.array(row['recharge_count'][0:N]), nan=0) + 1) / 10).tolist(),
            (np.log10(np.nan_to_num(np.array(row['fish_table_enter_count'][0:N]), nan=0) + 1) / 10).tolist(),
            (np.log10(np.nan_to_num(np.array(row['skill_use_count'][0:N]), nan=0) + 1) / 10).tolist(),
            (np.log10(np.nan_to_num(np.array(row['total_recharge_amount'][0:N]), nan=0) + 1) / 10).tolist(),
            (np.log10(np.nan_to_num(np.array(row['game_time_in_minutes'][0:N]), nan=0) + 1) / 10).tolist(),
            (np.log10(np.nan_to_num(np.array(row['total_catch'][0:N]), nan=0) + 1) / 10).tolist(),
        ])),

            np.nan_to_num(np.array([
                float(row['adtrace_reattributed']) if not pd.isna(row['adtrace_reattributed']) else 0.0,
                float(row['price']/1e4) if not pd.isna(row['price']) else 0.0,
            ])),
            float(row['pay_sum']) if not pd.isna(row['pay_sum']) else 0.0,
            str(row['sdk_yidun_device_id']),
            str(row['day'])
        )
        for _, row in df1.iterrows()
    ]



    print('Single Training Data:')
    print(processed_data[0])    
    
    torch.save(processed_data, f"data/base_data_{N}_{M}.pt")

if __name__ == "__main__":
    generate_pt_file(N = 3, M = 7)
    