import pandas as pd
import numpy as np
from tqdm import tqdm
import os
from typing import List, Dict, Any
from datetime import datetime, timed<PERSON>ta


def extract_behavior_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    从CSV数据中提取行为特征
    
    Args:
        df: 原始CSV数据
        
    Returns:
        pd.DataFrame: 处理后的行为数据
    """
    print("Extracting behavior features...")
    
    # 定义行为特征列
    behavior_columns = [
        'fish_gun_fire_sum_count', 'gun_level_up_consume_count', 'recharge_count',
        'fish_table_enter_count', 'skill_use_count', 'bkrpt_count', 
        'total_recharge_amount', 'login_count', 'shop_center_enter_count',
        'achievement_reward_count', 'have_checkin_reward', 
        'startup_quest_finish_game_count', 'click_120215_count', 'click_120214_count',
        'click_120093_count', 'click_120092_count', 'resource_total_down_count',
        'resource_down_count', 'activity_midnight', 'activity_morning',
        'activity_afternoon', 'activity_night', 'min_delta', 'max_delta',
        'final_delta', 'game_time_in_minutes', 'total_catch'
    ]
    
    # 检查哪些列存在
    existing_behavior_cols = [col for col in behavior_columns if col in df.columns]
    print(f"Found {len(existing_behavior_cols)} behavior columns: {existing_behavior_cols}")
    
    # 如果没有行为数据，返回空DataFrame
    if not existing_behavior_cols:
        print("No behavior columns found in CSV data")
        return pd.DataFrame(columns=['sdk_yidun_device_id', 'attribution_day'])
    
    # 提取行为数据
    behavior_df = df[['sdk_yidun_device_id', 'attribution_day'] + existing_behavior_cols].copy()
    
    # 填充缺失值
    for col in existing_behavior_cols:
        behavior_df[col] = behavior_df[col].fillna(0)
    
    # 转换数据类型
    for col in existing_behavior_cols:
        try:
            behavior_df[col] = pd.to_numeric(behavior_df[col], errors='coerce').fillna(0)
        except:
            behavior_df[col] = 0
    
    print(f"Behavior data shape: {behavior_df.shape}")
    return behavior_df


def create_behavior_sequences(behavior_df: pd.DataFrame, max_days: int = 14) -> pd.DataFrame:
    """
    为每个用户创建行为序列
    
    Args:
        behavior_df: 行为数据
        max_days: 最大天数
        
    Returns:
        pd.DataFrame: 包含行为序列的数据
    """
    print("Creating behavior sequences...")
    
    # 确保attribution_day是datetime类型
    behavior_df['attribution_day'] = pd.to_datetime(behavior_df['attribution_day'])
    
    # 为每个用户创建14天的序列
    result_data = []
    
    for device_id in tqdm(behavior_df['sdk_yidun_device_id'].unique(), desc="Processing users"):
        user_data = behavior_df[behavior_df['sdk_yidun_device_id'] == device_id]
        
        if len(user_data) == 0:
            continue
            
        attribution_day = user_data['attribution_day'].iloc[0]
        
        # 创建14天的序列
        behavior_sequence = []
        for day in range(max_days):
            day_date = attribution_day + timedelta(days=day)
            day_data = user_data[user_data['attribution_day'] == day_date]
            
            if len(day_data) > 0:
                # 取第一行数据（假设每天只有一条记录）
                day_features = day_data.iloc[0].drop(['sdk_yidun_device_id', 'attribution_day']).tolist()
            else:
                # 如果当天没有数据，用0填充
                feature_cols = [col for col in user_data.columns if col not in ['sdk_yidun_device_id', 'attribution_day']]
                day_features = [0] * len(feature_cols)
            
            behavior_sequence.append(day_features)
        
        result_data.append({
            'sdk_yidun_device_id': device_id,
            'attribution_day': attribution_day,
            'behavior_sequence': behavior_sequence
        })
    
    result_df = pd.DataFrame(result_data)
    print(f"Created behavior sequences for {len(result_df)} users")
    return result_df


def process_csv_for_training(csv_file_path: str, output_dir: str = "../data") -> pd.DataFrame:
    """
    处理CSV文件用于训练
    
    Args:
        csv_file_path: CSV文件路径
        output_dir: 输出目录
        
    Returns:
        pd.DataFrame: 处理后的数据
    """
    print(f"Processing CSV file: {csv_file_path}")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(csv_file_path)
        print(f"Loaded {len(df)} rows from CSV")
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return pd.DataFrame()
    
    # 数据清洗
    print("Cleaning data...")
    
    # 删除重复用户
    duplicates_sdk = df['sdk_yidun_device_id'][df['sdk_yidun_device_id'].duplicated(keep=False)].unique()
    df = df[~df['sdk_yidun_device_id'].isin(duplicates_sdk)].reset_index(drop=True)
    print(f"After removing duplicates: {len(df)} rows")
    
    # 删除缺失关键字段的行
    df = df.dropna(subset=['channel_ty_adgroup_id', 'sdk_yidun_device_id', 'attribution_day'])
    df = df[df['channel_ty_adgroup_id'] != ""]
    print(f"After removing missing values: {len(df)} rows")
    
    # 提取行为特征
    behavior_df = extract_behavior_features(df)
    
    # 创建行为序列
    if len(behavior_df) > 0:
        behavior_sequences = create_behavior_sequences(behavior_df)
    else:
        behavior_sequences = pd.DataFrame(columns=['sdk_yidun_device_id', 'attribution_day', 'behavior_sequence'])
    
    # 合并归因数据和行为数据
    campaign_df = df.drop(columns=[col for col in df.columns if col in behavior_df.columns and col not in ['sdk_yidun_device_id', 'attribution_day']], errors='ignore')
    
    # 合并数据
    merged_df = campaign_df.merge(behavior_sequences, on=['sdk_yidun_device_id', 'attribution_day'], how='inner')
    
    print(f"Final merged data shape: {merged_df.shape}")
    
    # 保存处理后的数据
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, 'processed_csv_data.pkl')
        merged_df.to_pickle(output_path)
        print(f"Saved processed data to: {output_path}")
    
    return merged_df


def main():
    """主函数"""
    # 检查CSV文件
    csv_files = [
        "csv_data/subtask_2624_20250715105822.csv",
        "csv_data/subtask_2625_20250716120249.csv"
    ]
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"Processing {csv_file}...")
            try:
                processed_df = process_csv_for_training(csv_file)
                print(f"Successfully processed {csv_file}")
                print(f"Final data shape: {processed_df.shape}")
                break
            except Exception as e:
                print(f"Error processing {csv_file}: {e}")
                continue
    else:
        print("No valid CSV files found in csv_data directory")


if __name__ == "__main__":
    main() 