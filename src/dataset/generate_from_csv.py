import sys
import os
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import pickle
from datetime import datetime, timedelta
from typing import List, Tuple, Any, Dict, Type
from collections import defaultdict

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.impala_utils import add_device_price, analyze_column, get_behavior_data_list
from dataset.generate_any_day import CustomOrdinalEncoder


def load_csv_data(csv_file_path: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    从CSV文件加载数据并分离归因数据和行为数据
    
    Args:
        csv_file_path: CSV文件路径
        
    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: (campaign_df, behavior_df)
    """
    print(f"Loading CSV data from {csv_file_path}...")
    
    # 读取CSV文件
    df = pd.read_csv(csv_file_path)
    print(f"Loaded {len(df)} rows from CSV")
    
    # 分离归因数据和行为数据
    # 归因数据包含用户基本信息和归因特征
    campaign_columns = [
        'sdk_yidun_device_id', 'attribution_day', 'channel_ty_campaign_id', 
        'channel_ty_account_id', 'adtrace_organic_traffic', 'sdk_mntid', 
        'adtrace_attribution_mode', 'sdk_os_name', 'adtrace_pay_times', 
        'adtrace_click_match_count', 'sdk_fileinittime', 'sdk_devicename', 
        'sdk_virtual_channel_version', 'click_time', 'sdk_language', 
        'adtrace_orderid', 'channel_ty_adgroup_id', 'adtrace_namespace', 
        'adtrace_yidun_validate', 'adtrace_is_greylist', 'sdk_hardware_name', 
        'adtrace_assist_platform', 'adtrace_ctit', 'sdk_device_name', 
        'channel_ty_csite_id', 'sdk_carrierinfo', 'sdk_systemversion', 
        'proj_project_id', 'channel_ty_word_id', 'adtrace_act_name', 
        'adtrace_is_blacklist', 'adtrace_device_ua', 'sdk_proj_app_id', 
        'adtrace_platform', 'proj_main_channel', 'sdk_split_ua_result', 
        'sdk_timezone', 'adtrace_device_caid_main_version', 'proj_sub_channel', 
        'sdk_os_version', 'proj_client_id', 'sdk_virtual_channel', 
        'sdk_package_name', 'proj_cloud_id', 'sdk_disk', 'proj_virtual_channel', 
        'sdk_memory', 'proj_game_type', 'adtrace_reattributed', 'sdk_machine', 
        'sdk_model', 'channel_ua', 'sdk_carrier_name', 'channel_ty_creative_id', 
        'adtrace_aid', 'adtrace_main_platform', 'channel_ty_video_id', 
        'sdk_countrycode', 'attribution_day_a', 'sdk_yidun_device_id_a', 
        'adtrace_aid_a'
    ]
    
    # 行为数据包含每日的行为特征
    behavior_columns = [
        'sdk_yidun_device_id', 'attribution_day', 'day', 'fish_gun_fire_sum_count',
        'gun_level_up_consume_count', 'recharge_count', 'fish_table_enter_count',
        'skill_use_count', 'bkrpt_count', 'total_recharge_amount', 'login_count',
        'shop_center_enter_count', 'achievement_reward_count', 'have_checkin_reward',
        'startup_quest_finish_game_count', 'click_120215_count', 'click_120214_count',
        'click_120093_count', 'click_120092_count', 'resource_total_down_count',
        'resource_down_count', 'activity_midnight', 'activity_morning',
        'activity_afternoon', 'activity_night', 'min_delta', 'max_delta',
        'final_delta', 'game_time_in_minutes', 'total_catch'
    ]
    
    # 支付和留存数据
    payment_retention_columns = [f'd{i}_retention' for i in range(1, 31)] + [f'd{i}_payment' for i in range(1, 31)]
    
    # 获取实际存在的列
    existing_campaign_cols = [col for col in campaign_columns if col in df.columns]
    existing_behavior_cols = [col for col in behavior_columns if col in df.columns]
    existing_payment_cols = [col for col in payment_retention_columns if col in df.columns]
    
    # 分离数据
    campaign_df = df[existing_campaign_cols + existing_payment_cols].copy()
    behavior_df = df[existing_behavior_cols].copy() if existing_behavior_cols else None
    
    print(f"Campaign data shape: {campaign_df.shape}")
    if behavior_df is not None:
        print(f"Behavior data shape: {behavior_df.shape}")
    
    return campaign_df, behavior_df


def process_csv_data(csv_file_path: str, output_dir: str = "../data", new_data: bool = True) -> pd.DataFrame:
    """
    处理CSV数据并生成pickle文件
    
    Args:
        csv_file_path: CSV文件路径
        output_dir: 输出目录
        new_data: 是否为新数据（决定是否重新训练编码器）
        
    Returns:
        pd.DataFrame: 处理后的合并数据
    """
    print("Loading CSV Data......")
    campaign_df_raw, behavior_df_raw = load_csv_data(csv_file_path)
    
    # 数据清洗
    print("Data Cleaning......")
    campaign_df_raw = campaign_df_raw.dropna(subset=['channel_ty_adgroup_id'])
    if behavior_df_raw is not None:
        behavior_df_raw = behavior_df_raw.fillna(0)
    
    print("Drop Multi-attribute User......")
    duplicates_sdk = campaign_df_raw['sdk_yidun_device_id'][campaign_df_raw['sdk_yidun_device_id'].duplicated(keep=False)].unique()
    campaign_df_raw = campaign_df_raw[~campaign_df_raw['sdk_yidun_device_id'].isin(duplicates_sdk)].reset_index(drop=True)
    if behavior_df_raw is not None:
        behavior_df_raw = behavior_df_raw[~behavior_df_raw['sdk_yidun_device_id'].isin(duplicates_sdk)].reset_index(drop=True)
    
    print("Data Processing......")
    keys, X_cols, ignores = analyze_column(campaign_df_raw, verbose=False)
    if new_data:
        campaign_df = add_device_price(campaign_df_raw[keys + X_cols])
    else:
        campaign_df = add_device_price(campaign_df_raw)
    
    # 处理行为数据
    if behavior_df_raw is not None:
        behavior_df = get_behavior_data_list(behavior_df_raw, campaign_df)
    else:
        # 如果没有行为数据，创建一个空的DataFrame
        behavior_df = pd.DataFrame(columns=['sdk_yidun_device_id', 'attribution_day'])
    
    print("Dropping Nans......")
    campaign_df = campaign_df.dropna(subset=['channel_ty_adgroup_id'])
    campaign_df = campaign_df[campaign_df['channel_ty_adgroup_id'] != ""]
    
    print("Encoding Category Features......")
    ignore_cols = ['sdk_yidun_device_id', 'attribution_day', 'attribution_day_a', 
                   'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']
    ignore_cols += [f'd{i}_payment' for i in range(1, 31)] + [f'd{i}_retention' for i in range(1, 31)]
    
    category_cols = [col for col in campaign_df.columns if col not in ignore_cols]
    
    encoder = CustomOrdinalEncoder(columns=category_cols)
    
    if new_data:
        campaign_df = encoder.fit_transform(campaign_df)
    else:
        encoder_path = os.path.join(output_dir, 'encoding_map.json')
        if os.path.exists(encoder_path):
            encoder.load(encoder_path)
            campaign_df = encoder.transform(campaign_df, ignore_cols)
        else:
            print("Warning: encoding_map.json not found, fitting new encoder")
            campaign_df = encoder.fit_transform(campaign_df)
    
    print("Merging Two Dataframes......")
    merged_df_raw = campaign_df.merge(behavior_df, on=['sdk_yidun_device_id', 'attribution_day'], how='inner')
    merged_df_raw = merged_df_raw.sample(frac=1).reset_index(drop=True)
    
    if new_data:
        print("Saving Files......")
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存编码器
        encoder_path = os.path.join(output_dir, 'encoding_map.json')
        encoder.save(encoder_path)
        
        # 保存pickle文件
        pickle_path = os.path.join(output_dir, 'any_day_data.pkl')
        merged_df_raw.to_pickle(pickle_path)
        
        print(f"Saved encoding_map.json to {encoder_path}")
        print(f"Saved any_day_data.pkl to {pickle_path}")
    
    print("Finish!")
    return merged_df_raw


def main():
    """主函数"""
    # 检查CSV文件是否存在
    csv_files = [
        "csv_data/subtask_2624_20250715105822.csv",
        "csv_data/subtask_2625_20250716120249.csv"
    ]
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"Processing {csv_file}...")
            try:
                merged_df = process_csv_data(csv_file, new_data=True)
                print(f"Successfully processed {csv_file}")
                print(f"Final data shape: {merged_df.shape}")
                break
            except Exception as e:
                print(f"Error processing {csv_file}: {e}")
                continue
    else:
        print("No valid CSV files found in csv_data directory")


if __name__ == "__main__":
    main() 