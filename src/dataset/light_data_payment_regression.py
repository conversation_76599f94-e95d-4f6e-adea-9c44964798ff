import zipfile
import pandas as pd
from io import BytesIO
from tqdm import tqdm
import numpy as np
import os
import random
import pickle

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset, random_split

import pytorch_lightning as pl
from sklearn.model_selection import train_test_split
import numpy as np
from sklearn.preprocessing import MinMaxScaler, LabelEncoder


class PaymentRegressionDataModule(pl.LightningDataModule):
    def __init__(self, pickle_path, batch_size=4096, num_workers=4):
        super().__init__()
        self.pickle_path = pickle_path
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.label_col = 'target'
        self.label_encoders = {}
        self.scaler = MinMaxScaler()
        
        # 预定义数据分割占位符
        self.train_data = None
        self.val_data = None

    def preprocess(self):
        """每个进程执行的数据分割（训练/验证）"""
        # 加载预处理数据
        seed = 42  # 你可以换成任何固定的数
        random.seed(seed)
        
        self.df_raw = pd.read_pickle(self.pickle_path)
        # df_full = self.df_raw.copy()
        
        # df_pos = df_full[df_full['target'] > 0]
        
        # # 负样本：target == 0
        # df_neg = df_full[df_full['target'] == 0]
        
        # # 从负样本中随机采样一半正样本数量的行
        # n_neg_sample = int(len(df_pos) * 0.5)
        # df_neg_sampled = df_neg.sample(n=n_neg_sample, random_state=42)

        # # 合并正样本和采样的负样本
        # df = pd.concat([df_pos, df_neg_sampled]).sample(frac=1, random_state=42).reset_index(drop=True)

        df = self.df_raw.copy()
        
        df = df.astype({col: float for col in df.select_dtypes(include='number').columns})
        df = df.drop(columns=df.select_dtypes(include=['object']).columns)
        
        for col in df.select_dtypes(include='category').columns:
            le = LabelEncoder()
            df[col] = le.fit_transform(df[col])
            self.label_encoders[col] = le
        
        # 提取标签并移除
        y = df[self.label_col].values
        y = np.log1p(y)
        X = df.drop(columns=[self.label_col])
        
        # MinMax 归一化（对特征）
        X_scaled = self.scaler.fit_transform(X)
        self.X, self.y = X_scaled, y
        self.feature_names = X.columns.tolist()

            
    def setup(self, stage=None):
        self.preprocess()
        full_dataset = PreprocessedDataset(self.X, self.y)

        total_size = len(full_dataset)
        val_size = int(total_size * 0.2)
        train_size = total_size - val_size

        self.train_set, self.val_set = random_split(
            full_dataset, [train_size, val_size]
        )
    
    def train_dataloader(self):
        return DataLoader(self.train_set, batch_size=self.batch_size, shuffle=True)

    def val_dataloader(self):
        return DataLoader(self.val_set, batch_size=self.batch_size)
    
    def save_preprocessors(self, prefix='preprocessors'):
        with open(f'{prefix}_label_encoders.pkl', 'wb') as f:
            pickle.dump(self.label_encoders, f)
        with open(f'{prefix}_scaler.pkl', 'wb') as f:
            pickle.dump(self.scaler, f)
    
class PreprocessedDataset(Dataset):
    def __init__(self, X, y):
        self.X = torch.tensor(X, dtype=torch.float32)
        self.y = torch.tensor(y, dtype=torch.float32)

    def __len__(self):
        return len(self.X)

    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]
    
if __name__ == "__main__":
    dm = PaymentRegressionDataModule(
        pickle_path = 'data/base_data_clean.pickle',
        batch_size=4
    )
    dm.setup()

    # 保存编码器与缩放器（供预测或测试集用）
    dm.save_preprocessors()

    max_vals = None

    from tqdm import tqdm
    for x, _ in tqdm(dm.train_dataloader()):
        if max_vals is None:
            max_vals = x.max(dim=0).values
        else:
            max_vals = torch.max(max_vals, x.max(dim=0).values)

    print("Feature-wise max values across the entire training set:")
    print(max_vals)

