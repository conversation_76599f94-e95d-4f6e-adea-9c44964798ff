import numpy as np
import pandas as pd
from tqdm import tqdm
import json
import os
from pprint import pprint
import random
import pickle
from datetime import datetime

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset, random_split

import pytorch_lightning as pl
from sklearn.model_selection import train_test_split


class GenericDataset(Dataset):
    """通用数据集，适配通用CSV处理结果"""
    
    def __init__(self, pickle_path, max_seq_len=14):
        self.pickle_path = pickle_path
        self.max_seq_len = max_seq_len
        
        # 加载数据
        self.df = pd.read_pickle(pickle_path)
        print(f"Loaded data shape: {self.df.shape}")
        
        # 加载分析结果
        analysis_path = pickle_path.replace('.pkl', '_analysis.json')
        if os.path.exists(analysis_path):
            with open(analysis_path, 'r') as f:
                self.analysis = json.load(f)
        else:
            # 如果没有分析文件，尝试从同目录加载
            analysis_path = os.path.join(os.path.dirname(pickle_path), 'data_analysis.json')
            if os.path.exists(analysis_path):
                with open(analysis_path, 'r') as f:
                    self.analysis = json.load(f)
            else:
                self.analysis = self._infer_analysis()
        
        # 处理数据
        self._process_data()
        
    def _infer_analysis(self):
        """推断数据分析结果"""
        analysis = {
            'key_columns': [],
            'categorical_columns': [],
            'numerical_columns': [],
            'target_columns': [],
            'behavior_columns': []
        }
        
        for col in self.df.columns:
            if any(keyword in col.lower() for keyword in ['id', 'device', 'user']):
                analysis['key_columns'].append(col)
            elif any(keyword in col.lower() for keyword in ['payment', 'pay', 'revenue']):
                analysis['target_columns'].append(col)
            elif pd.api.types.is_numeric_dtype(self.df[col]):
                analysis['numerical_columns'].append(col)
            else:
                analysis['categorical_columns'].append(col)
        
        # 行为列 = 数值列 - 目标列 - 关键列
        analysis['behavior_columns'] = [
            col for col in analysis['numerical_columns'] 
            if col not in analysis['target_columns'] and col not in analysis['key_columns']
        ]
        
        return analysis
    
    def _process_data(self):
        """处理数据"""
        print("Processing data for training...")
        
        # 获取关键列
        self.device_col = 'sdk_yidun_device_id'
        self.attribution_col = 'attribution_day'
        
        # 获取目标列（支付列）
        self.target_columns = [col for col in self.df.columns if 'payment' in col.lower()]
        print(f"Found {len(self.target_columns)} target columns: {self.target_columns}")
        
        # 获取分类列（用于归因特征）
        self.categorical_columns = self.analysis.get('categorical_columns', [])
        print(f"Found {len(self.categorical_columns)} categorical columns")
        
        # 获取行为列
        self.behavior_columns = self.analysis.get('behavior_columns', [])
        print(f"Found {len(self.behavior_columns)} behavior columns")
        
        # 如果没有行为列，创建虚拟行为特征
        if not self.behavior_columns:
            print("No behavior columns found, creating dummy behavior features")
            # self.behavior_columns = ['dummy_behavior'] * 32  # 创建32个虚拟特征
            # for i, col in enumerate(self.behavior_columns):
            #     self.df[col] = np.random.randn(len(self.df))  # 随机值
            raise ValueError("!! No behavior columns found")
        
        # 确保行为特征数量为32（模型期望的维度）
        if len(self.behavior_columns) > 32:
            print(f"Truncating behavior columns from {len(self.behavior_columns)} to 32")
            self.behavior_columns = self.behavior_columns[:32]
        elif len(self.behavior_columns) < 32:
            print(f"Padding behavior columns from {len(self.behavior_columns)} to 32")
            padding_needed = 32 - len(self.behavior_columns)
            for i in range(padding_needed):
                col_name = f'padding_behavior_{i}'
                self.behavior_columns.append(col_name)
                self.df[col_name] = 0.0  # 用0填充
        
        # 计算支付累积均值
        self._calculate_payment_cumsum_mean()
        
        # 准备数据
        self._prepare_data()
        
    def _calculate_payment_cumsum_mean(self):
        """计算支付累积均值"""
        print("Calculating payment cumsum mean...")
        
        payment_data = []
        for col in self.target_columns:
            if col in self.df.columns:
                payment_data.append(self.df[col].values)
        
        if payment_data:
            payment_matrix = np.column_stack(payment_data)
            self.payment_cumsum_mean = np.cumsum(np.mean(payment_matrix, axis=0))
            print(f"Payment cumsum mean shape: {self.payment_cumsum_mean.shape}")
        else:
            # 如果没有支付数据，创建虚拟数据
            self.payment_cumsum_mean = np.arange(1, 31, dtype=float)
            print("No payment data found, using dummy payment cumsum mean")
    
    def _prepare_data(self):
        """准备训练数据"""
        print("Preparing training data...")
        
        # 创建行为特征（如果没有序列，使用当前值）
        behavior_features = []
        for col in self.behavior_columns:
            if col in self.df.columns:
                behavior_features.append(self.df[col].values)
        
        if behavior_features:
            self.behavior_data = np.column_stack(behavior_features)
        else:
            # 如果没有行为数据，创建虚拟数据
            self.behavior_data = np.random.randn(len(self.df), 32)
        
        # 创建归因特征
        campaign_features = []
        for col in self.categorical_columns:
            if col in self.df.columns:
                # 将分类特征转换为数值
                if pd.api.types.is_object_dtype(self.df[col]):
                    self.df[col] = self.df[col].astype('category').cat.codes
                campaign_features.append(self.df[col].values)
        
        if campaign_features:
            self.campaign_data = np.column_stack(campaign_features)
        else:
            # 如果没有分类数据，创建虚拟数据
            self.campaign_data = np.random.randint(0, 10, (len(self.df), 47))
        
        # 创建支付数据
        payment_features = []
        for col in self.target_columns:
            if col in self.df.columns:
                payment_features.append(self.df[col].values)
        
        if payment_features:
            self.payment_data = np.column_stack(payment_features)
        else:
            # 如果没有支付数据，创建虚拟数据
            self.payment_data = np.random.randn(len(self.df), 30)
        
        # 创建支付标志
        self.have_paid_data = (self.payment_data > 0).astype(float)
        
        print(f"Data shapes:")
        print(f"  Behavior: {self.behavior_data.shape}")
        print(f"  Campaign: {self.campaign_data.shape}")
        print(f"  Payment: {self.payment_data.shape}")
        print(f"  Have paid: {self.have_paid_data.shape}")
    
    def __len__(self):
        return len(self.df)
    
    def __getitem__(self, idx):
        # 创建行为序列（如果没有序列数据，重复当前值）
        behavior_seq = np.tile(self.behavior_data[idx], (self.max_seq_len, 1))
        
        # 创建支付序列
        payment_seq = self.payment_data[idx][:self.max_seq_len]
        have_paid_seq = self.have_paid_data[idx][:self.max_seq_len]
        
        # 确保序列长度一致
        if len(payment_seq) < self.max_seq_len:
            padding_len = self.max_seq_len - len(payment_seq)
            payment_seq = np.pad(payment_seq, (0, padding_len), 'constant')
            have_paid_seq = np.pad(have_paid_seq, (0, padding_len), 'constant')
        
        return {
            'device_id': self.df.iloc[idx][self.device_col],
            'behavior': torch.FloatTensor(behavior_seq),
            'campaign': torch.FloatTensor(self.campaign_data[idx]),
            'payment': torch.FloatTensor(payment_seq),
            'have_paid': torch.FloatTensor(have_paid_seq)
        }


class GenericDataModule(pl.LightningDataModule):
    """通用数据模块"""
    
    def __init__(self, pickle_path, batch_size=4096, num_workers=4, max_seq_len=14):
        super().__init__()
        self.pickle_path = pickle_path
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.max_seq_len = max_seq_len
        
        self.train_data = None
        self.val_data = None
        self.payment_cumsum_mean = None

    def setup(self, stage=None):
        full_dataset = GenericDataset(self.pickle_path, max_seq_len=self.max_seq_len)

        total_size = len(full_dataset)
        val_size = int(total_size * 0.2)
        train_size = total_size - val_size

        self.train_set, self.val_set = random_split(
            full_dataset, [train_size, val_size],
            generator=torch.Generator().manual_seed(42)
        )
        self.payment_cumsum_mean = full_dataset.payment_cumsum_mean
        
    def train_dataloader(self):
        return DataLoader(self.train_set, batch_size=self.batch_size, pin_memory=True, num_workers=self.num_workers, shuffle=True)

    def val_dataloader(self):
        return DataLoader(self.val_set, batch_size=self.batch_size, pin_memory=True, num_workers=self.num_workers)
    
    def test_dataloader(self):
        raise NotImplementedError
    
    def predict_dataloader(self):
        raise NotImplementedError


if __name__ == "__main__":
    dm = GenericDataModule(
        pickle_path = os.path.join(os.path.dirname(__file__), '../../data/any_day_data.pkl'),
        batch_size=1, 
    )
    dm.setup()
    train_loader = dm.train_dataloader()
    for batch in train_loader:
        for k, v in batch.items():
            print(k)
            print(v)
            if type(v) == torch.Tensor:
                print(v.shape)
        break 