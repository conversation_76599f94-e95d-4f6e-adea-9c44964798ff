import sys
import os

from datetime import datetime, timedelta
from typing import List, Tuple, Any, Dict, Type
import numpy as np
import pandas as pd
from tqdm import tqdm
import json
from impala.dbapi import connect
from collections import defaultdict

from utils.impala_utils import load_any_day_data_from_impala, add_device_price, analyze_column, get_behavior_data_list, load_any_day_data_from_impala_inference

class CustomOrdinalEncoder:
    def __init__(self, columns):
        self.columns = columns
        self.mapping = {}

    def fit(self, df):
        for col in self.columns:
            unique_vals = sorted(set(x for x in df[col].dropna().unique()))
            self.mapping[col] = {val: i+1 for i, val in enumerate(unique_vals)}  # start from 1

    def transform_new(self, df):
        df_encoded = df.copy()
        for col in self.columns:
            df_encoded[col] = df_encoded[col].map(self.mapping[col]).fillna(0).astype(int)  # unknowns → 0
        return df_encoded

    def transform(self, df, ignore_cols):
        df_encoded = df.copy()
        
        for col in self.mapping.keys():
            if col in df_encoded.columns:  # 检查列是否在 DataFrame 中
                df_encoded[col] = df_encoded[col].map(self.mapping[col]).fillna(0).astype(int)  # 将未知映射为0

        target_columns = list(self.mapping.keys() & df_encoded.columns) + list(ignore_cols)
        existing_target_columns = list(set(target_columns) & set(df_encoded.columns))
        df_encoded = df_encoded[existing_target_columns]

        return df_encoded

    def fit_transform(self, df):
        self.fit(df)
        return self.transform_new(df)

    def save(self, path):
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(self.mapping, f, ensure_ascii=False, indent=2)

    def load(self, path):
        with open(path, 'r', encoding='utf-8') as f:
            self.mapping = json.load(f)


def get_any_day_training_data(day_param, end_day=None, new_data = True, for_inference = False):
    print("Loading Impala Data......")
    if end_day is None:
        # 单日模式：day_param作为run_day使用
        run_day = day_param
    else:
        # 日期范围模式：day_param作为start_day，end_day作为结束日期
        start_day = day_param
        end_day = end_day
    
    print("Loading Impala Data......")
    if not for_inference:
        campaign_df_raw, behavior_df_raw = load_any_day_data_from_impala(start_day, end_day)
    else:
        campaign_df_raw, behavior_df_raw = load_any_day_data_from_impala_inference(run_day)

    
    print(behavior_df_raw.head())
    campaign_df_raw = campaign_df_raw.dropna(subset=['channel_ty_adgroup_id'])
    behavior_df_raw = behavior_df_raw.fillna(0)
    
    print("Drop Multi-attribute User......")
    duplicates_sdk = campaign_df_raw['sdk_yidun_device_id'][campaign_df_raw['sdk_yidun_device_id'].duplicated(keep=False)].unique()
    campaign_df_raw = campaign_df_raw[~campaign_df_raw['sdk_yidun_device_id'].isin(duplicates_sdk)].reset_index(drop=True)
    behavior_df_raw = behavior_df_raw[~behavior_df_raw['sdk_yidun_device_id'].isin(duplicates_sdk)].reset_index(drop=True)
    
    print("Data Processing......")
    keys, X_cols, ignores = analyze_column(campaign_df_raw, verbose=False)
    if new_data:
        campaign_df = add_device_price(campaign_df_raw[keys + X_cols])
    else:
        campaign_df = add_device_price(campaign_df_raw)
    behavior_df = get_behavior_data_list(behavior_df_raw, campaign_df)
    
    print("Dropping Nans......")
    campaign_df = campaign_df.dropna(subset=['channel_ty_adgroup_id'])
    campaign_df = campaign_df[campaign_df['channel_ty_adgroup_id'] != ""]
    
    # simple_behavior_df = behavior_df.drop(columns=['sdk_yidun_device_id', 'attribution_day'])
    # normalized_df = pd.DataFrame()

    # normalize_dict = dict()
    # if not new_data:
    #     with open("../data/normalize_dict.json", "r") as f:
    #         normalize_dict = json.load(f)
    
    # print("Normalizing Datas......")
    # for col in tqdm(behavior_df.columns):
    #     if col in ['sdk_yidun_device_id', 'attribution_day']:
    #         normalized_df[col] = behavior_df[col]
    #         continue
        
    #     if new_data:
    #         col_data = behavior_df[col].dropna()
            
    #         # 先 flatten 得到所有值
    #         all_values = [x for lst in col_data if isinstance(lst, list) for x in lst]

    #         # 如果为空，跳过该列
    #         if not all_values:
    #             normalized_df[col] = behavior_df[col]
    #             continue

    #         col_min = min(all_values)
    #         col_max = max(all_values)
            
    #         if col == 'behav.total_recharge_amount':
    #             col_min = 0
    #             col_max = 10000
            
    #         if col_min == col_max:
    #             print(f"Same Value: {col}")
    #             continue
            
    #         normalize_dict[col] = [col_min, col_max]
        
    #     else:
    #         if col not in normalize_dict:
    #             continue
            
    #         col_max = normalize_dict[col][1]
    #         col_min = normalize_dict[col][0]
        
    #     range_val = col_max - col_min

    #     def normalize_list(lst):
    #         if not isinstance(lst, list):
    #             return lst
    #         return [(x - col_min) / range_val for x in lst]

    #     normalized_df[col] = behavior_df[col].apply(normalize_list)
        
    print("Encoding Category Features......")
    ignore_cols = ['sdk_yidun_device_id', 'attribution_day', 'attribution_day_a', 'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']
    ignore_cols += [f'd{i}_payment' for i in range(1, 31)] + [f'd{i}_retention' for i in range(1, 31)]
    # merged_df_raw = merged_df_raw.drop(columns=drop_cols, errors='ignore')
    category_cols = [col for col in campaign_df.columns if col not in ignore_cols]
    
    encoder = CustomOrdinalEncoder(columns=category_cols)
    
    if new_data:
        campaign_df = encoder.fit_transform(campaign_df)
        
    else:
        encoder.load('../data/encoding_map.json')
        campaign_df = encoder.transform(campaign_df, ignore_cols)
        
    print("Merging Two Dataframes......")
    merged_df_raw = campaign_df.merge(behavior_df, on=['sdk_yidun_device_id', 'attribution_day'], how='inner')
    merged_df_raw = merged_df_raw.sample(frac=1).reset_index(drop=True)
    
    if new_data:
        print("Saving Files......")
        # with open("../data/normalize_dict.json", "w") as f:
        #     json.dump(normalize_dict, f)
        encoder.save('../data/encoding_map.json')
        merged_df_raw.to_pickle("./data/any_day_data.pkl")
    
    print("Finish!")
    return merged_df_raw

if __name__ == "__main__":
    get_any_day_training_data('2025-04-13', '2025-06-13')
    # get_any_day_training_data('2025-04-16', '2025-04-30', new_data=False) 