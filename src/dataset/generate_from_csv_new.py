import sys
import os
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import pickle
from datetime import datetime, timedelta
from typing import List, Tuple, Any, Dict, Type
from collections import defaultdict

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dataset.generate_any_day import CustomOrdinalEncoder


def analyze_csv_structure(csv_file_path: str) -> Dict[str, Any]:
    """
    分析CSV文件结构，自动识别列类型
    
    Args:
        csv_file_path: CSV文件路径
        
    Returns:
        Dict: 包含列分析结果的字典
    """
    print(f"Analyzing CSV structure: {csv_file_path}")
    
    # 读取前几行来分析结构
    df_sample = pd.read_csv(csv_file_path, nrows=1000)
    
    analysis = {
        'total_rows': None,
        'total_columns': len(df_sample.columns),
        'column_types': {},
        'key_columns': [],
        'categorical_columns': [],
        'numerical_columns': [],
        'date_columns': [],
        'target_columns': [],
        'behavior_columns': []
    }
    
    print(f"Found {len(df_sample.columns)} columns")
    
    # 分析每一列
    for col in df_sample.columns:
        col_data = df_sample[col].dropna()
        
        if len(col_data) == 0:
            analysis['column_types'][col] = 'empty'
            continue
            
        # 检查是否为关键列
        if any(keyword in col.lower() for keyword in ['id', 'device', 'user', 'attribution']):
            analysis['key_columns'].append(col)
            analysis['column_types'][col] = 'key'
            
        # 检查是否为日期列
        elif any(keyword in col.lower() for keyword in ['date', 'day', 'time']):
            try:
                # 尝试转换第一个非空值
                first_valid = col_data.iloc[0]
                pd.to_datetime(first_valid)
                analysis['date_columns'].append(col)
                analysis['column_types'][col] = 'date'
            except:
                # 如果转换失败，检查列名是否明确包含日期关键词
                if any(keyword in col.lower() for keyword in ['attribution_day', 'execute_date']):
                    analysis['date_columns'].append(col)
                    analysis['column_types'][col] = 'date'
                
        # 检查是否为数值列
        elif pd.api.types.is_numeric_dtype(col_data):
            analysis['numerical_columns'].append(col)
            analysis['column_types'][col] = 'numerical'
            
        # 检查是否为分类列
        else:
            unique_ratio = len(col_data.unique()) / len(col_data)
            if unique_ratio < 0.5:  # 如果唯一值比例小于50%，认为是分类列
                analysis['categorical_columns'].append(col)
                analysis['column_types'][col] = 'categorical'
            else:
                analysis['numerical_columns'].append(col)
                analysis['column_types'][col] = 'numerical'
    
    # 识别目标列（支付相关）
    for col in analysis['numerical_columns']:
        if any(keyword in col.lower() for keyword in ['payment', 'pay', 'revenue', 'amount', 'money']):
            analysis['target_columns'].append(col)
    
    # 识别行为列（非目标数值列）
    for col in analysis['numerical_columns']:
        if col not in analysis['target_columns'] and col not in analysis['key_columns']:
            analysis['behavior_columns'].append(col)
    
    print(f"Analysis results:")
    print(f"  Key columns: {len(analysis['key_columns'])}")
    print(f"  Categorical columns: {len(analysis['categorical_columns'])}")
    print(f"  Numerical columns: {len(analysis['numerical_columns'])}")
    print(f"  Date columns: {len(analysis['date_columns'])}")
    print(f"  Target columns: {len(analysis['target_columns'])}")
    print(f"  Behavior columns: {len(analysis['behavior_columns'])}")
    
    return analysis


def load_csv_data_generic(csv_file_path: str, analysis: Dict[str, Any] = None) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    通用CSV数据加载函数
    
    Args:
        csv_file_path: CSV文件路径
        analysis: 列分析结果（如果为None会自动分析）
        
    Returns:
        Tuple[pd.DataFrame, Dict]: (数据, 分析结果)
    """
    print(f"Loading CSV data from {csv_file_path}...")
    
    # 如果没有提供分析结果，先分析
    if analysis is None:
        analysis = analyze_csv_structure(csv_file_path)
    
    # 读取完整数据
    df = pd.read_csv(csv_file_path)
    analysis['total_rows'] = len(df)
    
    print(f"Loaded {len(df)} rows from CSV")
    print(f"Data shape: {df.shape}")
    
    return df, analysis


def preprocess_data_generic(df: pd.DataFrame, analysis: Dict[str, Any]) -> pd.DataFrame:
    """
    通用数据预处理
    
    Args:
        df: 原始数据
        analysis: 列分析结果
        
    Returns:
        pd.DataFrame: 预处理后的数据
    """
    print("Preprocessing data...")
    
    df_processed = df.copy()
    
    # 1. 处理日期列
    for col in analysis['date_columns']:
        try:
            df_processed[col] = pd.to_datetime(df_processed[col], errors='coerce')
            print(f"  Converted {col} to datetime")
        except Exception as e:
            print(f"  Warning: Could not convert {col} to datetime: {e}")
    
    # 2. 处理数值列
    for col in analysis['numerical_columns']:
        try:
            df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce').fillna(0)
        except Exception as e:
            print(f"  Warning: Could not convert {col} to numeric: {e}")
    
    # 3. 处理分类列
    for col in analysis['categorical_columns']:
        df_processed[col] = df_processed[col].fillna('unknown')
    
    # 4. 处理关键列
    for col in analysis['key_columns']:
        df_processed[col] = df_processed[col].fillna('unknown')
    
    print("Data preprocessing completed")
    return df_processed


def create_behavior_sequences_generic(df: pd.DataFrame, analysis: Dict[str, Any], 
                                    max_days: int = 14) -> pd.DataFrame:
    """
    创建通用的行为序列
    
    Args:
        df: 预处理后的数据
        analysis: 列分析结果
        max_days: 最大天数
        
    Returns:
        pd.DataFrame: 包含行为序列的数据
    """
    print("Creating behavior sequences...")
    
    if not analysis['behavior_columns']:
        print("  No behavior columns found, skipping sequence creation")
        return df
    
    # 确保有归因日期列
    attribution_col = None
    for col in analysis['date_columns']:
        if 'attribution' in col.lower():
            attribution_col = col
            break
    
    if attribution_col is None:
        print("  No attribution date column found, skipping sequence creation")
        return df
    
    # 获取设备ID列
    device_col = None
    for col in analysis['key_columns']:
        if 'device' in col.lower() or 'id' in col.lower():
            device_col = col
            break
    
    if device_col is None:
        print("  No device ID column found, skipping sequence creation")
        return df
    
    print(f"  Using {attribution_col} as attribution date")
    print(f"  Using {device_col} as device ID")
    print(f"  Creating sequences for {len(analysis['behavior_columns'])} behavior columns")
    
    # 为每个用户创建序列
    result_data = []
    
    for device_id in tqdm(df[device_col].unique(), desc="Processing users"):
        user_data = df[df[device_col] == device_id]
        
        if len(user_data) == 0:
            continue
            
        attribution_day = user_data[attribution_col].iloc[0]
        
        # 创建max_days天的序列
        behavior_sequence = []
        for day in range(max_days):
            day_date = attribution_day + timedelta(days=day)
            day_data = user_data[user_data[attribution_col] == day_date]
            
            if len(day_data) > 0:
                # 取第一行数据
                day_features = day_data[analysis['behavior_columns']].iloc[0].tolist()
            else:
                # 如果当天没有数据，用0填充
                day_features = [0] * len(analysis['behavior_columns'])
            
            behavior_sequence.append(day_features)
        
        result_data.append({
            device_col: device_id,
            attribution_col: attribution_day,
            'behavior_sequence': behavior_sequence
        })
    
    result_df = pd.DataFrame(result_data)
    print(f"Created behavior sequences for {len(result_df)} users")
    return result_df


def process_csv_data_generic(csv_file_path: str, output_dir: str = "../data", 
                           new_data: bool = True, max_days: int = 14) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    通用的CSV数据处理函数
    
    Args:
        csv_file_path: CSV文件路径
        output_dir: 输出目录
        new_data: 是否为新数据
        max_days: 最大天数
        
    Returns:
        Tuple[pd.DataFrame, Dict]: (处理后的数据, 分析结果)
    """
    print("=" * 60)
    print("Generic CSV Data Processing")
    print("=" * 60)
    
    # 步骤1: 分析CSV结构
    analysis = analyze_csv_structure(csv_file_path)
    
    # 步骤2: 加载数据
    df, analysis = load_csv_data_generic(csv_file_path, analysis)
    
    # 步骤3: 数据清洗
    print("\nData Cleaning...")
    
    # 删除重复用户
    if analysis['key_columns']:
        device_col = analysis['key_columns'][0]  # 使用第一个关键列作为设备ID
        duplicates = df[device_col][df[device_col].duplicated(keep=False)].unique()
        df = df[~df[device_col].isin(duplicates)].reset_index(drop=True)
        print(f"  Removed {len(duplicates)} duplicate users")
    
    # 删除缺失关键字段的行（只检查最重要的列）
    critical_cols = ['sdk_yidun_device_id', 'attribution_day', 'channel_ty_adgroup_id']
    existing_critical_cols = [col for col in critical_cols if col in df.columns]
    
    if existing_critical_cols:
        df = df.dropna(subset=existing_critical_cols)
        print(f"  After removing missing values in critical columns: {len(df)} rows")
    else:
        print(f"  No critical columns found, keeping all rows: {len(df)} rows")
    
    # 步骤4: 数据预处理
    df = preprocess_data_generic(df, analysis)
    
    # 步骤5: 创建行为序列
    df_with_sequences = create_behavior_sequences_generic(df, analysis, max_days)
    
    # 步骤6: 类别特征编码
    print("\nEncoding categorical features...")
    if analysis['categorical_columns']:
        encoder = CustomOrdinalEncoder(columns=analysis['categorical_columns'])
        
        if new_data:
            df_with_sequences = encoder.fit_transform(df_with_sequences)
        else:
            encoder_path = os.path.join(output_dir, 'encoding_map.json')
            if os.path.exists(encoder_path):
                encoder.load(encoder_path)
                ignore_cols = analysis['key_columns'] + analysis['date_columns'] + analysis['target_columns']
                df_with_sequences = encoder.transform(df_with_sequences, ignore_cols)
            else:
                print("Warning: encoding_map.json not found, fitting new encoder")
                df_with_sequences = encoder.fit_transform(df_with_sequences)
    
    # 步骤7: 保存文件
    if new_data:
        print("\nSaving files...")
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存编码器
        if analysis['categorical_columns']:
            encoder_path = os.path.join(output_dir, 'encoding_map.json')
            encoder.save(encoder_path)
            print(f"  Saved encoding_map.json to {encoder_path}")
        
        # 保存分析结果
        analysis_path = os.path.join(output_dir, 'data_analysis.json')
        with open(analysis_path, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2, default=str)
        print(f"  Saved data_analysis.json to {analysis_path}")
        
        # 保存pickle文件
        pickle_path = os.path.join(output_dir, 'any_day_data.pkl')
        df_with_sequences.to_pickle(pickle_path)
        print(f"  Saved any_day_data.pkl to {pickle_path}")
    
    print("\n" + "=" * 60)
    print("Data processing completed!")
    print(f"Final data shape: {df_with_sequences.shape}")
    print("=" * 60)
    
    return df_with_sequences, analysis


def main():
    """主函数"""
    # 检查CSV文件
    csv_files = [
        "../csv_data/subtask_2624_20250715105822.csv",
        "../csv_data/subtask_2625_20250716120249.csv"
    ]
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"Processing {csv_file}...")
            try:
                merged_df, analysis = process_csv_data_generic(csv_file, new_data=True)
                print(f"Successfully processed {csv_file}")
                print(f"Final data shape: {merged_df.shape}")
                break
            except Exception as e:
                print(f"Error processing {csv_file}: {e}")
                continue
    else:
        print("No valid CSV files found in csv_data directory")


if __name__ == "__main__":
    main() 