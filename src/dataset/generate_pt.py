import zipfile
import pandas as pd
from io import BytesIO
from tqdm import tqdm
import numpy as np
import os
import random

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
import pytorch_lightning as pl
from sklearn.model_selection import train_test_split
import numpy as np

class LTVPredictionDataModule(pl.LightningDataModule):
    def __init__(self, user_data_folder, game_data_folder, device_price_path, batch_size=4096, num_workers=4):
        super().__init__()
        self.user_data_folder = user_data_folder
        self.game_data_folder = game_data_folder
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.device_price_path = device_price_path
        
        # 预定义数据分割占位符
        self.train_data = None
        self.val_data = None

    def prepare_data(self):
        """单次执行的全局数据准备（下载/保存）"""
        # 如果数据文件不存在时才执行
        if not os.path.exists("./base_data.pt"):
            raw_data = self._read_and_process_data()
            torch.save(raw_data, "./base_data.pt")

    def setup(self, stage=None):
        """每个进程执行的数据分割（训练/验证）"""
        # 加载预处理数据
        processed_data = torch.load("./base_data.pt")
        
        # 只在第一次调用时分割数据
        if self.train_data is None or self.val_data is None:
            train_data, val_data = train_test_split(
                processed_data, 
                test_size=0.2,
                random_state=42
            )
            self.train_data = train_data
            self.val_data = val_data
            # print("输入数据统计:")
            # print("Max:", max(self.train_data))
            # print("Min:", min(self.train_data))

    def _read_and_process_data(self):
        """封装原始数据处理逻辑"""
        print('Reading User Information CSVs......')
        all_user_dfs = []
        for user_filename in tqdm(os.listdir(self.user_data_folder)):
            if user_filename.endswith(".csv"):
                user_file_path = os.path.join(self.user_data_folder, user_filename)
                df_user = pd.read_csv(user_file_path)
                all_user_dfs.append(df_user)

        final_df_user = pd.concat(all_user_dfs, ignore_index=True).fillna(0)
        final_df_user['day'] = pd.to_datetime(final_df_user['day'])
        
        print('Reading Game Information CSVs......')
        dfs_game = []
        with zipfile.ZipFile(self.game_data_folder, 'r') as zip_file:
            game_file_list = zip_file.namelist()
            
            for game_file in tqdm(game_file_list):
                if game_file.endswith('.csv'):
                    with zip_file.open(game_file) as f:
                        df_game = pd.read_csv(BytesIO(f.read()))
                        dfs_game.append(df_game)  # 将DataFrame加入列表
        if dfs_game:
            final_df_game = pd.concat(dfs_game, ignore_index=True)
            # print(f"总行数: {len(final_df_game)}")
        else:
            print("没有找到CSV文件")
            
        final_df_game['day'] = pd.to_datetime(final_df_game['day'])
        
        columns_to_keep = [
            'adtrace_did',
            'day',
            'proj_main_channel',
            'sdk_device_name',
            'adtrace_reattributed',
            'price',
        ] + [f'stay{i}' for i in range(1, 8)] + [f'pay{i}' for i in range(1, 8)]
        
        final_df_user = final_df_user.drop_duplicates().reset_index(drop=True)
        final_df_game = final_df_game.drop_duplicates().reset_index(drop=True)
        print('Duplicated Data Deleted!')
        
        print('Reading Device Price.....')
        device_price_df = pd.read_csv(self.device_price_path)
        device_price_df = device_price_df.drop_duplicates('sdk_device_name').reset_index(drop=True)
        final_df_user['price'] = final_df_user['sdk_device_name'].map(device_price_df.set_index('sdk_device_name')['price'])
        print(final_df_user.head())
        
        print('Data Processing.....')
        delta_list = self.create_complete_sequences(final_df_user, final_df_game)
        df1 = pd.merge(final_df_user[columns_to_keep], delta_list.reset_index(), left_on = ['adtrace_did', 'day'], right_on = ['adtrace_did', 'min_day'], how='right')
        
        columns_to_check = ['min_delta', 'max_delta', 'final_delta', 'game_time_in_minutes', 'total_catch']

        temp_df = pd.DataFrame()
        for column in columns_to_check:
            temp_df[column] = df1[column].apply(lambda x: sum(x) if isinstance(x, list) else x)

        mask = temp_df[columns_to_check].sum(axis=1) != 0

        filtered_df = df1[mask].reset_index().drop('index', axis = 1)
        
        merged_df = pd.merge(filtered_df, final_df_user, on=['adtrace_did', 'day'], how='left', suffixes=('', '_df0'))
        print('All Data Merged!')
        
        pay_columns = [f'pay{i}' for i in range(4, 31)]
        merged_df["pay_sum"] = merged_df[pay_columns].sum(axis=1)

        # 只保留原始 df 的行
        result_df = merged_df[filtered_df.columns.tolist() + ['pay_sum']]
        result_df = result_df.fillna(0)
        print('Result DF Visualization.....')
        print(result_df.head())
        
        processed_data = [
            (
                np.array([
                    row['stay1':'stay3'].tolist(),
                    row['pay1':'pay3'].tolist(),
                    np.log10(np.array(row['min_delta'][0:3]) + 1).tolist(),
                    np.log10(np.array(row['max_delta'][0:3]) + 1).tolist(),
                    np.log10(np.array(row['final_delta'][0:3]) + 1).tolist(),
                    row['game_time_in_minutes'][0:3],
                    row['total_catch'][0:3]
                ]),  # 7×3 array
                np.array([
                    float(row['adtrace_reattributed']),
                    float(row['price']),
                ]).reshape(2, 1),  # 2×1 array，
                float(row['pay_sum'])
            )
            for _, row in result_df.iterrows()
        ]

        print('Single Training Data:')
        print(processed_data[0])

        return processed_data

    def train_dataloader(self):
        return DataLoader(
            SeqDataset(self.train_data),
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers
        )

    def val_dataloader(self):
        return DataLoader(
            SeqDataset(self.val_data),
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers
        )
    
    def create_complete_sequences(self, df1, df2):
        
        date_ranges = df1[['adtrace_did', 'day']]
        date_ranges.columns = ['adtrace_did', 'min_day']
        date_ranges = date_ranges[date_ranges['adtrace_did'].isin(df2['adtrace_did'])].reset_index(drop=True)
        
        date_ranges = date_ranges.copy()
        # 计算最大日期（最小日期+7天）
        date_ranges['max_day'] = date_ranges['min_day'] + pd.Timedelta(days=6)
        
        # 创建日期范围索引
        all_dates = pd.DataFrame({
            'adtrace_did': date_ranges['adtrace_did'].repeat(7).reset_index(drop=True), 
            'min_day': date_ranges['min_day'].repeat(7).reset_index(drop=True),
            'day': np.concatenate([
                pd.date_range(start=min_day, end=max_day, freq='D')
                for min_day, max_day in zip(date_ranges['min_day'], date_ranges['max_day'])
            ])
        })

        features = ['min_delta', 'max_delta', 'final_delta', 'game_time_in_minutes', 'total_catch']
        complete_df = pd.merge(all_dates, df2, on=['adtrace_did', 'day'], how='left')
        
        for feature in features:
            complete_df[feature] = complete_df[feature].fillna(0)
        
        # print('stage3')
        # print(complete_df)

        result = complete_df.sort_values(['adtrace_did', 'min_day']).groupby(['adtrace_did', 'min_day'])[features].agg(list)
        return result

class SeqDataset(Dataset):
    def __init__(self, result):
        self.result = result

    def __len__(self):
        return len(self.result)

    def __getitem__(self, idx):
        if idx < 0 or idx >= len(self.result):
            raise IndexError(f"Index {idx} out of range.")
        game_feature, player_feature, label = self.result[idx]
        
        game_feature = torch.tensor(game_feature, dtype=torch.float32)
        player_feature = torch.tensor(player_feature, dtype=torch.float32)
        label = torch.tensor(label, dtype=torch.float32)
        
        return game_feature, player_feature, label
    
if __name__ == "__main__":
    dm = LTVPredictionDataModule(
        user_data_folder='../campaign_data/raw_data/final/',
        game_data_folder='../campaign_data/raw_data/fish_merged.zip',
        device_price_path = '../campaign_data/device_price.csv',
        batch_size=64
    )
    dm.prepare_data()
    dm.setup()
    train_loader = dm.train_dataloader()
    for batch in train_loader:
        print("Batch contents:")
        for i, tensor in enumerate(batch):
            print(f"Tensor {i} shape: {tensor.shape}")
        break  # 只看一个批次
