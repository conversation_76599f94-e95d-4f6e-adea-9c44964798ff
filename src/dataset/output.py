import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

import os
from typing import List, Dict
from datetime import datetime, timedelta
import pandas as pd

from config import config
from utils.mysql import MySQLConnector

class MySQLOutput:
    def __init__(self):
        self.m = MySQLConnector(user=config['mysql']['user'], 
                                password=config['mysql']['password'], 
                                host=config['mysql']['host'], 
                                port=config['mysql']['port'], 
                                db=config['mysql']['database'])
        self.m.connect()
    
    # def output_user_ltv_actual(self, df: pd.DataFrame):  # TODO
    #     """ 输出用户实际LTV到MySQL """
    #     if not isinstance(df, pd.DataFrame):
    #         raise ValueError("data must be a pandas dataframe")
    #     columns = ["adtrace_id", "sdk_yidun_device_id", "date_start", "date_end", "ltv", "date_update"]
    #     values = df[columns].values.tolist()
    #     self.m.execute_insert(table_name="user_ltv_actual", columns=columns, values=values)
    
    def output_user_ltv_prediction(self, df: pd.DataFrame, model_name: str):
        """ 输出用户预测LTV到MySQL """
        if not isinstance(df, pd.DataFrame):
            raise ValueError("data must be a pandas dataframe")
        columns = ["sdk_yidun_device_id", "attribution_day", "adtrace_platform", "observed_days", "predicted_days", "pltv", "record_partition"]
        values = df[columns].values.tolist()
        values = [row + [model_name] for row in values]
        self.m.execute_insert(table_name="user_ltv_prediction", columns=columns + ["model_name"], values=values)
        
    
    # def output_ad_ltv_actual(self, df: pd.DataFrame):  # TODO
    #     """ 输出广告实际LTV到MySQL """
    #     if not isinstance(df, pd.DataFrame):
    #         raise ValueError("data must be a pandas dataframe")
    #     columns = ["ad_id", "date_start", "date_end", "ltv_days", "ltv", "user_count", "date_update"]
    #     values = df[columns].values.tolist()
    #     self.m.execute_insert(table_name="ad_ltv_actual", columns=columns, values=values)
    
    
    def output_ad_ltv_prediction(self, df: pd.DataFrame, model_name: str):
        """ 输出广告预测LTV到MySQL """
        if not isinstance(df, pd.DataFrame):
            raise ValueError("data must be a pandas dataframe")
        columns = ["channel_ty_adgroup_id", "attribution_day", "adtrace_platform", "observed_days", "predicted_days", "pltv", "user_count", "record_partition"]
        values = df[columns].values.tolist()
        values = [row + [model_name] for row in values]
        self.m.execute_insert(table_name="ad_ltv_prediction", columns=columns + ["model_name"], values=values)
    
    
    def output_user_profile_to_mysql(self, data):
        pass # TODO
    
