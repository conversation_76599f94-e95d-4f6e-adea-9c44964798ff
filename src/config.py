import yaml
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
env = os.getenv("APP_ENV", "dev").lower()
if env not in ["dev", "prod"]:
    raise ValueError(f"无效环境配置 {env}，默认使用 dev")
if env == "dev":
    config_path = f'{current_dir}/../config-dev.yaml'
else:
    config_path = f'{current_dir}/../config-prod.yaml'

with open(config_path, 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)
    # path中如果不是绝对路径，则加上base_dir
    for key, value in config['path'].items():
        if not value.startswith('/'):
            config['path'][key] = f'{current_dir}/../{value}'

            
