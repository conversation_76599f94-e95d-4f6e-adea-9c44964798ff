import os
os.environ["CUDA_VISIBLE_DEVICES"] = "2,3"

import pytorch_lightning as pl
from torch.utils.data import DataLoader
from sklearn.model_selection import train_test_split

from dataset.light_data_payment_regression import PaymentRegressionDataModule

# from model.torch_model import LightModel
from model.torch_model_payment_regression_modified import PaymentRegressionModel
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.callbacks import EarlyStopping

logger = TensorBoardLogger("tb_logs", name="payment_regression")

import warnings
warnings.filterwarnings("ignore", message=".*To copy construct from a tensor.*")

# Ensure dataset loading happens only once
if __name__ == "__main__":
    dm = PaymentRegressionDataModule(
        pickle_path = 'data/base_data_clean.pickle',
        batch_size=4096
    )
    
    dm.setup()
    train_loader = dm.train_dataloader()
    
    model = PaymentRegressionModel()
    
    early_stop_callback = EarlyStopping(
        monitor="val_loss", 
        mode="min",  
        patience=20, 
        verbose=True  
    )
    
    trainer = pl.Trainer(
        max_epochs=1000,
        devices=[0,1],
        accelerator="gpu",
        logger=TensorBoardLogger("tb_logs", name="payment_regression"),
        log_every_n_steps=1,
        num_sanity_val_steps=0, 
        callbacks=[early_stop_callback]
    )
    
    trainer.fit(model, datamodule=dm)