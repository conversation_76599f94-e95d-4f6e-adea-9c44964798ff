import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from typing import Any, Dict, Tuple
from datetime import datetime, timedelta
import time
from collections import defaultdict
import traceback
import numpy as np
import pandas as pd
import torch
import pytz
from tqdm import tqdm

from config import config
from misc.load_data_v2 import Fish3DLTVDataset
from dataset.output import MySQLOutput
from utils.mysql import MySQLConnector
from model.torch_model_whether_payment import WhetherPaymentModel
from model.torch_model_payment_regression import PaymentRegressionModel

from pipelines.base import PipelineBase

class Pipelinev2(PipelineBase):
    def __init__(self, name: str, class_path: str, log_file_path: str, **kwargs):
        super().__init__(name, class_path, log_file_path, **kwargs)
        self.name = name
        self.today = datetime.now(pytz.timezone("Asia/Shanghai")).date()  # 当前日期
        self.ds = kwargs.get("ds", (self.today - timedelta(days=1)).strftime("%Y-%m-%d"))
        self.attribution_ds = (datetime.strptime(self.ds, "%Y-%m-%d") - timedelta(days=2)).strftime("%Y-%m-%d")  # 数据时间范围为[attribution_ds, ds]
        self.days = 7
        
        self.user_adtrace_platform = {}
        self.adtrace_platform_map = lambda x: '1' if x in ('jrtt', 'jrtt_wxgame', 'jrtt_xiansuo_v3', 'jrtt_xiansuo_v2', 'jrtt_dygame') else '2' if x in ('kuaishou', 'kuaishou_wxgame', 'kslive', 'ksunion') else '3' if x in ('gdt', 'gdt_v2', 'gdt_wxgame') else '101'
        
        
    def load_data(self) -> Tuple[Fish3DLTVDataset, Dict[str, str], Dict[str, str]]:
        """ 加载数据 
        return:
            ltv_dataset: LTV数据集
            user_adtrace_platform: 用户-渠道映射
            ad_adtrace_platform: 广告-渠道映射
        """
        ltv_dataset = Fish3DLTVDataset(config, self.ds)
        user_adtrace_platform = {did: self.adtrace_platform_map(platform) for did, platform in ltv_dataset.did2adtrace_platform.items()}
        ad_adtrace_platform = {}
        for did in user_adtrace_platform:
            ad_id = ltv_dataset.did2campaign[did]
            ad_adtrace_platform[ad_id] = user_adtrace_platform[did]
        
        return ltv_dataset, user_adtrace_platform, ad_adtrace_platform
    
    
    def predict(self, ltv_dataset: Fish3DLTVDataset) -> Tuple[Dict[str, float], Dict[str, float], Dict[str, int]]:
        """ 预测LTV """
        model_whether_payment = WhetherPaymentModel()
        model_payment_regression = PaymentRegressionModel()
        
        # 加载 .pth 文件
        state_dict_whether_payment = torch.load(config["path"]["whether_payment_model_path"])
        state_dict_payment_regression = torch.load(config["path"]["payment_regression_model_path"])

        # 加载状态字典到模型中
        model_whether_payment.load_state_dict(state_dict_whether_payment)
        model_payment_regression.load_state_dict(state_dict_payment_regression)

        model_whether_payment.eval()
        model_payment_regression.eval()
        
        predicted_user_ltv = {}
        predicted_campaign_ltv = defaultdict(float)
        campaign_user_counts = defaultdict(int)
        
        for did in tqdm(ltv_dataset.users):
            # self.predicted_user_ltv[item["adtrace_id"]] = random.randint(0, 200)
            seq_feature, user_feature = ltv_dataset.transform(did)
            seq_feature = torch.tensor(seq_feature).squeeze(0)
            user_feature = torch.tensor(user_feature).squeeze(0)
            if model_whether_payment(seq_feature.to(torch.float).clone().detach().unsqueeze(0), user_feature.unsqueeze(0)).item() < 0.85:
                predicted_LTV = 0
            else:
                output = model_payment_regression(seq_feature.float().unsqueeze(0), user_feature.unsqueeze(0)).item()
                predicted_LTV = np.exp(output)
            
            min_LTV = sum(seq_feature[1, :])
            predicted_user_ltv[did] = (min_LTV + predicted_LTV).item()
            predicted_campaign_ltv[ltv_dataset.did2campaign[did]] += predicted_user_ltv[did]
            campaign_user_counts[ltv_dataset.did2campaign[did]] += 1
            
        
        return predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts
    
    
    def output(self, 
               predicted_user_ltv: Dict[str, float], 
               predicted_campaign_ltv: Dict[str, float], 
               campaign_user_counts: Dict[str, int], 
               user_adtrace_platform: Dict[str, str], 
               ad_adtrace_platform: Dict[str, str]): 
        """ 输出预测结果 """
        m = MySQLOutput()
        
        # 输出用户LTV预测值
        user_ltv_prediction_data = [
            {
                "sdk_yidun_device_id": did,
                "attribution_day": self.attribution_ds,
                "adtrace_platform": user_adtrace_platform[did],
                "observed_days": 3,
                "predicted_days": self.days,
                "pltv": pltv,
                "record_partition": self.ds,
            }
            for did, pltv in predicted_user_ltv.items()
        ]
        
        user_ltv_df = pd.DataFrame(user_ltv_prediction_data).fillna(-1)
        self.logger.info("User LTV Prediction Example:")
        self.logger.info(user_ltv_df.head().to_string())
        
        m.output_user_ltv_prediction(user_ltv_df, self.name)
        
        # 输出广告LTV预测值
        ad_ltv_prediction_data = [
            {
                "channel_ty_adgroup_id": channel_ty_adgroup_id,
                "attribution_day": self.attribution_ds, # （当前仅包含一天）
                "adtrace_platform": ad_adtrace_platform[channel_ty_adgroup_id],
                "observed_days": 3,
                "predicted_days": self.days,
                "pltv": pltv,
                "user_count": campaign_user_counts[channel_ty_adgroup_id],
                "record_partition": self.ds,
            }
            for channel_ty_adgroup_id, pltv in predicted_campaign_ltv.items()
        ]
        
        ad_ltv_df = pd.DataFrame(ad_ltv_prediction_data).fillna(-1)
        self.logger.info("Campaign LTV Prediction Example:")
        self.logger.info(ad_ltv_df.head().to_string())
        
        m.output_ad_ltv_prediction(ad_ltv_df, self.name)
        
            
    def execute(self) -> Dict[str, Any]:
        """执行完整流水线"""
        start_time = time.time()
        if self.load_task_status(self.ds, self.name) != "pending":
            self.logger.info(f"pipeline {self.name} already run, skip")
            return {
                "status": "skip",
                "info": f"pipeline {self.name} already run, skip",
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
            }
        try:
            # 数据加载
            self.logger.info("data loading...")
            ltv_dataset, user_adtrace_platform, ad_adtrace_platform = self.load_data()
            
            # 执行预测
            self.logger.info("predicting...")
            predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts = self.predict(ltv_dataset)
            
            # 结果保存
            self.logger.info("saving results...")
            self.output(predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts, user_adtrace_platform, ad_adtrace_platform)

            # 更新任务状态
            self.update_task_status(flag='success')

            # 收集执行结果
            result = {
                "status": "success",
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
                "info": f"len(predicted_user_ltv) : {len(predicted_user_ltv)}, len(predicted_campaign_ltv) : {len(predicted_campaign_ltv)}",
            }
            
        except Exception as e:
            self.logger.error(f"pipeline {self.name} failed: {str(e)}", exc_info=True)
            exc_type, exc_value, exc_traceback = sys.exc_info()
            tb = traceback.extract_tb(exc_traceback)[-1] 
            result = {
                "status": "failed",
                "error": str(e),
                "error_line": f'{tb.filename}:{tb.lineno}',
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
            }
            self.update_task_status(flag='failure')
            
        return result