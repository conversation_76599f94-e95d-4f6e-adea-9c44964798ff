import os
os.environ["CUDA_VISIBLE_DEVICES"] = "4,5"
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from datetime import datetime, timedelta
import time
import os
import traceback
import torch
from typing import List, Tuple, Any, Dict, Type
import numpy as np
import pandas as pd
import json
import pickle
from tqdm import tqdm
from collections import defaultdict

from config import config
from utils.mysql import MySQLConnector
from dataset.output import MySQLOutput
from pipelines.base import PipelineBase

from model.torch_model_any_day_prediction import AnyDayModel
from dataset.generate_any_day import get_any_day_training_data
# from dataset.light_data_any_day_prediction import process_behavior_dataframe, process_payment_dataframe

class PipelineADPInference(PipelineBase):
    def __init__(self, name: str, class_path: str, log_file_path: str, run_date, **kwargs):
        super().__init__(name, class_path, log_file_path, **kwargs)
        self.name = name  # 流水线名称 
        self.today = datetime.now().date()  # 当前日期
        self.ds = kwargs.get("ds", (self.today - timedelta(days=1)).strftime("%Y-%m-%d"))
        self.attribution_ds = (datetime.strptime(self.ds, "%Y-%m-%d") - timedelta(days=2)).strftime("%Y-%m-%d")  # 数据时间范围为[attribution_ds, ds]
        self.start_day = kwargs.get("start_day", 3)
        self.end_day = kwargs.get("end_day", 7)
        self.max_length = 14    #训练时的maxlength

        self.run_date = run_date
        self.folder_path = f"/wkspace/sunyi/LTV_prediction/evaluation/evaluate_data/data_{self.run_date}"
        
        self.model_filepath = kwargs.get("model_filepath")
        if not os.path.isabs(self.model_filepath):
            self.model_filepath = os.path.join(project_root, '..', self.model_filepath)
            
        self.encoding_map = kwargs.get("encoding_map")
        if not os.path.isabs(self.encoding_map):
            self.encoding_map = os.path.join(project_root, '..', self.encoding_map)
            
        self.model = None
        
        self.user_adtrace_platform = {}
        self.ad_adtrace_platform = {}
        self.adtrace_platform_map = lambda x: '1' if x in ('jrtt', 'jrtt_wxgame', 'jrtt_xiansuo_v3', 'jrtt_xiansuo_v2', 'jrtt_dygame') else '2' if x in ('kuaishou', 'kuaishou_wxgame', 'kslive', 'ksunion') else '3' if x in ('gdt', 'gdt_v2', 'gdt_wxgame') else '101'
        
        self.merged_df_raw = None
        self.device = None
        
        self.all_base_df = []
        self.all_behavior_feature = []
        self.all_campaign_feature = []
        self.all_start_feature = []
        self.all_target_payment = []
                
    def _load_model(self):
    # 获取可用设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Using device: {device}")
        
        # 创建模型
        self.model = AnyDayModel()
        
        # 加载模型权重
        if device.type == "cuda":
            state_dict_any_day = torch.load(config["path"]["adp_model_path"], map_location=device)
        else:
            print("Warining: using cpu")
            state_dict_any_day = torch.load(config["path"]["adp_model_path"], map_location="cpu")
        
        self.model.load_state_dict(state_dict_any_day)
        
        # 将模型移动到设备上
        self.model = self.model.to(device)
        
        self.model.eval()
        
        self.device = device  # 返回设备以便在其他地方使用
    
    def _get_dataframe(self, run_date):
        self.merged_df_raw = get_any_day_training_data(run_date, new_data=False, for_inference=True) 
        return self.merged_df_raw
    
    def update_task_status(self, flag: str):
        """ 更新任务状态 """
        conn = MySQLConnector(user=config['mysql']['user'], 
                            password=config['mysql']['password'], 
                            host=config['mysql']['host'], 
                            port=config['mysql']['port'], 
                            db=config['mysql']['database'])
        conn.connect()
        query = f"insert into service_status (ds, task_name, task_status) values ('{self.ds}', '{self.name}', '{flag}') on duplicate key update task_status = '{flag}'"
        conn.execute(query)

    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """ 加载数据 """
        # return self._load_data_from_csv()
        return self._get_dataframe(self.run_date)
    
    def process_base_dataframe(self, merged_df_raw, start_idx):
        base_df = merged_df_raw[
            ['sdk_yidun_device_id', 'attribution_day',  'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']
            ].copy()
        
        sum_cols_3LTV = [f"d{i}_payment" for i in range(1, 4)]
        sum_cols_7LTV = [f"d{i}_payment" for i in range(1, 8)]
        sum_cols_14LTV = [f"d{i}_payment" for i in range(1, 15)]
                
        numeric_df = merged_df_raw[sum_cols_14LTV].apply(pd.to_numeric, errors='coerce')
        numeric_df = numeric_df.fillna(0)
        
        base_df['ltv_3'] = numeric_df[sum_cols_3LTV].sum(axis=1)
        base_df['ltv_7'] = numeric_df[sum_cols_7LTV].sum(axis=1)
        base_df['ltv_14'] = numeric_df[sum_cols_14LTV].sum(axis=1)

        base_df['observed_days'] = start_idx + 1
        return base_df
    
    def save_data_to_folder(self, folder_path):
    # 确保文件夹存在
        os.makedirs(folder_path, exist_ok=True)

        # 存储数据
        pd.concat(self.all_base_df).to_parquet(os.path.join(folder_path, 'base_df.parquet'))

        with open(os.path.join(folder_path, 'behavior_feature.pkl'), 'wb') as f:
            pickle.dump(self.all_behavior_feature, f)

        with open(os.path.join(folder_path, 'campaign_feature.pkl'), 'wb') as f:
            pickle.dump(self.all_campaign_feature, f)

        with open(os.path.join(folder_path, 'start_feature.pkl'), 'wb') as f:
            pickle.dump(self.all_start_feature, f)

        with open(os.path.join(folder_path, 'target_payment.pkl'), 'wb') as f:
            pickle.dump(self.all_target_payment, f)
        
        with open(os.path.join(folder_path, 'user_adtrace_platform.pkl'), 'wb') as f:
            pickle.dump(self.user_adtrace_platform, f)
            
    def load_data_from_folder(self, folder_path):
        # 读取 base_df 的 parquet 文件，并将其分割成一个列表的 DataFrame
        base_df_path = os.path.join(folder_path, 'base_df.parquet')
        self.all_base_df = [pd.read_parquet(base_df_path)]

        # 读取各个特征列表的 pickle 文件
        with open(os.path.join(folder_path, 'behavior_feature.pkl'), 'rb') as f:
            self.all_behavior_feature = pickle.load(f)

        with open(os.path.join(folder_path, 'campaign_feature.pkl'), 'rb') as f:
            self.all_campaign_feature = pickle.load(f)

        with open(os.path.join(folder_path, 'start_feature.pkl'), 'rb') as f:
            self.all_start_feature = pickle.load(f)

        with open(os.path.join(folder_path, 'target_payment.pkl'), 'rb') as f:
            self.all_target_payment = pickle.load(f)
        
        with open(os.path.join(folder_path, 'user_adtrace_platform.pkl'), 'rb') as f:
            self.user_adtrace_platform = pickle.load(f)
    
    def data_preprocess(self, merged_df_raw) -> Tuple[List[List[str]], pd.DataFrame]:
        
        drop_cols = ['sdk_yidun_device_id', 'attribution_day', 'attribution_day_a', 
                    'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']  + [f'd{i}_retention' for i in range(1, 31)]
        payment_cols = [f'd{i}_payment' for i in range(1, 31)]
        
        existing_payment_cols = list(set(payment_cols) & set(merged_df_raw.columns))
        
        negative_mask = (merged_df_raw[existing_payment_cols].apply(pd.to_numeric, errors='coerce') < 0).any(axis=1)
        if negative_mask.any():
            merged_df_raw = merged_df_raw.loc[~negative_mask]
            merged_df_raw.reset_index(drop=True, inplace=True)
            
        payment_df = merged_df_raw[existing_payment_cols].astype(float)
        merged_df = merged_df_raw.drop(columns=drop_cols + payment_cols, errors='ignore')
        
        for _, row in merged_df_raw.iterrows():
            self.user_adtrace_platform[row['sdk_yidun_device_id']] = self.adtrace_platform_map(str(row['adtrace_platform']))
        
        print("Data Read!")
        
        behavior_df = merged_df[[col for col in merged_df.columns if col.startswith('behav')]]
        campagin_df = merged_df[[col for col in merged_df.columns if not col.startswith('behav')]]
        
        # 从文件恢复
        with open('../data/behavior_columns.pkl', 'rb') as f:
            behavior_columns_restored = pickle.load(f)

        with open('../data/campaign_columns.pkl', 'rb') as f:
            campaign_columns_restored = pickle.load(f)
            
        behavior_df = behavior_df[behavior_columns_restored]
        campagin_df = campagin_df[campaign_columns_restored]
        
        for start_idx in range(0, 14):
            print(f"Processing Start Day = {start_idx}")
            target_attribution_day = (datetime.strptime(self.run_date, "%Y-%m-%d") - timedelta(days=1+start_idx)).strftime("%Y-%m-%d")
            print(f"Target Attribution Day is {target_attribution_day}")
            
            date_mask = merged_df_raw['attribution_day'] == target_attribution_day
            temp_merged_df_raw = merged_df_raw[date_mask].reset_index(drop=True)
            temp_behavior_df = behavior_df[date_mask].reset_index(drop=True)
            temp_campaign_df = campagin_df[date_mask].reset_index(drop=True)
            temp_payment_df = payment_df[date_mask].reset_index(drop=True)

            base_df = self.process_base_dataframe(temp_merged_df_raw, start_idx)
            behavior_feature = process_behavior_dataframe(temp_behavior_df, start_idx)
            campaign_feature = temp_campaign_df.values.tolist()
            start_feature = [start_idx + 1] * len(campaign_feature)
        
            self.all_base_df.append(base_df)
            self.all_behavior_feature.extend(behavior_feature)
            self.all_campaign_feature.extend(campaign_feature)
            self.all_start_feature.extend(start_feature)
                
        self.save_data_to_folder(self.folder_path)
    
    def verify_data(self):
        # 获取列名和字典键
        df_columns = set(self.merged_df_raw.columns)
        with open('../data/encoding_map.json', 'r', encoding='utf-8') as f:
            my_dict = json.load(f)
        dict_keys = set(my_dict.keys())

        # 查找不同的列名和键
        different_in_df = df_columns - dict_keys
        different_in_dict = dict_keys - df_columns

        print("Not in DF", different_in_dict)
        return True

    
    def predict(self) -> Tuple[Dict[str, float], Dict[str, float], Dict[str, int]]:
        """ 预测LTV """        
        # guiyin_features = torch.tensor(self.all_campaign_feature, dtype=torch.float)
        # behav_features = torch.tensor(self.all_behavior_feature, dtype=torch.float)
        # behav_lengths = torch.tensor(self.all_start_feature, dtype=torch.long)
        # target_dates = torch.tensor(self.all_end_feature, dtype=torch.long)

        # # Stack all features into a single large batch
        # batch = (guiyin_features, behav_features, behav_lengths, target_dates)

        # with torch.no_grad():
        #     predictions = self.model(batch)
        
        # # Use predictions directly
        # predictions_list = predictions.cpu()  # Move predictions to CPU if needed
        
        # 假设 all_campaign_feature, all_behavior_feature, all_start_feature, all_end_feature 是列表或数组
        
        predictions_list_3LTV = []
        predictions_list_7LTV = []
        predictions_list_14LTV = []
        
        num_samples = len(self.all_campaign_feature)
        batch_size = 1024
        
        device = next(self.model.parameters()).device
        print(f"Model is on device: {device}")
        
        # 处理完整批次
        for batch_start in tqdm(range(0, num_samples, batch_size)):
            batch_end = min(batch_start + batch_size, num_samples)
            # 获取模型所在的设备
            device = next(self.model.parameters()).device

            # 准备当前批次的数据
            guiyin_features = torch.tensor(self.all_campaign_feature[batch_start:batch_end], dtype=torch.float).to(device)
            behav_features = torch.tensor(self.all_behavior_feature[batch_start:batch_end], dtype=torch.float).to(device)
            behav_lengths = torch.tensor(self.all_start_feature[batch_start:batch_end], dtype=torch.long).to(device)

            # print(torch.max(guiyin_features, axis=0))
            # print(torch.max(behav_features, axis=0))
            # print(torch.max(behav_lengths, axis=0))
            
            # print(torch.min(guiyin_features, axis=0))
            # print(torch.min(behav_features, axis=0))
            # print(torch.min(behav_lengths, axis=0))
            
            # 组成批次
            batch = (guiyin_features, behav_features, behav_lengths)
            
            # 进行预测
            with torch.no_grad():
                batch_predictions_3LTV, batch_predictions_7LTV, batch_predictions_14LTV = self.model(batch)
                predictions_list_3LTV.append(batch_predictions_3LTV.cpu())
                predictions_list_7LTV.append(batch_predictions_7LTV.cpu())
                predictions_list_14LTV.append(batch_predictions_14LTV.cpu())
    
                
            # 输出预测结果或进一步处理
            # print(f"Prediction for record {i}: {prediction}")

        # Further processing on predictions_list can be done here
        print(predictions_list_3LTV[0].shape)
        pred_3LTV_logits = torch.cat(predictions_list_3LTV, dim=0) 
        print(pred_3LTV_logits.shape)    
        
        print(predictions_list_7LTV[0].shape)
        pred_7LTV_logits = torch.cat(predictions_list_7LTV, dim=0) 
        print(pred_7LTV_logits.shape)    
        
        print(predictions_list_14LTV[0].shape)
        pred_14LTV_logits = torch.cat(predictions_list_14LTV, dim=0) 
        print(pred_14LTV_logits.shape)       

        pred_3LTV_mask = torch.sigmoid(pred_3LTV_logits[:, 0]) > 0.5
        pred_7LTV_mask = torch.sigmoid(pred_7LTV_logits[:, 0]) > 0.5
        pred_14LTV_mask = torch.sigmoid(pred_14LTV_logits[:, 0]) > 0.5
        
        print(f"sum 3ltv whether paid: {sum(pred_3LTV_mask)}")
        print(f"sum 7ltv whether paid: {sum(pred_7LTV_mask)}")
        print(f"sum 14ltv whether paid: {sum(pred_14LTV_mask)}")
        
        print(pred_3LTV_logits[:, 0])
        
        stacked_df = pd.concat(self.all_base_df, ignore_index=True)
        # y_base = self.payment_df[['d1_payment', 'd2_payment', 'd3_payment']].sum(axis=1)
        print(len(stacked_df))
        
        pred_3LTV = torch.exp(pred_3LTV_logits[:, 1] + (torch.nn.functional.softplus(pred_3LTV_logits[:, 2])**2)/2)
        pred_7LTV = torch.exp(pred_7LTV_logits[:, 1] + (torch.nn.functional.softplus(pred_7LTV_logits[:, 2])**2)/2)
        pred_14LTV = torch.exp(pred_14LTV_logits[:, 1] + (torch.nn.functional.softplus(pred_14LTV_logits[:, 2])**2)/2)

        y_pred_3LTV = np.maximum(pred_3LTV, 0) * pred_3LTV_mask
        y_pred_7LTV = np.maximum(pred_7LTV, 0) * pred_7LTV_mask
        y_pred_14LTV = np.maximum(pred_14LTV, 0) * pred_14LTV_mask
        
        y_pred_3LTV = np.asarray(y_pred_3LTV)
        y_pred_7LTV = np.asarray(y_pred_7LTV)
        y_pred_14LTV = np.asarray(y_pred_14LTV)
        
        predicted_user_ltv = defaultdict(float)
        predicted_campaign_ltv = defaultdict(float)
        campaign_user_counts = defaultdict(int)
                
        for i, row in stacked_df.iterrows():
            sdk_yidun_device_id_ = row['sdk_yidun_device_id']
            channel_ty_adgroup_id_ = row['channel_ty_adgroup_id']
            observed_days_ = row['observed_days']
            
            # (, attribution_day,  channel_ty_account_id, channel_ty_adgroup_id, channel_ty_campaign_id)
            predicted_user_ltv[(sdk_yidun_device_id_, observed_days_)] += np.array([y_pred_3LTV[i], y_pred_7LTV[i], y_pred_14LTV[i]])
            if channel_ty_adgroup_id_ not in ['unknown', '__DID__', '-1', 'nan', '']:
                predicted_campaign_ltv[(channel_ty_adgroup_id_, observed_days_)] += np.array([y_pred_3LTV[i], y_pred_7LTV[i], y_pred_14LTV[i]])
                campaign_user_counts[(channel_ty_adgroup_id_, observed_days_)] += 1
                # 增加广告ADTRACE_PLATFORM映射
                self.ad_adtrace_platform[(channel_ty_adgroup_id_, observed_days_)] = self.user_adtrace_platform[sdk_yidun_device_id_] 
        
        return predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts
        
            
    def output(self, predicted_user_ltv: Dict[str, float], 
                predicted_campaign_ltv: Dict[str, float], 
                campaign_user_counts: Dict[str, int]): 
        """ 输出预测结果 """
        m = MySQLOutput()
        
        # 输出用户LTV预测值
        user_ltv_prediction_data = [
            {
                "sdk_yidun_device_id": sdk_yidun_device_id,
                "attribution_day":(datetime.strptime(self.run_date, "%Y-%m-%d") - timedelta(days=observed_days)).strftime("%Y-%m-%d"),
                "adtrace_platform": self.user_adtrace_platform[sdk_yidun_device_id],
                "observed_days": observed_days,
                "pltv_3LTV": pltv[0],
                "pltv_7LTV": pltv[0] + pltv[1],
                "pltv_14LTV": pltv[0] + pltv[1] + pltv[2],
                "record_partition": self.ds,
            }
            for (sdk_yidun_device_id, observed_days), pltv in predicted_user_ltv.items()
        ]
        user_ltv_df = pd.DataFrame(user_ltv_prediction_data)
        print(user_ltv_df.head())
        # m.output_user_ltv_prediction(user_ltv_df, self.name)
        
        # 汇总用户LTV预测值，输出广告LTV预测值
        campaign_ltv_prediction_data = [
            {
                "channel_ty_adgroup_id": channel_ty_adgroup_id,
                "attribution_day": (datetime.strptime(self.run_date, "%Y-%m-%d") - timedelta(days=observed_days)).strftime("%Y-%m-%d"), # （当前仅包含一天）
                "adtrace_platform": self.ad_adtrace_platform[(channel_ty_adgroup_id, observed_days)],
                "observed_days": observed_days,
                "pltv_3LTV": pltv[0],
                "pltv_7LTV": pltv[0] + pltv[1],
                "pltv_14LTV": pltv[0] + pltv[1] + pltv[2],
                "user_count": campaign_user_counts[channel_ty_adgroup_id],
                "record_partition": self.ds,
            }
            for (channel_ty_adgroup_id, observed_days), pltv in predicted_campaign_ltv.items()
        ]
        campaign_ltv_df = pd.DataFrame(campaign_ltv_prediction_data)
        print(campaign_ltv_df.head())
        # m.output_ad_ltv_prediction(campaign_ltv_df, self.name)
        
        return user_ltv_df, campaign_ltv_df

    
    def execute(self) -> Dict[str, Any]:
        """执行完整流水线"""
        start_time = time.time()
        try:
            # 模型加载
            self.logger.info("model loading...")
            self._load_model()
            
            if os.path.exists(self.folder_path):
                self.logger.info("found preprocessed data, loading......")
                self.load_data_from_folder(self.folder_path)
                
            else:
                # 数据加载
                self.logger.info("data loading...")
                self.load_data()
                
                # 数据预处理
                self.logger.info("data preprocessing...")
                self.data_preprocess(self.merged_df_raw)
            
            # self.verify_data()
                
            # 执行预测
            self.logger.info("predicting...")
            predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts = self.predict()
            
            # 结果保存
            self.logger.info("saving results...")
            self.output(predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts)
            
            # 更新任务状态
            self.update_task_status(flag='success')
            
            # 收集执行结果
            result = {
                "status": "success",
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
                "info": f"len(predicted_user_ltv): {len(predicted_user_ltv)}, len(predicted_campaign_ltv): {len(predicted_campaign_ltv)}",
            }
            
        except Exception as e:
            self.logger.error(f"pipeline {self.name} failed: {str(e)}", exc_info=True)
            exc_type, exc_value, exc_traceback = sys.exc_info()
            tb = traceback.extract_tb(exc_traceback)[-1] 
            result = {
                "status": "failed",
                "error": str(e),
                "error_line": f'{tb.filename}:{tb.lineno}',
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
            }
            self.update_task_status(flag='failure')
            
        return result
    