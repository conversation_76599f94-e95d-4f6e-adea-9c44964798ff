import os
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from datetime import datetime, timedelta
import pytz
import time
import os
import traceback
import random
from typing import List, Tuple, Any, Dict, Type
import numpy as np
import pandas as pd
import json
from io import StringIO
import tarfile
from tqdm import tqdm
from collections import defaultdict
from catboost import CatBoostRegressor
from impala.dbapi import connect

from utils.logger import setup_logger
from config import config
from utils.mysql import MySQLConnector
from dataset.output import MySQLOutput
from pipelines.base import PipelineBase



class PipelineTreeInference(PipelineBase):
    def __init__(self, name: str, class_path: str, log_file_path: str, **kwargs):
        super().__init__(name, class_path, log_file_path, **kwargs)
        self.name = name  # 流水线名称 
        self.today = datetime.now(pytz.timezone("Asia/Shanghai")).date()  # 当前日期

        self.ds = kwargs.get("ds", (self.today - timedelta(days=1)).strftime("%Y-%m-%d"))
        self.attribution_ds = (datetime.strptime(self.ds, "%Y-%m-%d") - timedelta(days=2)).strftime("%Y-%m-%d")  # 数据时间范围为[attribution_ds, ds]
        self.days = 7  # 预测天数，X日LTV
        
        self.model_filepath = kwargs.get("model_filepath")
        if not os.path.isabs(self.model_filepath):
            self.model_filepath = os.path.join(project_root, '..', self.model_filepath)
        self.model_metadata_filepath = kwargs.get("model_metadata_filepath")
        if not os.path.isabs(self.model_metadata_filepath):
            self.model_metadata_filepath = os.path.join(project_root, '..', self.model_metadata_filepath)
        self.model = None
        
        self.user_adtrace_platform = {}
        self.ad_adtrace_platform = {}
        self.adtrace_platform_map = lambda x: '1' if x in ('jrtt', 'jrtt_wxgame', 'jrtt_xiansuo_v3', 'jrtt_xiansuo_v2', 'jrtt_dygame') else '2' if x in ('kuaishou', 'kuaishou_wxgame', 'kslive', 'ksunion') else '3' if x in ('gdt', 'gdt_v2', 'gdt_wxgame') else '101'
        
        
    def _load_model(self):
        with open(self.model_metadata_filepath, "r") as f:
            self.model_metadata = json.load(f)
        self.model = CatBoostRegressor()
        self.model.load_model(self.model_filepath)
        self.logger.info("model load success")
    
    
    def _read_dataframe(self, file_list):
        """ 读取dataframe数据 """
        d = dict(zip(self.model_metadata["feature_names"], self.model_metadata["feature_types"]))
        _map = {'object': str, 'int64': int, 'float64': float}
        converters = {k: _map[v] for k, v in d.items()}
        converters.update({'channel_ty_adgroup_id': str, 'channel_ty_account_id': str, 'channel_ty_campaign_id': str})

        df_list = []
        for filepath in file_list:
            if filepath.endswith('.tar.gz'):
                with tarfile.open(filepath, 'r:gz') as tar:
                    for member in tar.getmembers():
                        if member.name.endswith('.csv'):
                            csv_file = tar.extractfile(member)
                            content = csv_file.read().decode('utf-8') 
                            # df = pd.read_csv(StringIO(content), converters=converters)  
                            df = pd.read_csv(StringIO(content), dtype=converters)  
                            df_list.append(df)
            elif filepath.endswith('.csv'):
                df = pd.read_csv(filepath, dtype=converters)   
                df_list.append(df)
        return pd.concat(df_list, ignore_index=True)
    
    
    def _add_device_price(self, df):
        """ 添加设备价格 """
        device2price = pd.read_csv(os.path.join(project_root, '../data/device2price.csv'))
        df_with_price = pd.merge(
            df,
            device2price,
            left_on='sdk_device_name',
            right_on='device_name', 
            how='left' 
        )
        df_with_price = df_with_price.rename(columns={'price': 'device_price'})
        return df_with_price
    
    
    def _flatten_behavior_data(self, df, df_with_attribution):
        """ 将多日的行为数据拼接为单日数据 """
        user2attribution = df_with_attribution.set_index('sdk_yidun_device_id')['attribution_day'].to_dict()
        df['attribution_day'] = df['sdk_yidun_device_id'].map(user2attribution)
        df['attribution_day'] = pd.to_datetime(df['attribution_day'])
        df['day'] = pd.to_datetime(df['day'])
        df_filter = df[(df['attribution_day'] <= df['day']) & (df['attribution_day'] + pd.Timedelta(days=2) >= df['day'])]
        df_filter['day_diff'] = (df_filter['day'] - df_filter['attribution_day']).dt.days
        
        value_columns = ['fish_gun_fire_sum_count', 'gun_level_up_consume_count', 
                        'recharge_count', 'fish_table_enter_count', 'skill_use_count',
                        'bkrpt_count', 'total_recharge_amount', 'login_count',
                        'shop_center_enter_count', 'achievement_reward_count',
                        'have_checkin_reward', 'startup_quest_finish_game_count',
                        'click_120215_count', 'click_120214_count', 'click_120093_count',
                        'click_120092_count', 'resource_total_down_count',
                        'resource_down_count', 'activity_midnight', 'activity_morning',
                        'activity_afternoon', 'activity_night', 
                        'game_time_in_minutes', 'final_delta', 'max_delta', 'min_delta',
                        'max_gunlevel', 'min_gunlevel', 'max_boss_rate', 'total_catch',
                        'total_catch_boss',]

        data_dict = defaultdict(dict)
        for _, row in df_filter.iterrows():
            key = (row['sdk_yidun_device_id'], row['attribution_day'])
            day_diff = row['day_diff']
            
            # 存储每个 day_diff 对应的属性值
            if key not in data_dict:
                data_dict[key] = {'sdk_yidun_device_id': row['sdk_yidun_device_id'], 'attribution_day': row['attribution_day'].strftime('%Y-%m-%d')}
                data_dict[key].update({"behav." + col + f'_{i}': 0 for i in range(3) for col in value_columns })
            
            # 生成后缀 col_0, col_1, col_2
            for col in value_columns:
                col_name = f"behav.{col}_{day_diff}"
                data_dict[key][col_name] = row[col]

        df_result = pd.DataFrame(list(data_dict.values()))
        return df_result

    
    def _load_data_from_csv(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """ 从数据库中获取任务名称和参数，加载并返回广告数据和行为数据 """
        try: 
            conn = MySQLConnector(user=config['mysql']['user'], 
                                    password=config['mysql']['password'], 
                                    host=config['mysql']['host'], 
                                    port=config['mysql']['port'], 
                                    db=config['mysql']['database'])
            conn.connect()
            query = f"select task_name, params from service_status where ds = '{self.ds}' and task_name = '{self.name}' and task_status = 'pending' "
            result = conn.execute_select(query)
            while not result:
                self.logger.info(f"waiting for {self.name} to be scheduled...")
                self.logger.info(f"query: {query}")
                conn.close()
                time.sleep(60)
                conn.connect()

                result = conn.execute_select(query)
                
            _, params = result[0]  # params为json字符串，其中behavior_data_path/campaign_data_path为key，对应的value记录了路径
            params = json.loads(params)
            campaign_data_path = params['campaign_data_path']
            behavior_data_path = params['behavior_data_path']
            
            if os.path.isdir(campaign_data_path):
                campaign_df = self._read_dataframe([os.path.join(campaign_data_path, file) for file in os.listdir(campaign_data_path)])
            elif os.path.isfile(campaign_data_path):
                campaign_df = self._read_dataframe([campaign_data_path])
            else:
                raise ValueError(f"campaign_data_path {campaign_data_path} is not a file or directory")
            
            if os.path.isdir(behavior_data_path):
                behavior_df = self._read_dataframe([os.path.join(behavior_data_path, file) for file in os.listdir(behavior_data_path)])
            elif os.path.isfile(behavior_data_path):
                behavior_df = self._read_dataframe([behavior_data_path])
            else:
                raise ValueError(f"behavior_data_path {behavior_data_path} is not a file or directory")
                
        except Exception as e:
            self.logger.error(f"load_data failed: {str(e)}", exc_info=True)
            raise e
        
        return campaign_df, behavior_df
    
    
    def _load_data_from_impala(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """ 从impala中获取数据 """
        conn = connect(
            host=config['impala']['proxy_host'],
            port=config['impala']['proxy_port'],
            auth_mechanism="NOSASL"
        )
        cursor = conn.cursor(user=config['impala']['user'])
        with open(os.path.join(project_root, 'misc/impala_advertisement_feature_20461.sql'), 'r') as f:
            sql_template = f.read()
            campaign_query = sql_template.format(attribution_day=self.attribution_ds)
        with open(os.path.join(project_root, 'misc/impala_user_behavior_10010.sql'), 'r') as f:
            sql_template = f.read()
            behavior_query = sql_template.format(start_ds=self.attribution_ds, end_ds=self.ds)
        
        cursor.execute("refresh koi_data.dwd_advertisement_feature_20461")
        cursor.execute(campaign_query)
        campaign_results = cursor.fetchall()
        column_names = [desc[0] for desc in cursor.description]
        campaign_df = pd.DataFrame(campaign_results, columns=column_names)
        
        cursor.execute('refresh koi_data.dwd_user_behavior_10010')
        cursor.execute(behavior_query)
        behavior_results = cursor.fetchall()
        column_names = [desc[0] for desc in cursor.description]
        behavior_df = pd.DataFrame(behavior_results, columns=column_names)
        
        return campaign_df, behavior_df
        
        
        
    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """ 加载数据 """
        # return self._load_data_from_csv()
        return self._load_data_from_impala()
    
    
    
    def data_preprocess(self, campaign_df: pd.DataFrame, behavior_df: pd.DataFrame) -> Tuple[List[List[str]], pd.DataFrame]:
        """
        verbose: 打印信息
        return: 
            id_list: 一系列id列表 [['sdk_yidun_device_id', 'attribution_day',  'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id'], ...]
            X: 用户特征
        """
        
        ### adtrace_platform
        for _, row in campaign_df.iterrows():
            self.user_adtrace_platform[row['sdk_yidun_device_id']] = self.adtrace_platform_map(str(row['adtrace_platform']))
        
        campaign_df = self._add_device_price(campaign_df)
        behavior_df = self._flatten_behavior_data(behavior_df, campaign_df)
        
        # 转换列类型
        d = dict(zip(self.model_metadata["feature_names"], self.model_metadata["feature_types"]))
        _map = {'object': str, 'int64': int, 'float64': float}
        converters = {k: _map[v] for k, v in d.items()}
        converters.update({'channel_ty_adgroup_id': str, 'channel_ty_account_id': str, 'channel_ty_campaign_id': str})
        
        for col, dtype in converters.items():
            if dtype in (int, float):
                if col in behavior_df.columns:
                    behavior_df[col] = pd.to_numeric(behavior_df[col], errors='coerce').fillna(-1).astype(dtype)
                if col in campaign_df.columns:
                    campaign_df[col] = pd.to_numeric(campaign_df[col], errors='coerce').fillna(-1).astype(dtype)
        
        full_df = pd.merge(campaign_df, behavior_df, on=('sdk_yidun_device_id', 'attribution_day'), how='left')
        full_df = full_df.drop(['behav.total_recharge_amount_2', 'behav.total_recharge_amount_1', 'behav.total_recharge_amount_0', 'd1_retention', 'd2_retention', 'd3_retention'], axis=1)
        
        future_payment_cols = [f'd{i}_payment' for i in range(4, 31)]
        future_retention_cols = [f'd{i}_retention' for i in range(4, 31)]
        drop_cols = ['sdk_yidun_device_id', 'attribution_day', 'attribution_day_a', 'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']
        X_columns = [c for c in full_df.columns.tolist() if c not in future_payment_cols + future_retention_cols]
        X = full_df[X_columns]
        
        # 处理缺失值、分桶
        obj_features = X.select_dtypes(include=['object']).columns.tolist()
        int_features = X.select_dtypes(include=['int']).columns.tolist()
        float_features = X.select_dtypes(include=['float']).columns.tolist()
        X[obj_features] = X[obj_features].fillna('unknown').astype(str)
        X[int_features] = X[int_features].fillna(-1).astype('int64')
        X[float_features] = X[float_features].fillna(-1).astype('float64')
        id_list = X[['sdk_yidun_device_id', 'attribution_day',  'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']].values.tolist()
        X.drop(columns=drop_cols, inplace=True, errors='ignore')
        extra_cols = list(set(X.columns.tolist()) - set(self.model_metadata["feature_names"]))
        X.drop(columns=extra_cols, inplace=True, errors='ignore')
        X = X[self.model_metadata["feature_names"]]  # 确保特征顺序一致
        
        cat_features = X.select_dtypes(include=['object']).columns.tolist()
        # numberical_features = X.select_dtypes(include=['number']).columns.tolist()
        text_features = ['channel_ua', 'adtrace_device_ua']
        
        return id_list, X, cat_features, text_features
    
    
    def verify_data(self, X: pd.DataFrame, **kwargs):
        """ 检查特征数量、名称、类型是否匹配
        metadata = {
            "feature_names": model.feature_names_,
            "feature_types": [str(train_X[col].dtype) for col in train_X.columns],
            "cat_features": cat_features,
            "text_features": text_features
        }
        """
        cat_features = kwargs.get("cat_features", [])
        text_features = kwargs.get("text_features", [])
        columns_types = [str(X[col].dtype) for col in X.columns]
        if list(X.columns) != self.model_metadata["feature_names"]:
            raise ValueError(f"特征名称不匹配\n"
                        f"预期：{self.model_metadata['feature_names']}\n"
                        f"实际：{list(X.columns)}\n")
            
        if columns_types != self.model_metadata["feature_types"]:
            mismatch_features = {}
            for i in range(len(self.model_metadata["feature_names"])):
                if self.model_metadata['feature_types'][i] != columns_types[i]:
                    mismatch_features[self.model_metadata['feature_names'][i]] = (self.model_metadata['feature_types'][i], columns_types[i])            
            raise ValueError(f"特征类型不匹配：{mismatch_features}\n")
        
        if cat_features != self.model_metadata["cat_features"]:
            raise ValueError(f"cat_features不匹配\n"
                             f"预期：{self.model_metadata['cat_features']}\n"
                             f"实际：{cat_features}\n")
        if text_features != self.model_metadata["text_features"]: 
            raise ValueError(f"text_features不匹配\n"
                             f"预期：{self.model_metadata['text_features']}\n"
                             f"实际：{text_features}\n")
        return True
    
    def predict(self, id_list: List[List[str]], X: pd.DataFrame) -> Tuple[Dict[str, float], Dict[str, float], Dict[str, int]]:
        """ 预测LTV """
        y_base = X[['d1_payment', 'd2_payment', 'd3_payment']].sum(axis=1)
        y_pred = self.model.predict(X) + y_base * 1.06 
        y_pred = np.maximum(y_pred, 0)
        y_pred = np.asarray(y_pred)
        y_base = np.asarray(y_base)
        
        predicted_user_ltv = defaultdict(float)
        predicted_campaign_ltv = defaultdict(float)
        campaign_user_counts = defaultdict(int)
        
        for i, (sdk_yidun_device_id, attribution_day,  channel_ty_account_id, channel_ty_adgroup_id, channel_ty_campaign_id) in enumerate(id_list):
            predicted_user_ltv[sdk_yidun_device_id] += y_pred[i] + y_base[i] 
            if channel_ty_adgroup_id not in ['unknown', '__DID__', '-1', 'nan', '']:
                predicted_campaign_ltv[channel_ty_adgroup_id] += y_pred[i] + y_base[i] 
                campaign_user_counts[channel_ty_adgroup_id] += 1
                # 增加广告ADTRACE_PLATFORM映射
                self.ad_adtrace_platform[channel_ty_adgroup_id] = self.user_adtrace_platform[sdk_yidun_device_id]
                
        return predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts
        
            
    def output(self, predicted_user_ltv: Dict[str, float], 
                predicted_campaign_ltv: Dict[str, float], 
                campaign_user_counts: Dict[str, int]): 
        """ 输出预测结果 """
        m = MySQLOutput()
        
        # 输出用户LTV预测值
        user_ltv_prediction_data = [
            {
                "sdk_yidun_device_id": sdk_yidun_device_id,
                "attribution_day": self.attribution_ds,
                "adtrace_platform": self.user_adtrace_platform[sdk_yidun_device_id],
                "observed_days": 3,
                "predicted_days": self.days,
                "pltv": pltv,
                "record_partition": self.ds,
            }
            for sdk_yidun_device_id, pltv in predicted_user_ltv.items()
        ]
        user_ltv_df = pd.DataFrame(user_ltv_prediction_data)
        # m.output_user_ltv_prediction(user_ltv_df, self.name)
        
        # 汇总用户LTV预测值，输出广告LTV预测值
        campaign_ltv_prediction_data = [
            {
                "channel_ty_adgroup_id": channel_ty_adgroup_id,
                "attribution_day": self.attribution_ds, # （当前仅包含一天）
                "adtrace_platform": self.ad_adtrace_platform[channel_ty_adgroup_id],
                "observed_days": 3,
                "predicted_days": self.days,
                "pltv": pltv,
                "user_count": campaign_user_counts[channel_ty_adgroup_id],
                "record_partition": self.ds,
            }
            for channel_ty_adgroup_id, pltv in predicted_campaign_ltv.items()
        ]
        campaign_ltv_df = pd.DataFrame(campaign_ltv_prediction_data)
        # m.output_ad_ltv_prediction(campaign_ltv_df, self.name)
        
    
    def execute(self) -> Dict[str, Any]:
        """执行完整流水线"""
        start_time = time.time()
        # 如果已经运行过则跳过
        if self.load_task_status(self.ds, self.name) != "pending":
            self.logger.info(f"pipeline {self.name} already run, skip")
            return {
                "status": "skip",
                "info": f"pipeline {self.name} already run, skip",
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
            }
            
        try:
            # 模型加载
            self.logger.info("model loading...")
            self._load_model()
            
            # 数据加载
            self.logger.info("data loading...")
            campaign_df, behavior_df = self.load_data()
            
            # 数据预处理
            self.logger.info("data preprocessing...")
            id_list, X, cat_features, text_features = self.data_preprocess(campaign_df, behavior_df)
            self.verify_data(X, cat_features=cat_features, text_features=text_features)
            
            # 执行预测
            self.logger.info("predicting...")
            predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts = self.predict(id_list, X)
            
            # 结果保存
            self.logger.info("saving results...")
            self.output(predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts)
            
            # 更新任务状态
            self.update_task_status(flag='success')
            
            # 收集执行结果
            result = {
                "status": "success",
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
                "info": f"len(predicted_user_ltv): {len(predicted_user_ltv)}, len(predicted_campaign_ltv): {len(predicted_campaign_ltv)}",
            }
            
        except Exception as e:
            self.logger.error(f"pipeline {self.name} failed: {str(e)}", exc_info=True)
            exc_type, exc_value, exc_traceback = sys.exc_info()
            tb = traceback.extract_tb(exc_traceback)[-1] 
            result = {
                "status": "failed",
                "error": str(e),
                "error_line": f'{tb.filename}:{tb.lineno}',
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
            }
            self.update_task_status(flag='failure')
            
        return result
    