import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from typing import Any, Dict, Type
from datetime import datetime, timedelta
import time
import traceback
import random
import os
import pandas as pd

from config import config
from utils.logger import setup_logger
from dataset.output import MySQLOutput
from utils.mysql import MySQLConnector


class PipelineBase:
    """ 流水线基类, 子类需要在execute中实现数据加载、数据预处理、模型预测、结果保存流程 
    必须要包含的参数:
        name: 流水线名称
        class_path: 流水线类路径
        log_file_path: 日志文件路径
    """
    
    def __init__(self, name: str, class_path: str, log_file_path: str, **kwargs):
        self.name = name
        self.log_file_path = log_file_path
        if not os.path.isabs(self.log_file_path):
            self.log_file_path = os.path.join(project_root, '..', self.log_file_path)
        self.logger = setup_logger(self.name, self.log_file_path)
        self.logger.info(f"pipeline {name} initialized")


    def update_task_status(self, flag: str):
        """ 更新任务状态 """
        conn = MySQLConnector(user=config['mysql']['user'], 
                            password=config['mysql']['password'], 
                            host=config['mysql']['host'], 
                            port=config['mysql']['port'], 
                            db=config['mysql']['database'])
        conn.connect()
        query = f"insert into service_status (ds, task_name, task_status) values ('{self.ds}', '{self.name}', '{flag}') on duplicate key update task_status = '{flag}'"
        conn.execute(query)
        conn.close()
    
    
    def load_task_status(self, ds, task_name) -> str:
        """ 加载任务状态 """
        conn = MySQLConnector(user=config['mysql']['user'], 
                            password=config['mysql']['password'], 
                            host=config['mysql']['host'], 
                            port=config['mysql']['port'], 
                            db=config['mysql']['database'])
        conn.connect()
        query = f"select task_status from service_status where ds = '{ds}' and task_name = '{task_name}'"
        result = conn.execute_select(query)
        conn.close()
        try:
            return result[0][0]
        except:
            return "pending"
    

    def execute(self) -> Dict[str, Any]:
        """ 执行流水线 
        返回值:
            Dict[str, Any]: 执行结果
        """
        raise NotImplementedError
    

class PipelineRandomGuess(PipelineBase):
    def __init__(self, name: str, class_path: str, log_file_path: str, **kwargs):
        super().__init__(name, class_path, log_file_path, **kwargs)
        self.today = datetime.now().date()
        self.days = 7
        self.users = []
        self.ads = []
        self.predicted_user_ltv = {}
        
    
    def load_data(self):
        """ 加载数据
        因为整体目标是预测广告的LTV，所以只需要加载短期内新建的广告和对应的用户数据
        假设当前为第T天结束时刻，加载[T-X, T]天的数据
        """
        # 随机生成数据
        self.ads = [
            {
                "ad_id": "ad_" + str(random.random())[2:],
                "ad_create_date": self.today - timedelta(days=random.randint(0, self.days)),
            }
            for _ in range(10)
        ]
        
        self.users = []
        for _ in range(300):
            ad = random.choice(self.ads)
            ad_id, ad_create_date = ad["ad_id"], ad["ad_create_date"]
            user = {
                "adtrace_id": f"user_{str(random.random())[2:]}", 
                "registration_date": ad_create_date + timedelta(days=(self.today - ad_create_date).days),
                "channel_ty_campaign_id": ad_id,
                "sdk_device_name": "device_" + str(random.random())[2:],
                "proj_main_channel": "channel_" + str(random.random())[2:],
                "adtrace_reattributed": random.randint(0, 1),
            }
            self.users.append(user)

    
    def predict(self):
        """ 预测LTV """
        for item in self.users:
            self.predicted_user_ltv[item["adtrace_id"]] = random.randint(0, 200)
        
    
    def output(self): 
        """ 输出预测结果 """
        m = MySQLOutput()
        
        # 输出用户LTV预测值
        user_ltv_prediction_data = [
            {
                "adtrace_id": item["adtrace_id"],
                "date_start": item["registration_date"],
                "date_end": item["registration_date"] + timedelta(days=self.days-1),
                "predicted_ltv": self.predicted_user_ltv[item["adtrace_id"]],
                "date_update": self.today,
            }
            for item in self.users  
        ]
        user_ltv_df = pd.DataFrame(user_ltv_prediction_data)
        m.output_user_ltv_prediction(user_ltv_df, "test")
        
        # 汇总用户LTV预测值，输出广告LTV预测值
        ad_ltv_prediction_data = []
        for ad in self.ads:
            ad_id, ad_create_date = ad["ad_id"], ad["ad_create_date"]
            ad_users = [item['adtrace_id'] for item in self.users if item["channel_ty_campaign_id"] == ad_id and self.today >= item["registration_date"] >= ad_create_date]
            predicts = [self.predicted_user_ltv[user_id]  for user_id in ad_users]
            d = {
                "ad_id": ad_id,
                "date_start": ad_create_date,
                "date_end": self.today,
                "ltv_days": self.days,
                "predicted_ltv": sum(predicts),
                "user_count": len(predicts),
                "date_update": self.today,
            }
            ad_ltv_prediction_data.append(d)
    
        ad_ltv_df = pd.DataFrame(ad_ltv_prediction_data)
        m.output_ad_ltv_prediction(ad_ltv_df, "test")
        
            
    def execute(self) -> Dict[str, Any]:
        """执行完整流水线"""
        start_time = time.time()
        try:
            # 数据加载
            self.logger.info("data loading...")
            self.load_data()
            
            # 数据预处理
            self.logger.info("data preprocessing...")
            
            # 执行预测
            self.logger.info("predicting...")
            self.predict()
            
            # 结果保存
            self.logger.info("saving results...")
            self.output()
            
            # 收集执行结果
            result = {
                "status": "success",
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
                "info": f"len(predicted_user_ltv) : {len(self.predicted_user_ltv)}",
            }
            
        except Exception as e:
            self.logger.error(f"pipeline {self.name} failed: {str(e)}", exc_info=True)
            exc_type, exc_value, exc_traceback = sys.exc_info()
            tb = traceback.extract_tb(exc_traceback)[-1] 
            result = {
                "status": "failed",
                "error": str(e),
                "error_line": f'{tb.filename}:{tb.lineno}',
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
            }
            
        return result
    