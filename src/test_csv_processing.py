#!/usr/bin/env python3
"""
测试CSV数据处理流程
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from dataset.generate_from_csv import load_csv_data, process_csv_data
from dataset.process_csv_behavior import extract_behavior_features, create_behavior_sequences


def test_csv_loading():
    """测试CSV文件加载"""
    print("=" * 50)
    print("Testing CSV Loading")
    print("=" * 50)
    
    # 查找CSV文件
    csv_files = [
        "../csv_data/subtask_2624_20250715105822.csv",
        "../csv_data/subtask_2625_20250716120249.csv"
    ]
    
    csv_file = None
    for file_path in csv_files:
        if os.path.exists(file_path):
            csv_file = file_path
            break
    
    if csv_file is None:
        print("No CSV files found for testing")
        return False
    
    try:
        # 测试加载CSV数据
        campaign_df, behavior_df = load_csv_data(csv_file)
        
        print(f"✓ CSV loading successful")
        print(f"  Campaign data shape: {campaign_df.shape}")
        if behavior_df is not None:
            print(f"  Behavior data shape: {behavior_df.shape}")
        else:
            print(f"  No behavior data found")
        
        # 检查关键列
        required_cols = ['sdk_yidun_device_id', 'attribution_day', 'channel_ty_adgroup_id']
        missing_cols = [col for col in required_cols if col not in campaign_df.columns]
        
        if missing_cols:
            print(f"✗ Missing required columns: {missing_cols}")
            return False
        else:
            print(f"✓ All required columns present")
        
        return True
        
    except Exception as e:
        print(f"✗ CSV loading failed: {e}")
        return False


def test_behavior_extraction():
    """测试行为特征提取"""
    print("\n" + "=" * 50)
    print("Testing Behavior Feature Extraction")
    print("=" * 50)
    
    # 查找CSV文件
    csv_files = [
        "../csv_data/subtask_2624_20250715105822.csv",
        "../csv_data/subtask_2625_20250716120249.csv"
    ]
    
    csv_file = None
    for file_path in csv_files:
        if os.path.exists(file_path):
            csv_file = file_path
            break
    
    if csv_file is None:
        print("No CSV files found for testing")
        return False
    
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file)
        print(f"Loaded {len(df)} rows from CSV")
        
        # 测试行为特征提取
        behavior_df = extract_behavior_features(df)
        
        if len(behavior_df) > 0:
            print(f"✓ Behavior feature extraction successful")
            print(f"  Behavior data shape: {behavior_df.shape}")
            print(f"  Behavior columns: {list(behavior_df.columns)}")
            
            # 检查是否有行为数据
            behavior_cols = [col for col in behavior_df.columns if col not in ['sdk_yidun_device_id', 'attribution_day']]
            if behavior_cols:
                print(f"  Found {len(behavior_cols)} behavior features")
            else:
                print(f"  No behavior features found")
            
            return True
        else:
            print(f"✗ No behavior data extracted")
            return False
        
    except Exception as e:
        print(f"✗ Behavior extraction failed: {e}")
        return False


def test_data_processing():
    """测试完整的数据处理流程"""
    print("\n" + "=" * 50)
    print("Testing Complete Data Processing")
    print("=" * 50)
    
    # 查找CSV文件
    csv_files = [
        "../csv_data/subtask_2624_20250715105822.csv",
        "../csv_data/subtask_2625_20250716120249.csv"
    ]
    
    csv_file = None
    for file_path in csv_files:
        if os.path.exists(file_path):
            csv_file = file_path
            break
    
    if csv_file is None:
        print("No CSV files found for testing")
        return False
    
    try:
        # 测试完整的数据处理流程
        output_dir = "../data/test_output"
        merged_df = process_csv_data(csv_file, output_dir, new_data=True)
        
        if merged_df is not None and len(merged_df) > 0:
            print(f"✓ Complete data processing successful")
            print(f"  Final data shape: {merged_df.shape}")
            print(f"  Output directory: {output_dir}")
            
            # 检查输出文件
            pickle_path = os.path.join(output_dir, 'any_day_data.pkl')
            encoding_path = os.path.join(output_dir, 'encoding_map.json')
            
            if os.path.exists(pickle_path):
                print(f"✓ Pickle file created: {pickle_path}")
            else:
                print(f"✗ Pickle file not created")
            
            if os.path.exists(encoding_path):
                print(f"✓ Encoding map created: {encoding_path}")
            else:
                print(f"✗ Encoding map not created")
            
            return True
        else:
            print(f"✗ Data processing failed: Empty dataframe")
            return False
        
    except Exception as e:
        print(f"✗ Data processing failed: {e}")
        return False


def test_small_sample():
    """测试小样本数据处理"""
    print("\n" + "=" * 50)
    print("Testing Small Sample Processing")
    print("=" * 50)
    
    # 查找CSV文件
    csv_files = [
        "../csv_data/subtask_2624_20250715105822.csv",
        "../csv_data/subtask_2625_20250716120249.csv"
    ]
    
    csv_file = None
    for file_path in csv_files:
        if os.path.exists(file_path):
            csv_file = file_path
            break
    
    if csv_file is None:
        print("No CSV files found for testing")
        return False
    
    try:
        # 读取小样本数据
        df = pd.read_csv(csv_file, nrows=1000)  # 只读取前1000行
        print(f"Loaded {len(df)} rows as sample")
        
        # 保存小样本
        sample_file = "../csv_data/sample_data.csv"
        df.to_csv(sample_file, index=False)
        print(f"Saved sample to: {sample_file}")
        
        # 处理小样本
        output_dir = "../data/sample_output"
        merged_df = process_csv_data(sample_file, output_dir, new_data=True)
        
        if merged_df is not None and len(merged_df) > 0:
            print(f"✓ Sample processing successful")
            print(f"  Sample data shape: {merged_df.shape}")
            return True
        else:
            print(f"✗ Sample processing failed")
            return False
        
    except Exception as e:
        print(f"✗ Sample processing failed: {e}")
        return False


def main():
    """主测试函数"""
    print("Starting CSV Processing Tests")
    print("=" * 60)
    
    tests = [
        ("CSV Loading", test_csv_loading),
        ("Behavior Extraction", test_behavior_extraction),
        ("Complete Processing", test_data_processing),
        ("Small Sample", test_small_sample),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # 打印测试结果
    print("\n" + "=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        print("\nYou can now proceed with training:")
        print("python run_csv_training.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return passed == total


if __name__ == "__main__":
    main() 