#!/usr/bin/env python3
"""
测试通用CSV数据处理流程
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from dataset.generate_from_csv_new import (
    analyze_csv_structure, 
    load_csv_data_generic, 
    preprocess_data_generic,
    create_behavior_sequences_generic,
    process_csv_data_generic
)


def test_csv_analysis():
    """测试CSV文件分析"""
    print("=" * 50)
    print("Testing CSV Analysis")
    print("=" * 50)
    
    # 查找CSV文件
    csv_files = [
        "../csv_data/subtask_2624_20250715105822.csv",
        "../csv_data/subtask_2625_20250716120249.csv"
    ]
    
    csv_file = None
    for file_path in csv_files:
        if os.path.exists(file_path):
            csv_file = file_path
            break
    
    if csv_file is None:
        print("No CSV files found for testing")
        return False
    
    try:
        # 测试CSV结构分析
        analysis = analyze_csv_structure(csv_file)
        
        print(f"✓ CSV analysis successful")
        print(f"  Total columns: {analysis['total_columns']}")
        print(f"  Key columns: {analysis['key_columns']}")
        print(f"  Categorical columns: {len(analysis['categorical_columns'])}")
        print(f"  Numerical columns: {len(analysis['numerical_columns'])}")
        print(f"  Date columns: {analysis['date_columns']}")
        print(f"  Target columns: {analysis['target_columns']}")
        print(f"  Behavior columns: {len(analysis['behavior_columns'])}")
        
        # 检查是否有必要的列
        if not analysis['key_columns']:
            print("✗ No key columns found")
            return False
        
        if not analysis['date_columns']:
            print("✗ No date columns found")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ CSV analysis failed: {e}")
        return False


def test_data_loading():
    """测试数据加载"""
    print("\n" + "=" * 50)
    print("Testing Data Loading")
    print("=" * 50)
    
    # 查找CSV文件
    csv_files = [
        "../csv_data/subtask_2624_20250715105822.csv",
        "../csv_data/subtask_2625_20250716120249.csv"
    ]
    
    csv_file = None
    for file_path in csv_files:
        if os.path.exists(file_path):
            csv_file = file_path
            break
    
    if csv_file is None:
        print("No CSV files found for testing")
        return False
    
    try:
        # 测试数据加载
        df, analysis = load_csv_data_generic(csv_file)
        
        print(f"✓ Data loading successful")
        print(f"  Data shape: {df.shape}")
        print(f"  Total rows: {analysis['total_rows']}")
        
        # 检查数据完整性
        if len(df) == 0:
            print("✗ Empty dataframe")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Data loading failed: {e}")
        return False


def test_data_preprocessing():
    """测试数据预处理"""
    print("\n" + "=" * 50)
    print("Testing Data Preprocessing")
    print("=" * 50)
    
    # 查找CSV文件
    csv_files = [
        "../csv_data/subtask_2624_20250715105822.csv",
        "../csv_data/subtask_2625_20250716120249.csv"
    ]
    
    csv_file = None
    for file_path in csv_files:
        if os.path.exists(file_path):
            csv_file = file_path
            break
    
    if csv_file is None:
        print("No CSV files found for testing")
        return False
    
    try:
        # 加载数据
        df, analysis = load_csv_data_generic(csv_file)
        
        # 测试数据预处理
        df_processed = preprocess_data_generic(df, analysis)
        
        print(f"✓ Data preprocessing successful")
        print(f"  Original shape: {df.shape}")
        print(f"  Processed shape: {df_processed.shape}")
        
        # 检查预处理结果
        if df_processed.shape != df.shape:
            print("✗ Shape changed during preprocessing")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Data preprocessing failed: {e}")
        return False


def test_behavior_sequences():
    """测试行为序列创建"""
    print("\n" + "=" * 50)
    print("Testing Behavior Sequence Creation")
    print("=" * 50)
    
    # 查找CSV文件
    csv_files = [
        "../csv_data/subtask_2624_20250715105822.csv",
        "../csv_data/subtask_2625_20250716120249.csv"
    ]
    
    csv_file = None
    for file_path in csv_files:
        if os.path.exists(file_path):
            csv_file = file_path
            break
    
    if csv_file is None:
        print("No CSV files found for testing")
        return False
    
    try:
        # 加载和预处理数据
        df, analysis = load_csv_data_generic(csv_file)
        df_processed = preprocess_data_generic(df, analysis)
        
        # 测试行为序列创建
        df_with_sequences = create_behavior_sequences_generic(df_processed, analysis, max_days=14)
        
        print(f"✓ Behavior sequence creation successful")
        print(f"  Original shape: {df_processed.shape}")
        print(f"  With sequences shape: {df_with_sequences.shape}")
        
        # 检查是否有行为序列列
        if 'behavior_sequence' in df_with_sequences.columns:
            print(f"  Behavior sequence column found")
            return True
        else:
            print(f"  No behavior sequence column created (may be normal if no behavior columns)")
            return True
        
    except Exception as e:
        print(f"✗ Behavior sequence creation failed: {e}")
        return False


def test_complete_processing():
    """测试完整的数据处理流程"""
    print("\n" + "=" * 50)
    print("Testing Complete Data Processing")
    print("=" * 50)
    
    # 查找CSV文件
    csv_files = [
        "../csv_data/subtask_2624_20250715105822.csv",
        "../csv_data/subtask_2625_20250716120249.csv"
    ]
    
    csv_file = None
    for file_path in csv_files:
        if os.path.exists(file_path):
            csv_file = file_path
            break
    
    if csv_file is None:
        print("No CSV files found for testing")
        return False
    
    try:
        # 测试完整的数据处理流程
        output_dir = "../data/test_output_generic"
        merged_df, analysis = process_csv_data_generic(csv_file, output_dir, new_data=True, max_days=14)
        
        if merged_df is not None and len(merged_df) > 0:
            print(f"✓ Complete data processing successful")
            print(f"  Final data shape: {merged_df.shape}")
            print(f"  Output directory: {output_dir}")
            
            # 检查输出文件
            pickle_path = os.path.join(output_dir, 'any_day_data.pkl')
            encoding_path = os.path.join(output_dir, 'encoding_map.json')
            analysis_path = os.path.join(output_dir, 'data_analysis.json')
            
            if os.path.exists(pickle_path):
                print(f"✓ Pickle file created: {pickle_path}")
            else:
                print(f"✗ Pickle file not created")
            
            if os.path.exists(encoding_path):
                print(f"✓ Encoding map created: {encoding_path}")
            else:
                print(f"✗ Encoding map not created")
            
            if os.path.exists(analysis_path):
                print(f"✓ Data analysis created: {analysis_path}")
            else:
                print(f"✗ Data analysis not created")
            
            return True
        else:
            print(f"✗ Data processing failed: Empty dataframe")
            return False
        
    except Exception as e:
        print(f"✗ Data processing failed: {e}")
        return False


def test_small_sample():
    """测试小样本数据处理"""
    print("\n" + "=" * 50)
    print("Testing Small Sample Processing")
    print("=" * 50)
    
    # 查找CSV文件
    csv_files = [
        "../csv_data/subtask_2624_20250715105822.csv",
        "../csv_data/subtask_2625_20250716120249.csv"
    ]
    
    csv_file = None
    for file_path in csv_files:
        if os.path.exists(file_path):
            csv_file = file_path
            break
    
    if csv_file is None:
        print("No CSV files found for testing")
        return False
    
    try:
        # 读取小样本数据
        df = pd.read_csv(csv_file, nrows=1000)  # 只读取前1000行
        print(f"Loaded {len(df)} rows as sample")
        
        # 保存小样本
        sample_file = "../csv_data/sample_data_generic.csv"
        df.to_csv(sample_file, index=False)
        print(f"Saved sample to: {sample_file}")
        
        # 处理小样本
        output_dir = "../data/sample_output_generic"
        merged_df, analysis = process_csv_data_generic(sample_file, output_dir, new_data=True, max_days=14)
        
        if merged_df is not None and len(merged_df) > 0:
            print(f"✓ Sample processing successful")
            print(f"  Sample data shape: {merged_df.shape}")
            return True
        else:
            print(f"✗ Sample processing failed")
            return False
        
    except Exception as e:
        print(f"✗ Sample processing failed: {e}")
        return False


def main():
    """主测试函数"""
    print("Starting Generic CSV Processing Tests")
    print("=" * 60)
    
    tests = [
        ("CSV Analysis", test_csv_analysis),
        ("Data Loading", test_data_loading),
        ("Data Preprocessing", test_data_preprocessing),
        ("Behavior Sequences", test_behavior_sequences),
        ("Complete Processing", test_complete_processing),
        ("Small Sample", test_small_sample),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # 打印测试结果
    print("\n" + "=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        print("\nYou can now proceed with training:")
        print("python train_from_csv_generic.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return passed == total


if __name__ == "__main__":
    main() 