import os
from datetime import datetime
from pprint import pprint

from sklearn.model_selection import train_test_split
import torch
from torch.utils.data import DataLoader
import pytorch_lightning as pl
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.callbacks import EarlyStopping
from pytorch_lightning.profilers import SimpleProfiler

from dataset.light_data_any_day_prediction import AnyDayDataModule
from model.torch_model_any_day_prediction import AnyDayModel



class GradientLogger(pl.Callback):
    def on_after_backward(self, trainer, pl_module):
        print("Debug Gradients (Max Grad > 10):")
        for name, param in pl_module.named_parameters():
            if param.requires_grad and param.grad is not None:
                max_grad = param.grad.max().item()
                if max_grad > 10:
                    print(f"Layer: {name}")
                    print(
                        f"Mean Grad: {param.grad.mean().item():.4f}, Std Grad: {param.grad.std().item():.4f}, " +
                        f"Max Grad: {max_grad:.4f}, Min Grad: {param.grad.min().item():.4f}\n")


if __name__ == "__main__":
    dm = AnyDayDataModule(
        pickle_path = os.path.join(os.path.dirname(__file__), '../data/any_day_data.pkl'),
        batch_size=2048,
        num_workers=6,
    )
    dm.setup()
    # baseline_matrix = [[dm.payment_cumsum_mean[i] / dm.payment_cumsum_mean[j] for j in range(i+1)] for i in range(len(dm.payment_cumsum_mean))]
    # pprint(baseline_matrix)
    model = AnyDayModel(hidden_dim=64, 
                        payment_cumsum_mean=dm.payment_cumsum_mean,
                        weight_decay=1e-3,
                        use_mse=False,
                        fixed_sigma=1)
    # gradient_logger = GradientLogger()
    # early_stop_callback = EarlyStopping(
    #     monitor="val_loss", 
    #     mode="min",  
    #     patience=15, 
    #     verbose=True  
    # )
    logger = TensorBoardLogger(
        save_dir=os.path.join(os.path.dirname(__file__), '../logs/'),
        name='adp',
        version=f"v1-{datetime.now().strftime('%m%d-%H%M')}",
        default_hp_metric=False,
    )
    logger.experiment.add_text("description", str(model.hparams))
    
    trainer = pl.Trainer(
        max_epochs=500,
        devices=[0],
        accelerator="gpu",
        profiler="simple",
        logger=logger,
        log_every_n_steps=1,
        check_val_every_n_epoch=5,
        # num_sanity_val_steps=0, 
        # callbacks=[early_stop_callback],
        # gradient_clip_val=0.1,  # 将梯度裁剪阈值设置为适合你模型的值
        # gradient_clip_algorithm="norm",  # 可选 "norm" (默认) 或 "value"
    )
    
    trainer.fit(model, datamodule=dm)
