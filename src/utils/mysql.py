import traceback
import pymysql
from typing import List, Tuple

class MySQLConnector:
    def __init__(self, user, password, db, host, port):
        self.user = user
        self.password = password
        self.db = db
        self.host = host
        self.port = port
        self.connection = None

    def connect(self):
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.db,
                connect_timeout=600,
                charset='utf8'
            )
        except Exception as e:
            print(f'{e}, {traceback.format_exc()}')
            self.connection = None
            print("Connection to MySQL DB failed")

    def close(self):
        try:
            self.connection.close()
            self.connection = None
        except Exception as e:
            print(f'{e}, {traceback.format_exc()}')
            print("Connection to MySQL DB failed to close")
            raise e

    def execute(self, sql: str):
        with self.connection.cursor() as cursor:
            try:
                cursor.execute(sql)
                self.connection.commit()
            except Exception as e:
                print(f'{e}, {traceback.format_exc()}')
                self.connection.rollback()
                raise e

    def execute_select(self, sql: str):
        with self.connection.cursor() as cursor:
            cursor.execute(sql)
            result = cursor.fetchall()
            return result

    def execute_query_and_create_partition(self, table_name: str, partition_name: str, value: str):
        sql_check_partition = f"""
        SELECT PARTITION_NAME
        FROM INFORMATION_SCHEMA.PARTITIONS
        WHERE TABLE_SCHEMA = '{self.db}' AND TABLE_NAME = '{table_name}' AND PARTITION_NAME = '{partition_name}'
        """
        cursor = self.connection.cursor()
        cursor.execute(sql_check_partition)
        result = cursor.fetchone()
        if result:
            print(f"Partition {partition_name} already exists")
        else:
            sql_add_partition = f"""
            ALTER TABLE {table_name} 
            ADD PARTITION (PARTITION {partition_name} VALUES IN ('{value}'))
            """
            cursor.execute(sql_add_partition)
            self.connection.commit()
            print(f"Partition {partition_name} added successfully")

    def execute_insert(self, table_name: str, columns: List[str], values: List[Tuple]):
        with self.connection.cursor() as cursor:
            sql = f"INSERT INTO {table_name} ({','.join(columns)}) VALUES ({','.join(['%s'] * len(columns))})"
            try:
                cursor.executemany(sql, values)
                self.connection.commit()
            except Exception as e:
                print(f'{e}, {traceback.format_exc()}')
                self.connection.rollback()
                raise e

    def execute_update(self, table_name: str, key_columns: List[str], update_columns: List[str], key_values: List[Tuple], update_values: List[Tuple]):
        assert len(key_columns) > 0 and len(update_columns) > 0
        with self.connection.cursor() as cursor:
            kvs = ','.join([f'{key}=%s' for key in update_columns])
            conditions = ' and '.join([f'{key}=%s' for key in key_columns])
            sql = 'update {table} set {kvs} where {conditions}'.format(
                table=table_name, kvs=kvs, conditions=conditions)
            data = [update_values[i] + key_values[i] for i in range(len(update_values))]
            try:
                cursor.executemany(sql, data)
                self.connection.commit()
            except Exception as e:
                print(f'{e}, {traceback.format_exc()}')
                self.connection.rollback()
                raise e

    def execute_insert_update(self, table_name: str, columns: List[str], values: List[Tuple], update_columns: List[str]):
        with self.connection.cursor() as cursor:
            cols = ','.join(columns)
            holders = ','.join(['%s'] * len(columns))
            update = ",".join(["{c} = VALUES({c})".format(c=c) for c in update_columns]) if update_columns else ""
            sql = 'insert into {table} ({cols}) values ({holders}) ON DUPLICATE KEY UPDATE {update}'.format(
                table=table_name, cols=cols, holders=holders, update=update
            )
            try:
                cursor.executemany(sql, values)
                self.connection.commit()
            except Exception as e:
                print(f'{e}, {traceback.format_exc()}')
                self.connection.rollback()
                raise e

