import os
import logging
from logging.handlers import TimedRotatingFileHand<PERSON>

def setup_logger(name, log_file_path, level=logging.INFO, add_d=False):
    logger = logging.getLogger(name)
    
    if not logger.handlers:
        logger.setLevel(level)

        SIMPLE_LOG_FORMAT = '%(asctime)s %(levelname)s %(message)s'
        formatter = logging.Formatter(SIMPLE_LOG_FORMAT)

        # 输出到控制台
        console_handler = logging.StreamHandler()

        # 输出到文件
        dirpath = os.path.dirname(log_file_path)
        dirpath = '.' if dirpath == '' else dirpath
        os.makedirs(dirpath, exist_ok=True)
        if add_d:
            handler = TimedRotatingFileHandler(filename=log_file_path, when='midnight', interval=1)
            handler.suffix = '%Y-%m-%d'
        else:
            handler = logging.FileHandler(log_file_path)
            
        handler.setFormatter(formatter)
        handler.setLevel(level=level)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(level=level)   
        
        logger.addHandler(handler)
        logger.addHandler(console_handler)

    return logger
