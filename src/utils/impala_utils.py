import sys
import os

from datetime import datetime, timedelta
from typing import List, <PERSON><PERSON>, Any, Dict, Type
import numpy as np
import pandas as pd
from tqdm import tqdm
from impala.dbapi import connect
from collections import defaultdict
from config import config

def load_any_day_data_from_impala_inference(run_day, max_length = 14) -> <PERSON><PERSON>[pd.DataFrame, pd.DataFrame]:
    
    print(f"Please Check Reading Data for Inference......")
    
    """ 从impala中获取数据 """
    conn = connect(
        host=config['impala']['proxy_host'],
        port=config['impala']['proxy_port'],
        auth_mechanism="NOSASL"
    )
    cursor = conn.cursor(user=config['impala']['user'])
    
    start_date = (datetime.strptime(run_day, "%Y-%m-%d") - timedelta(days=max_length-1))
    end_date = (datetime.strptime(run_day, "%Y-%m-%d") - timedelta(days=1))
    # 生成日期列表
    date_list = []
    current = start_date
    while current <= end_date:
        date_list.append(current.strftime("%Y-%m-%d"))
        current += timedelta(days=1)

    print(date_list)

    campaign_df_list = []
    print("Loading Campaign Data")
    for attribution_day_ in tqdm(date_list):
        with open('../src/misc/impala_advertisement_feature_20461.sql', 'r') as f:
            sql_template = f.read()
            campaign_query = sql_template.format(attribution_day=attribution_day_)
            cursor.execute("refresh koi_data.dwd_advertisement_feature_20461")
            cursor.execute(campaign_query)
            campaign_results = cursor.fetchall()
            column_names = [desc[0] for desc in cursor.description]
            campaign_df_ = pd.DataFrame(campaign_results, columns=column_names)
            campaign_df_list.append(campaign_df_)
    
    campaign_df = pd.concat(campaign_df_list)
    
    print("Loading Behavior Data, This May Take a While......")
    print(start_date)
    print(end_date)
    with open(f"../src/misc/impala_user_behavior_10010.sql", 'r') as f:
        sql_template = f.read()
        behavior_query = sql_template.format(start_ds=start_date.strftime("%Y-%m-%d"), end_ds=end_date.strftime("%Y-%m-%d"))

    cursor.execute(f"refresh koi_data.dwd_user_behavior_10010")
    cursor.execute(behavior_query)
    behavior_results = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]
    behavior_df = pd.DataFrame(behavior_results, columns=column_names).fillna(0)
    
    return campaign_df, behavior_df

def load_any_day_data_from_impala(attribution_ds, ds) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """ 从impala中获取数据 """
    conn = connect(
        host=config['impala']['proxy_host'],
        port=config['impala']['proxy_port'],
        auth_mechanism="NOSASL"
    )
    cursor = conn.cursor(user=config['impala']['user'])
    
    date1 = datetime.strptime(attribution_ds, "%Y-%m-%d")
    date2 = datetime.strptime(ds, "%Y-%m-%d")

    # 计算天数差并断言
    assert abs((date2 - date1).days) >= 14, "日期间隔不超过14天"
    
    start_date = datetime.strptime(attribution_ds, "%Y-%m-%d")
    end_date = datetime.strptime(ds, "%Y-%m-%d") - timedelta(days=14)

    # 生成日期列表
    date_list = []
    current = start_date
    while current <= end_date:
        date_list.append(current.strftime("%Y-%m-%d"))
        current += timedelta(days=1)

    print(date_list)

    campaign_df_list = []
    print("Loading Campaign Data")
    for attribution_day_ in tqdm(date_list):
        with open('../src/misc/impala_advertisement_feature_20461.sql', 'r') as f:
            sql_template = f.read()
            campaign_query = sql_template.format(attribution_day=attribution_day_)
            cursor.execute("refresh koi_data.dwd_advertisement_feature_20461")
            cursor.execute(campaign_query)
            campaign_results = cursor.fetchall()
            column_names = [desc[0] for desc in cursor.description]
            campaign_df_ = pd.DataFrame(campaign_results, columns=column_names)
            campaign_df_list.append(campaign_df_)
    
    campaign_df = pd.concat(campaign_df_list)
    
    print("Loading Behavior Data, This May Take a While......")
    with open('../src/misc/impala_user_behavior_10010.sql', 'r') as f:
        sql_template = f.read()
        behavior_query = sql_template.format(start_ds=attribution_ds, end_ds=ds)

    cursor.execute('refresh koi_data.dwd_user_behavior_10010')
    cursor.execute(behavior_query)
    behavior_results = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]
    behavior_df = pd.DataFrame(behavior_results, columns=column_names)
    
    return campaign_df, behavior_df

def add_device_price(df):
    import pandas as pd
    device2price = pd.read_csv('../data/device2price.csv')
    df_with_price = pd.merge(
        df,
        device2price,
        left_on='sdk_device_name',
        right_on='device_name', 
        how='left' 
    )
    df_with_price = df_with_price.rename(columns={'price': 'device_price'})
    return df_with_price

def analyze_column(df, verbose=True):
    """ 区分列名
    ignore_cols: 忽略
    X_cols: 输入特征
    y_cols: 输出目标
    key_cols: ID等信息
    """
    ignore_cols, X_cols = [], []
    key_cols = []
    for column in df.columns:
        distinct_vals = df[column].nunique()
        missing_vals = df[column].isna().sum()
        if column in ('sdk_yidun_device_id', 'attribution_day', 'adtrace_aid', 'proj_main_channel', 
                    'channel_ty_adgroup_id', 'channel_ty_account_id', 'channel_ty_campaign_id', 'channel_ty_csite_id'):
            key_cols.append(column)
        elif distinct_vals in (0, 1) or missing_vals == df.shape[0]:  # 全部相同 或 全部缺失
            ignore_cols.append(column)
        elif df[column].dtype == 'object' and (distinct_vals + missing_vals > 0.7 * df.shape[0] or distinct_vals > 0.2 * df.shape[0]):  # obj类型过多
            ignore_cols.append(column) 
        elif column in ('ingest_time', 'event_time', 'sdk_sysfiletime', 'adtrace_last_active_time', 'adtrace_missionid', 'adtrace_attribution_time', 
                        'adtrace_pay_times_boundary', 'sdk_boottimeinsec', 'adtrace_yidun_last_active_time'): # ignore time
            ignore_cols.append(column)
        elif column in ('adtrace_yidun_validate_message', ): #  人工过滤
            ignore_cols.append(column)
        elif column.endswith('payment'):
            X_cols.append(column)
        else:
            if verbose:
                print(f"Column: {column}")
                print(f"  Data type: {df[column].dtype}")
                print(f"  Distinct values: {distinct_vals}")
                print(f"  Missing values: {missing_vals}")
                print(f"  sample: ")
                print(f"{df[column].head(5)}")
                print("-" * 40)  # 分隔线
            X_cols.append(column)
    return key_cols, X_cols, ignore_cols

def get_behavior_data_list(df, df_with_attribution):
    user2attribution = df_with_attribution.set_index('sdk_yidun_device_id')['attribution_day'].to_dict()
    df['attribution_day'] = df['sdk_yidun_device_id'].map(user2attribution)
    df['attribution_day'] = pd.to_datetime(df['attribution_day'])
    df['day'] = pd.to_datetime(df['day'])
    df_filter = df[(df['attribution_day'] <= df['day']) & (df['attribution_day'] + pd.Timedelta(days=13) >= df['day'])].copy()
    df_filter['day_diff'] = (df_filter['day'] - df_filter['attribution_day']).dt.days 
    # print(df_filter.columns)
    value_columns = ['fish_gun_fire_sum_count', 'gun_level_up_consume_count', 
                        'recharge_count', 'fish_table_enter_count', 'skill_use_count',
                        'bkrpt_count', 'total_recharge_amount', 'login_count',
                        'shop_center_enter_count', 'achievement_reward_count',
                        'have_checkin_reward', 'startup_quest_finish_game_count',
                        'click_120215_count', 'click_120214_count', 'click_120093_count',
                        'click_120092_count', 'resource_total_down_count',
                        'resource_down_count', 'activity_midnight', 'activity_morning',
                        'activity_afternoon', 'activity_night', 
                        'game_time_in_minutes', 'final_delta', 'max_delta', 'min_delta',
                        'max_gunlevel', 'min_gunlevel', 'max_boss_rate', 'total_catch',
                        'total_catch_boss',]

    data_dict = defaultdict(dict)
    df_filter = df_filter.sort_values('day')
    for _, row in df_filter.iterrows():
        key = (row['sdk_yidun_device_id'], row['attribution_day'])
        day_diff = row['day_diff']
        
        # 存储每个 day_diff 对应的属性值
        if key not in data_dict:
            data_dict[key] = {'sdk_yidun_device_id': row['sdk_yidun_device_id'], 'attribution_day': row['attribution_day'].strftime('%Y-%m-%d')}
            data_dict[key].update({"behav." + col: 14 * [0.0] for col in value_columns })
        
        # 生成后缀 col_0, col_1
        for col in value_columns:
            col_name = f"behav.{col}"
            data_dict[key][col_name][day_diff] = float(row[col])

    df_result = pd.DataFrame(list(data_dict.values()))
    return df_result

