WITH 
attribution_data AS (
    SELECT
    sdk_yidun_device_id, day as attribution_day, channel_ty_campaign_id, channel_ty_account_id, 
    adtrace_organic_traffic, sdk_mntid, adtrace_attribution_mode, sdk_os_name, adtrace_pay_times, 
    adtrace_click_match_count, sdk_fileinittime, sdk_devicename, sdk_virtual_channel_version, click_time, 
    sdk_language, adtrace_orderid, channel_ty_adgroup_id, adtrace_namespace, adtrace_yidun_validate, 
    adtrace_is_greylist, sdk_hardware_name, adtrace_assist_platform, adtrace_ctit, sdk_device_name,  
    channel_ty_csite_id, sdk_carrierinfo, sdk_systemversion, proj_project_id, channel_ty_word_id, 
    adtrace_act_name, adtrace_is_blacklist, adtrace_device_ua, sdk_proj_app_id,  
    adtrace_platform, proj_main_channel, sdk_split_ua_result, sdk_timezone, adtrace_device_caid_main_version, 
    proj_sub_channel, sdk_os_version, proj_client_id, sdk_virtual_channel, sdk_package_name, proj_cloud_id, 
    sdk_disk, proj_virtual_channel, sdk_memory, proj_game_type, adtrace_reattributed, sdk_machine, 
    sdk_model, channel_ua, sdk_carrier_name, channel_ty_creative_id, adtrace_aid, adtrace_main_platform, 
    channel_ty_video_id, sdk_countrycode
    FROM
      ga_view.dwd_event_20461
    WHERE
      event_id = 'adtrace_attribution_results'
      AND day ${PartDate:ds}
      AND adtrace_attributed = '0'
      AND adtrace_aid IN ('2', '21')
      AND sdk_yidun_device_id != ''  
),

payment_events AS (
    SELECT 
        sdk_yidun_device_id,
        from_unixtime(cast(substring(cast(adtrace_attribution_time as STRING),1,10) as BIGINT)+8*60*60,'yyyy-MM-dd') AS day_att,
        day AS pay_day,
        sum(cast(sdk_purchase_amount as decimal(38,2))/100) AS payment_amount
    FROM ga_view.dwd_event_20461
    WHERE event_id = 'adtrace_pay_new'
      AND day ${PartDate:ds}
      AND retry_times = '0'
    GROUP BY sdk_yidun_device_id, day_att, pay_day
),

login_events AS (
    SELECT DISTINCT 
        sdk_yidun_device_id,
        from_unixtime(cast(substring(cast(adtrace_attribution_time as STRING),1,10) as BIGINT)+8*60*60,'yyyy-MM-dd') AS day_att,
        day AS login_day
    FROM ga_view.dwd_event_20461
    WHERE event_id = 'adtrace_login_new'
        AND day ${PartDate:ds}
),

pay_retention_data AS (
SELECT 
    a.attribution_day as attribution_day_a,
    a.sdk_yidun_device_id as sdk_yidun_device_id_a,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 0 THEN 1 ELSE 0 END) AS D1_retention,     
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 1 THEN 1 ELSE 0 END) AS D2_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 2 THEN 1 ELSE 0 END) AS D3_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 3 THEN 1 ELSE 0 END) AS D4_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 4 THEN 1 ELSE 0 END) AS D5_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 5 THEN 1 ELSE 0 END) AS D6_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 6 THEN 1 ELSE 0 END) AS D7_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 7 THEN 1 ELSE 0 END) AS D8_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 8 THEN 1 ELSE 0 END) AS D9_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 9 THEN 1 ELSE 0 END) AS D10_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 10 THEN 1 ELSE 0 END) AS D11_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 11 THEN 1 ELSE 0 END) AS D12_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 12 THEN 1 ELSE 0 END) AS D13_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 13 THEN 1 ELSE 0 END) AS D14_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 14 THEN 1 ELSE 0 END) AS D15_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 15 THEN 1 ELSE 0 END) AS D16_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 16 THEN 1 ELSE 0 END) AS D17_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 17 THEN 1 ELSE 0 END) AS D18_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 18 THEN 1 ELSE 0 END) AS D19_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 19 THEN 1 ELSE 0 END) AS D20_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 20 THEN 1 ELSE 0 END) AS D21_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 21 THEN 1 ELSE 0 END) AS D22_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 22 THEN 1 ELSE 0 END) AS D23_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 23 THEN 1 ELSE 0 END) AS D24_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 24 THEN 1 ELSE 0 END) AS D25_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 25 THEN 1 ELSE 0 END) AS D26_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 26 THEN 1 ELSE 0 END) AS D27_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 27 THEN 1 ELSE 0 END) AS D28_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 28 THEN 1 ELSE 0 END) AS D29_retention,
MAX(CASE WHEN datediff(l.login_day, a.attribution_day) = 29 THEN 1 ELSE 0 END) AS D30_retention,
    
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 0 THEN p.payment_amount END),0) AS D1_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 1 THEN p.payment_amount END),0) AS D2_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 2 THEN p.payment_amount END),0) AS D3_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 3 THEN p.payment_amount END),0) AS D4_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 4 THEN p.payment_amount END),0) AS D5_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 5 THEN p.payment_amount END),0) AS D6_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 6 THEN p.payment_amount END),0) AS D7_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 7 THEN p.payment_amount END),0) AS D8_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 8 THEN p.payment_amount END),0) AS D9_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 9 THEN p.payment_amount END),0) AS D10_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 10 THEN p.payment_amount END),0) AS D11_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 11 THEN p.payment_amount END),0) AS D12_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 12 THEN p.payment_amount END),0) AS D13_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 13 THEN p.payment_amount END),0) AS D14_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 14 THEN p.payment_amount END),0) AS D15_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 15 THEN p.payment_amount END),0) AS D16_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 16 THEN p.payment_amount END),0) AS D17_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 17 THEN p.payment_amount END),0) AS D18_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 18 THEN p.payment_amount END),0) AS D19_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 19 THEN p.payment_amount END),0) AS D20_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 20 THEN p.payment_amount END),0) AS D21_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 21 THEN p.payment_amount END),0) AS D22_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 22 THEN p.payment_amount END),0) AS D23_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 23 THEN p.payment_amount END),0) AS D24_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 24 THEN p.payment_amount END),0) AS D25_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 25 THEN p.payment_amount END),0) AS D26_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 26 THEN p.payment_amount END),0) AS D27_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 27 THEN p.payment_amount END),0) AS D28_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 28 THEN p.payment_amount END),0) AS D29_payment,
COALESCE(SUM(CASE WHEN datediff(p.pay_day, a.attribution_day) = 29 THEN p.payment_amount END),0) AS D30_payment
FROM attribution_data a
LEFT JOIN payment_events p 
    ON a.sdk_yidun_device_id = p.sdk_yidun_device_id
    AND p.day_att = a.attribution_day  -- 归因时间匹配
LEFT JOIN login_events l 
    ON a.sdk_yidun_device_id = l.sdk_yidun_device_id
    AND l.day_att = a.attribution_day  -- 归因时间匹配
    AND l.login_day = p.pay_day
GROUP BY a.attribution_day, a.sdk_yidun_device_id
)

SELECT 
    a.*,
    pr.*
FROM pay_retention_data pr
JOIN attribution_data a
ON pr.attribution_day_a = a.attribution_day 
AND pr.sdk_yidun_device_id_a = a.sdk_yidun_device_id

    