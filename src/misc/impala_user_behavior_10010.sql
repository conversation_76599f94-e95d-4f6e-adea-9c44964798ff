select 
    sdk_yidun_device_id,day,game_time_in_minutes,final_delta,max_delta,min_delta,max_gunlevel,min_gunlevel,max_boss_rate,total_catch,total_catch_boss,
    fish_gun_fire_sum_count,gun_level_up_consume_count,recharge_count,fish_table_enter_count,skill_use_count,bkrpt_count,total_recharge_amount,login_count,
    shop_center_enter_count,achievement_reward_count,have_checkin_reward,startup_quest_finish_game_count,
    click_120215_count,click_120214_count,click_120093_count,click_120092_count,resource_total_down_count,resource_down_count,
    activity_midnight,activity_morning,activity_afternoon,activity_night
from koi_data.dwd_user_behavior_10010 
where day between '{start_ds}' and '{end_ds}'
