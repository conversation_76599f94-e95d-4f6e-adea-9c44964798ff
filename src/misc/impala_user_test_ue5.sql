SELECT
FROM_UNIXTIME(cast((cast(`event_time` as bigint) + 28800000)/1000 as bigint)) AS 时间,
event_id,
event_time,
proj_base_final,
proj_base_truedelta,
proj_chip_amount,
proj_bet,
proj_chiptype,
proj_reason,
proj_before,
proj_after
-- 剩下填你想看的字段
FROM ga_view.dwd_users_20580
WHERE day = '{attribution_day}'
AND event_id IN ('fire_output','fish_table_leave','fish_table_enter','chip_increase', 'out_of_chip')-- 剩下填你想看的事件
AND (event_id != 'fire_output' OR type = 'coin')
AND user_id = '10038943'
AND (event_id != 'fire_output' OR proj_chiptype = '1')
AND (event_id != 'chip_increase' OR proj_reason != 'fire_output')
ORDER BY 时间 DESC