-- 用户主表（记录用户来源信息）
CREATE TABLE user_profile (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `adtrace_id` varchar(64) DEFAULT NULL COMMENT '用户唯一标识',
  `sdk_yidun_device_id` varchar(255) DEFAULT NULL,
  `channel_ty_campaign_id` varchar(32) DEFAULT NULL COMMENT '关联广告ID',
  `channel_ty_adgroup_id` varchar(32) DEFAULT NULL,
  `channel_ty_account_id` varchar(32) DEFAULT NULL,
  `channel_ty_csite_id` varchar(64) NOT NULL COMMENT '版位ID',
  `registration_date` date NOT NULL COMMENT '注册日期',
  `sdk_device_name` varchar(64) DEFAULT NULL COMMENT '设备信息（品牌/型号/OS版本）',
  `proj_main_channel` varchar(64) DEFAULT NULL COMMENT '主渠道',
  `adtrace_reattributed` int NOT NULL COMMENT '是否再归因',
  PRIMARY KEY (`id`,`registration_date`),
  KEY `idx_registration_date` (`registration_date`),
  KEY `idx_adtrace_id` (`adtrace_id`),
  KEY `idx_channel_ty_campaign_id` (`channel_ty_campaign_id`),
  KEY `idx_channel_ty_adgroup_id` (`channel_ty_adgroup_id`),
  KEY `idx_channel_ty_account_id` (`channel_ty_account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
PARTITION BY RANGE (YEAR(registration_date)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p2027 VALUES LESS THAN (2028),
    PARTITION p2028 VALUES LESS THAN (2029),
    PARTITION p2029 VALUES LESS THAN (2030),
    PARTITION p2030 VALUES LESS THAN (2031)
);


-- 用户真实LTV表（记录实际观测值）
CREATE TABLE user_ltv_actual (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `adtrace_id` varchar(64) DEFAULT NULL COMMENT '用户标识',
  `sdk_yidun_device_id` varchar(255) DEFAULT NULL,
  `date_start` date NOT NULL COMMENT '观察开始日期',
  `date_end` date NOT NULL COMMENT '观察结束日期',
  `ltv` decimal(12,2) NOT NULL COMMENT '观察期内的实际LTV',
  `date_update` date NOT NULL COMMENT '数据更新日期',
  PRIMARY KEY (`id`,`date_update`),
  KEY `idx_adtrace_id` (`adtrace_id`),
  KEY `idx_date_start` (`date_start`),
  KEY `idx_date_end` (`date_end`),
  KEY `idx_user_ltv_actual_sdk_yidun_device_id` (`sdk_yidun_device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
PARTITION BY RANGE (TO_DAYS(date_update)) (
    PARTITION p202312 VALUES LESS THAN (TO_DAYS('2024-01-01')),
    PARTITION p202401 VALUES LESS THAN (TO_DAYS('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (TO_DAYS('2024-03-01')),
    PARTITION p202403 VALUES LESS THAN (TO_DAYS('2024-04-01')),
    PARTITION p202404 VALUES LESS THAN (TO_DAYS('2024-05-01')),
    PARTITION p202405 VALUES LESS THAN (TO_DAYS('2024-06-01')),
    PARTITION p202406 VALUES LESS THAN (TO_DAYS('2024-07-01')),
    PARTITION p202407 VALUES LESS THAN (TO_DAYS('2024-08-01')),
    PARTITION p202408 VALUES LESS THAN (TO_DAYS('2024-09-01')),
    PARTITION p202409 VALUES LESS THAN (TO_DAYS('2024-10-01')),
    PARTITION p202410 VALUES LESS THAN (TO_DAYS('2024-11-01')),
    PARTITION p202411 VALUES LESS THAN (TO_DAYS('2024-12-01')),
    PARTITION p202412 VALUES LESS THAN (TO_DAYS('2025-01-01')),
    PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01')),
    PARTITION p202502 VALUES LESS THAN (TO_DAYS('2025-03-01')),
    PARTITION p202503 VALUES LESS THAN (TO_DAYS('2025-04-01')),
    PARTITION p202504 VALUES LESS THAN (TO_DAYS('2025-05-01')),
    PARTITION p202505 VALUES LESS THAN (TO_DAYS('2025-06-01')),
    PARTITION p202506 VALUES LESS THAN (TO_DAYS('2025-07-01')),
    PARTITION p202507 VALUES LESS THAN (TO_DAYS('2025-08-01')),
    PARTITION p202508 VALUES LESS THAN (TO_DAYS('2025-09-01')),
    PARTITION p202509 VALUES LESS THAN (TO_DAYS('2025-10-01')),
    PARTITION p202510 VALUES LESS THAN (TO_DAYS('2025-11-01')),
    PARTITION p202511 VALUES LESS THAN (TO_DAYS('2025-12-01')),
    PARTITION p202512 VALUES LESS THAN (TO_DAYS('2026-01-01')),
    PARTITION p202601 VALUES LESS THAN (TO_DAYS('2026-02-01')),
    PARTITION p202602 VALUES LESS THAN (TO_DAYS('2026-03-01')),
    PARTITION p202603 VALUES LESS THAN (TO_DAYS('2026-04-01')),
    PARTITION p202604 VALUES LESS THAN (TO_DAYS('2026-05-01')),
    PARTITION p202605 VALUES LESS THAN (TO_DAYS('2026-06-01')),
    PARTITION p202606 VALUES LESS THAN (TO_DAYS('2026-07-01')),
    PARTITION p202607 VALUES LESS THAN (TO_DAYS('2026-08-01')),
    PARTITION p202608 VALUES LESS THAN (TO_DAYS('2026-09-01')),
    PARTITION p202609 VALUES LESS THAN (TO_DAYS('2026-10-01')),
    PARTITION p202610 VALUES LESS THAN (TO_DAYS('2026-11-01')),
    PARTITION p202611 VALUES LESS THAN (TO_DAYS('2026-12-01')),
    PARTITION p202612 VALUES LESS THAN (TO_DAYS('2027-01-01')),
    PARTITION p202701 VALUES LESS THAN (TO_DAYS('2027-02-01')),
    PARTITION p202702 VALUES LESS THAN (TO_DAYS('2027-03-01')),
    PARTITION p202703 VALUES LESS THAN (TO_DAYS('2027-04-01')),
    PARTITION p202704 VALUES LESS THAN (TO_DAYS('2027-05-01')),
    PARTITION p202705 VALUES LESS THAN (TO_DAYS('2027-06-01')),
    PARTITION p202706 VALUES LESS THAN (TO_DAYS('2027-07-01')),
    PARTITION p202707 VALUES LESS THAN (TO_DAYS('2027-08-01')),
    PARTITION p202708 VALUES LESS THAN (TO_DAYS('2027-09-01')),
    PARTITION p202709 VALUES LESS THAN (TO_DAYS('2027-10-01')),
    PARTITION p202710 VALUES LESS THAN (TO_DAYS('2027-11-01')),
    PARTITION p202711 VALUES LESS THAN (TO_DAYS('2027-12-01')),
    PARTITION p202712 VALUES LESS THAN (TO_DAYS('2028-01-01'))
);

-- 用户预测LTV表
CREATE TABLE `user_ltv_prediction` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sdk_yidun_device_id` varchar(255) NOT NULL,
  `attribution_day` date NOT NULL COMMENT '归因日期',
  `observed_days` int unsigned NOT NULL COMMENT '可观测的数据天数',
  `predicted_days` int unsigned NOT NULL COMMENT 'pLTV的天数(如7日LTV,14日LTV,30日LTV)',
  `pltv` decimal(12,2) NOT NULL COMMENT '预测的LTV',
  `model_name` varchar(32) NOT NULL COMMENT '模型版本',
  `record_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '预测执行时间',
  `record_partition` date NOT NULL COMMENT '分区日期',
  PRIMARY KEY (`id`,`record_partition`),
  KEY `idx_date_start` (`attribution_day`),
  KEY `idx_date_end` (`observed_days`),
  KEY `idx_user_ltv_prediction_sdk_yidun_device_id` (`sdk_yidun_device_id`)
) ENGINE=InnoDB AUTO_INCREMENT=469373 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY RANGE (to_days(`record_partition`))
(PARTITION p202312 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739433) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN (739464) ENGINE = InnoDB,
 PARTITION p202408 VALUES LESS THAN (739495) ENGINE = InnoDB,
 PARTITION p202409 VALUES LESS THAN (739525) ENGINE = InnoDB,
 PARTITION p202410 VALUES LESS THAN (739556) ENGINE = InnoDB,
 PARTITION p202411 VALUES LESS THAN (739586) ENGINE = InnoDB,
 PARTITION p202412 VALUES LESS THAN (739617) ENGINE = InnoDB,
 PARTITION p202501 VALUES LESS THAN (739648) ENGINE = InnoDB,
 PARTITION p202502 VALUES LESS THAN (739676) ENGINE = InnoDB,
 PARTITION p202503 VALUES LESS THAN (739707) ENGINE = InnoDB,
 PARTITION p202504 VALUES LESS THAN (739737) ENGINE = InnoDB,
 PARTITION p202505 VALUES LESS THAN (739768) ENGINE = InnoDB,
 PARTITION p202506 VALUES LESS THAN (739798) ENGINE = InnoDB,
 PARTITION p202507 VALUES LESS THAN (739829) ENGINE = InnoDB,
 PARTITION p202508 VALUES LESS THAN (739860) ENGINE = InnoDB,
 PARTITION p202509 VALUES LESS THAN (739890) ENGINE = InnoDB,
 PARTITION p202510 VALUES LESS THAN (739921) ENGINE = InnoDB,
 PARTITION p202511 VALUES LESS THAN (739951) ENGINE = InnoDB,
 PARTITION p202512 VALUES LESS THAN (739982) ENGINE = InnoDB,
 PARTITION p202601 VALUES LESS THAN (740013) ENGINE = InnoDB,
 PARTITION p202602 VALUES LESS THAN (740041) ENGINE = InnoDB,
 PARTITION p202603 VALUES LESS THAN (740072) ENGINE = InnoDB,
 PARTITION p202604 VALUES LESS THAN (740102) ENGINE = InnoDB,
 PARTITION p202605 VALUES LESS THAN (740133) ENGINE = InnoDB,
 PARTITION p202606 VALUES LESS THAN (740163) ENGINE = InnoDB,
 PARTITION p202607 VALUES LESS THAN (740194) ENGINE = InnoDB,
 PARTITION p202608 VALUES LESS THAN (740225) ENGINE = InnoDB,
 PARTITION p202609 VALUES LESS THAN (740255) ENGINE = InnoDB,
 PARTITION p202610 VALUES LESS THAN (740286) ENGINE = InnoDB,
 PARTITION p202611 VALUES LESS THAN (740316) ENGINE = InnoDB,
 PARTITION p202612 VALUES LESS THAN (740347) ENGINE = InnoDB,
 PARTITION p202701 VALUES LESS THAN (740378) ENGINE = InnoDB,
 PARTITION p202702 VALUES LESS THAN (740406) ENGINE = InnoDB,
 PARTITION p202703 VALUES LESS THAN (740437) ENGINE = InnoDB,
 PARTITION p202704 VALUES LESS THAN (740467) ENGINE = InnoDB,
 PARTITION p202705 VALUES LESS THAN (740498) ENGINE = InnoDB,
 PARTITION p202706 VALUES LESS THAN (740528) ENGINE = InnoDB,
 PARTITION p202707 VALUES LESS THAN (740559) ENGINE = InnoDB,
 PARTITION p202708 VALUES LESS THAN (740590) ENGINE = InnoDB,
 PARTITION p202709 VALUES LESS THAN (740620) ENGINE = InnoDB,
 PARTITION p202710 VALUES LESS THAN (740651) ENGINE = InnoDB,
 PARTITION p202711 VALUES LESS THAN (740681) ENGINE = InnoDB,
 PARTITION p202712 VALUES LESS THAN (740712) ENGINE = InnoDB) */
 ;

-- 广告真实LTV表
CREATE TABLE ad_ltv_actual (
    id BIGINT UNSIGNED AUTO_INCREMENT,
    ad_id VARCHAR(32) NOT NULL COMMENT '关联广告ID',
    date_start DATE NOT NULL COMMENT '开始日期',
    date_end DATE NOT NULL COMMENT '结束日期',
    ltv_days SMALLINT UNSIGNED NOT NULL COMMENT 'LTV计算天数(如7日LTV,14日LTV,30日LTV)',
    ltv DECIMAL(16,2) NOT NULL COMMENT '开始-结束日期内导入用户的实际X日LTV',
    user_count INT UNSIGNED NOT NULL COMMENT '覆盖用户数',
    date_update DATE NOT NULL COMMENT '数据更新日期',
    
    PRIMARY KEY (id, date_update),
    INDEX idx_ad_id (ad_id),
    INDEX idx_date_start (date_start),
    INDEX idx_date_end (date_end)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
PARTITION BY RANGE (YEAR(date_update)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p2027 VALUES LESS THAN (2028),
    PARTITION p2028 VALUES LESS THAN (2029),
    PARTITION p2029 VALUES LESS THAN (2030),
    PARTITION p2030 VALUES LESS THAN (2031)
);


-- 广告预测LTV表
CREATE TABLE `ad_ltv_prediction` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `channel_ty_adgroup_id` varchar(32) NOT NULL COMMENT '关联广告ID',
  `attribution_day` date NOT NULL COMMENT '归因日期',
  `observed_days` int unsigned NOT NULL COMMENT '可观测的数据天数',
  `predicted_days` int unsigned NOT NULL COMMENT 'pLTV的天数(如7日LTV,14日LTV,30日LTV)',
  `pltv` decimal(16,2) NOT NULL COMMENT '预测的LTV',
  `user_count` int unsigned NOT NULL COMMENT '用户数',
  `model_name` varchar(32) NOT NULL COMMENT '模型版本',
  `record_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '预测执行时间',
  `record_partition` date NOT NULL COMMENT '分区日期',
  PRIMARY KEY (`id`,`record_partition`),
  KEY `idx_ad_id` (`channel_ty_adgroup_id`),
  KEY `idx_date_start` (`attribution_day`)
) ENGINE=InnoDB AUTO_INCREMENT=27132 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
/*!50100 PARTITION BY RANGE (year(`record_partition`))
(PARTITION p2023 VALUES LESS THAN (2024) ENGINE = InnoDB,
 PARTITION p2024 VALUES LESS THAN (2025) ENGINE = InnoDB,
 PARTITION p2025 VALUES LESS THAN (2026) ENGINE = InnoDB,
 PARTITION p2026 VALUES LESS THAN (2027) ENGINE = InnoDB,
 PARTITION p2027 VALUES LESS THAN (2028) ENGINE = InnoDB,
 PARTITION p2028 VALUES LESS THAN (2029) ENGINE = InnoDB,
 PARTITION p2029 VALUES LESS THAN (2030) ENGINE = InnoDB,
 PARTITION p2030 VALUES LESS THAN (2031) ENGINE = InnoDB) */


-- wget命令执行历史记录表
CREATE TABLE `wget_command_history` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `ds` date NOT NULL COMMENT '数据日期',
  `behavior_wget` text COMMENT '用户游戏行为数据，来自项目10010',
  `pay_wget` text COMMENT '用户生命周期行为数据，来自项目20461',
  `status` enum('pending','success','failed') DEFAULT 'pending' COMMENT '执行状态',
  `insert_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '该行记录的插入时间',
  `last_executed` datetime DEFAULT NULL COMMENT 'wget执行时间',
  `file_size` bigint DEFAULT NULL COMMENT '下载文件大小(KB)',
  `destination_path` varchar(255) DEFAULT NULL COMMENT '文件保存路径',
  PRIMARY KEY (`id`),
  KEY `idx_ds` (`ds`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='wget命令执行历史记录表'



-- 广告干预表
CREATE TABLE `ad_intervention_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `campaign_id` varchar(64) NOT NULL COMMENT '广告ID',
  `action_taken` varchar(32) NOT NULL COMMENT '执行的操作',
  `action_params` TEXT DEFAULT NULL COMMENT '操作参数',
  `model_name` varchar(32) NOT NULL COMMENT '模型/策略名',
  `executed_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
  `ds` date NOT NULL COMMENT '数据更新日期',
  PRIMARY KEY (`id`, `ds`),
  KEY `idx_campaign` (`campaign_id`),
  KEY `idx_execution` (`executed_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
PARTITION BY RANGE (TO_DAYS(ds)) (
    PARTITION p202312 VALUES LESS THAN (TO_DAYS('2024-01-01')),
    PARTITION p202401 VALUES LESS THAN (TO_DAYS('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (TO_DAYS('2024-03-01')),
    PARTITION p202403 VALUES LESS THAN (TO_DAYS('2024-04-01')),
    PARTITION p202404 VALUES LESS THAN (TO_DAYS('2024-05-01')),
    PARTITION p202405 VALUES LESS THAN (TO_DAYS('2024-06-01')),
    PARTITION p202406 VALUES LESS THAN (TO_DAYS('2024-07-01')),
    PARTITION p202407 VALUES LESS THAN (TO_DAYS('2024-08-01')),
    PARTITION p202408 VALUES LESS THAN (TO_DAYS('2024-09-01')),
    PARTITION p202409 VALUES LESS THAN (TO_DAYS('2024-10-01')),
    PARTITION p202410 VALUES LESS THAN (TO_DAYS('2024-11-01')),
    PARTITION p202411 VALUES LESS THAN (TO_DAYS('2024-12-01')),
    PARTITION p202412 VALUES LESS THAN (TO_DAYS('2025-01-01')),
    PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01')),
    PARTITION p202502 VALUES LESS THAN (TO_DAYS('2025-03-01')),
    PARTITION p202503 VALUES LESS THAN (TO_DAYS('2025-04-01')),
    PARTITION p202504 VALUES LESS THAN (TO_DAYS('2025-05-01')),
    PARTITION p202505 VALUES LESS THAN (TO_DAYS('2025-06-01')),
    PARTITION p202506 VALUES LESS THAN (TO_DAYS('2025-07-01')),
    PARTITION p202507 VALUES LESS THAN (TO_DAYS('2025-08-01')),
    PARTITION p202508 VALUES LESS THAN (TO_DAYS('2025-09-01')),
    PARTITION p202509 VALUES LESS THAN (TO_DAYS('2025-10-01')),
    PARTITION p202510 VALUES LESS THAN (TO_DAYS('2025-11-01')),
    PARTITION p202511 VALUES LESS THAN (TO_DAYS('2025-12-01')),
    PARTITION p202512 VALUES LESS THAN (TO_DAYS('2026-01-01')),
    PARTITION p202601 VALUES LESS THAN (TO_DAYS('2026-02-01')),
    PARTITION p202602 VALUES LESS THAN (TO_DAYS('2026-03-01')),
    PARTITION p202603 VALUES LESS THAN (TO_DAYS('2026-04-01')),
    PARTITION p202604 VALUES LESS THAN (TO_DAYS('2026-05-01')),
    PARTITION p202605 VALUES LESS THAN (TO_DAYS('2026-06-01')),
    PARTITION p202606 VALUES LESS THAN (TO_DAYS('2026-07-01')),
    PARTITION p202607 VALUES LESS THAN (TO_DAYS('2026-08-01')),
    PARTITION p202608 VALUES LESS THAN (TO_DAYS('2026-09-01')),
    PARTITION p202609 VALUES LESS THAN (TO_DAYS('2026-10-01')),
    PARTITION p202610 VALUES LESS THAN (TO_DAYS('2026-11-01')),
    PARTITION p202611 VALUES LESS THAN (TO_DAYS('2026-12-01')),
    PARTITION p202612 VALUES LESS THAN (TO_DAYS('2027-01-01')),
    PARTITION p202701 VALUES LESS THAN (TO_DAYS('2027-02-01')),
    PARTITION p202702 VALUES LESS THAN (TO_DAYS('2027-03-01')),
    PARTITION p202703 VALUES LESS THAN (TO_DAYS('2027-04-01')),
    PARTITION p202704 VALUES LESS THAN (TO_DAYS('2027-05-01')),
    PARTITION p202705 VALUES LESS THAN (TO_DAYS('2027-06-01')),
    PARTITION p202706 VALUES LESS THAN (TO_DAYS('2027-07-01')),
    PARTITION p202707 VALUES LESS THAN (TO_DAYS('2027-08-01')),
    PARTITION p202708 VALUES LESS THAN (TO_DAYS('2027-09-01')),
    PARTITION p202709 VALUES LESS THAN (TO_DAYS('2027-10-01')),
    PARTITION p202710 VALUES LESS THAN (TO_DAYS('2027-11-01')),
    PARTITION p202711 VALUES LESS THAN (TO_DAYS('2027-12-01')),
    PARTITION p202712 VALUES LESS THAN (TO_DAYS('2028-01-01'))
);
