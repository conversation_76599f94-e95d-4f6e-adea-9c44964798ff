import sys
import os
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from impala.dbapi import connect
import pandas as pd
from io import StringIO
import tarfile

from config import config

def execute_impala_query_campaign():
    conn = connect(
        host=config['impala']['proxy_host'],
        port=config['impala']['proxy_port'],
        auth_mechanism="NOSASL"
    )
    js = {"campaign_data_path": "/wkspace/qiushi/ltv-prediction/data/task_147_10010_20250421095901.tar.gz", "behavior_data_path":"/wkspace/qiushi/ltv-prediction/data/task_146_10010_20250421100401.tar.gz"}
    cursor = conn.cursor(user=config['impala']['user'])
    with open(os.path.join(project_root, 'misc/impala_advertisement_feature_20461.sql'), 'r') as f:
        sql_template = f.read()
        campaign_query = sql_template.format(attribution_day='2025-04-17')
        
    cursor.execute("show partitions koi_data.dwd_advertisement_feature_20461")
    print(cursor.fetchall())
    return
    cursor.execute("refresh koi_data.dwd_advertisement_feature_20461")
    cursor.execute(campaign_query)
    campaign_results = cursor.fetchall()
    
    column_names = [desc[0] for desc in cursor.description]
    campaign_df = pd.DataFrame(campaign_results, columns=column_names)
    print("campaign_df.shape: ", campaign_df.shape)
    print("campaign_df.columns: ", campaign_df.columns)
    print("campaign_df.head(): ", campaign_df.head().to_string())
    print(campaign_df['sdk_yidun_device_id'].nunique())
    # print(campaign_df[campaign_df['sdk_yidun_device_id'] == 'vE87IhExmAVFG0EQFQPUp2L8gmEt8NZi'].to_string())
    # print(campaign_df[campaign_df['sdk_yidun_device_id'] == 'Cs65ImHrk1lADAAFQQOA7Qe0cj4xvej6'].to_string())
    print(campaign_df[campaign_df['sdk_yidun_device_id'] == 'CuZnYXUxRmxFWQRUEQPB1c13XYdqQgpZ'].to_string())
    # print(campaign_df[campaign_df['sdk_yidun_device_id'] == 'm31VMAmAOPhBCABQAVfWY2JsK1Q5kYW7'].to_string())
    # print(campaign_df[campaign_df['sdk_yidun_device_id'] == 'ULiX4Un8GNNAW1VVAAPGfA4sHnmQWk8u'].to_string())
    
    df_list = []
    with tarfile.open(js['campaign_data_path'], 'r:gz') as tar:
        for member in tar.getmembers():
            if member.name.endswith('.csv'):
                csv_file = tar.extractfile(member)
                content = csv_file.read().decode('utf-8') 
                df = pd.read_csv(StringIO(content))  
                df_list.append(df)
    campaign_df_old = pd.concat(df_list, ignore_index=True)
    print('=' * 100)
    print("campaign_df_old.shape: ", campaign_df_old.shape)
    print("campaign_df_old.shape: ", campaign_df_old[campaign_df_old['attribution_day'] == '2025-04-17'].shape)
    print("campaign_df_old.columns: ", campaign_df_old.columns)
    print("campaign_df_old.head(): ", campaign_df_old.head().to_string())
    print(campaign_df_old['sdk_yidun_device_id'].nunique())
    print(campaign_df_old[campaign_df_old['sdk_yidun_device_id'] == 'CuZnYXUxRmxFWQRUEQPB1c13XYdqQgpZ'][column_names].to_string())
    # compare campaign_df and campaign_df_old
    # campaign_df_old = campaign_df_old.rename(columns={'channel_ty_adgroup_id': 'adgroup_id', 'channel_ty_account_id': 'account_id', 'channel_ty_campaign_id': 'campaign_id'})
    # campaign_df_old = campaign_df_old[['adgroup_id', 'account_id', 'campaign_id']]
    # campaign_df_old = campaign_df_old.drop_duplicates()
    # campaign_df = campaign_df.rename(columns={'channel_ty_adgroup_id': 'adgroup_id', 'channel_ty_account_id': 'account_id', 'channel_ty_campaign_id': 'campaign_id'})
    # campaign_df = campaign_df[['adgroup_id', 'account_id', 'campaign_id']]
    # campaign_df = campaign_df.drop_duplicates()
    
   
def execute_impala_query_behavior():
    js = {"campaign_data_path": "/wkspace/qiushi/ltv-prediction/data/task_147_10010_20250421095901.tar.gz", "behavior_data_path":"/wkspace/qiushi/ltv-prediction/data/task_146_10010_20250421100401.tar.gz"}
    conn = connect(
        host=config['impala']['proxy_host'],
        port=config['impala']['proxy_port'],
        auth_mechanism="NOSASL"
    )
    cursor = conn.cursor(user=config['impala']['user'])
    with open(os.path.join(project_root, 'misc/impala_user_behavior_10010.sql'), 'r') as f:
        sql_template = f.read()
        behavior_query = sql_template.format(start_ds='2025-04-17', end_ds='2025-04-17')
    cursor.execute("show partitions koi_data.dwd_advertisement_feature_20461")
    print(cursor.fetchall())
    return
    cursor.execute('show partitions koi_data.dwd_user_behavior_10010')
    print(cursor.fetchall())
    cursor.execute('refresh koi_data.dwd_user_behavior_10010')
    cursor.execute('select day, count(1) from koi_data.dwd_user_behavior_10010 group by day')
    print(cursor.fetchall())
    
    behavior_query = f"select * from koi_data.dwd_user_behavior_10010 where day = '2025-04-16'"
    cursor.execute(behavior_query)
    behavior_results = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]
    behavior_df = pd.DataFrame(behavior_results, columns=column_names)
    print("behavior_df.shape: ", behavior_df.shape)
    print("behavior_df.columns: ", behavior_df.columns)
    print("behavior_df.head(): ", behavior_df.head().to_string())
    print(behavior_df['sdk_yidun_device_id'].nunique())
    print(behavior_df[behavior_df['sdk_yidun_device_id'] == 'M+vJijE3pGFECxQURALQgYQh2aQZsn4+' ][column_names].to_string())
    print(behavior_df[behavior_df['sdk_yidun_device_id'] == 'u0Nh7VtlkKFBSQEFRQKCFI3bAPpXp3v3'][column_names].to_string())
    # print(behavior_df[behavior_df['sdk_yidun_device_id'] == 'rn8xI/qiOL1FCAREARbEgeupQLHLMHNr'][column_names].to_string())
    
    # print(behavior_df[behavior_df['sdk_yidun_device_id'] == 'uCbQenjrYDZADFBUUEfUCfDnnsYSMIEg'][column_names].to_string())
    print(behavior_df[behavior_df['sdk_yidun_device_id'] == 'M6yBnTn8GjZEDFRAUEbUkB1BzdQdtk0+'][column_names].to_string())
    
    df_list = []
    with tarfile.open(js['behavior_data_path'], 'r:gz') as tar:
        for member in tar.getmembers():
            if member.name.endswith('.csv'):
                csv_file = tar.extractfile(member)
                content = csv_file.read().decode('utf-8') 
                df = pd.read_csv(StringIO(content))  
                df_list.append(df)
    behavior_df_old = pd.concat(df_list, ignore_index=True)
    print('=' * 100)
    print("behavior_df_old.shape: ", behavior_df_old.shape)
    print("behavior_df_old.shape: ", behavior_df_old[behavior_df_old['day'] == '2024-04-17'].shape)
    print("behavior_df_old.columns: ", behavior_df_old.columns)
    print("behavior_df_old.head(): ", behavior_df_old.head().to_string())
    print(behavior_df_old['sdk_yidun_device_id'].nunique())
    
    
    # 构造字典对比，key为column
    target = behavior_df_old[behavior_df_old['sdk_yidun_device_id'] == 'M6yBnTn8GjZEDFRAUEbUkB1BzdQdtk0+'][column_names]
    source = behavior_df[behavior_df['sdk_yidun_device_id'] == 'M6yBnTn8GjZEDFRAUEbUkB1BzdQdtk0+'][column_names]
    print(target.shape, source.shape)
    target = target.head(1).to_dict(orient='records')
    source = source.head(1).to_dict(orient='records')
    print(target)
    print(source)
 
        
if __name__ == "__main__":
    execute_impala_query_campaign()
    execute_impala_query_behavior()
    