import os
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
import pickle
import random
from pprint import pprint
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from collections import defaultdict
from tqdm import tqdm
from multiprocessing import Pool
from functools import partial
from config import config
from operator import itemgetter
import json
from impala.dbapi import connect as impala_connect
from sklearn.preprocessing import LabelEncoder
from deprecation import deprecated
from typing import List, Tuple, Any, Dict, Type
from impala.dbapi import connect
from deprecated import deprecated


def extract_and_pad(data, index, pad_len=3):
    padded_data = pad(list(map(itemgetter(index), data)), pad_len)
    return [float(item) for item in padded_data]

def load_data_from_impala_outter(attribution_ds, ds) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """ 从impala中获取数据 """
    conn = connect(
        host=config['impala']['proxy_host'],
        port=config['impala']['proxy_port'],
        auth_mechanism="NOSASL"
    )
    cursor = conn.cursor(user=config['impala']['user'])
    with open(os.path.join(project_root, 'misc/impala_advertisement_feature_20461.sql'), 'r') as f:
        sql_template = f.read()
        campaign_query = sql_template.format(attribution_day=attribution_ds)
    with open(os.path.join(project_root, 'misc/impala_user_behavior_10010.sql'), 'r') as f:
        sql_template = f.read()
        behavior_query = sql_template.format(start_ds=attribution_ds, end_ds=ds)
    
    cursor.execute("refresh koi_data.dwd_advertisement_feature_20461")
    cursor.execute(campaign_query)
    campaign_results = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]
    campaign_df = pd.DataFrame(campaign_results, columns=column_names)
    
    cursor.execute('refresh koi_data.dwd_user_behavior_10010')
    cursor.execute(behavior_query)
    behavior_results = cursor.fetchall()
    column_names = [desc[0] for desc in cursor.description]
    behavior_df = pd.DataFrame(behavior_results, columns=column_names)
    
    return campaign_df, behavior_df

def load_campaign_relation_from_impala(attribution_ds) -> tuple[dict, dict, dict]:
    
    hql = f""" 
    select 
        sdk_yidun_device_id, attribution_day, channel_ty_campaign_id, channel_ty_account_id, 
        channel_ty_adgroup_id, channel_ty_csite_id, adtrace_platform 
    from koi_data.dwd_advertisement_feature_20461 
    where attribution_day = '{attribution_ds}'
    """
    conn = impala_connect(
            host=config['impala']['proxy_host'],
            port=config['impala']['proxy_port'],
            auth_mechanism="NOSASL"
        )
    cursor = conn.cursor(user=config['impala']['user'])
    cursor.execute(hql)
    
    campaign2did = defaultdict(set)
    did2campaign = {}
    did2adtrace_platform = {}
    
    for sdk_yidun_device_id, attribution_day, _, _, channel_ty_adgroup_id, _, adtrace_platform in cursor.fetchall():
        campaign2did[channel_ty_adgroup_id].add(sdk_yidun_device_id)
        did2campaign[sdk_yidun_device_id] = channel_ty_adgroup_id
        did2adtrace_platform[sdk_yidun_device_id] = adtrace_platform
    
    conn.close()
    
    return did2campaign, campaign2did, did2adtrace_platform


    
def load_behavior_stats_from_impala(ds, users):
    days = 3
    start_ds = (datetime.strptime(ds, "%Y-%m-%d") - timedelta(days=days-1)).strftime("%Y-%m-%d")
    hql = f"""
    select 
        sdk_yidun_device_id, day,
        fish_gun_fire_sum_count,gun_level_up_consume_count,recharge_count,fish_table_enter_count,skill_use_count,
        bkrpt_count,total_recharge_amount,login_count,shop_center_enter_count,achievement_reward_count,
        have_checkin_reward,startup_quest_finish_game_count,click_120215_count,click_120214_count,
        click_120093_count,click_120092_count,resource_total_down_count,resource_down_count,activity_midnight,
        activity_morning,activity_afternoon,activity_night,game_time_in_minutes,final_delta,max_delta,min_delta,
        max_gunlevel,min_gunlevel,max_boss_rate,total_catch,total_catch_boss
    from koi_data.dwd_user_behavior_10010 
    where day between '{start_ds}' and '{ds}'
    """
    conn = impala_connect(
            host=config['impala']['proxy_host'],
            port=config['impala']['proxy_port'],
            auth_mechanism="NOSASL"
        )
    cursor = conn.cursor(user=config['impala']['user'])
    cursor.execute(hql)
    
    behavior_stats_daily = {}
    behavior_stats = {}
    feat_size = None
    for item in cursor.fetchall():
        did, day, stats = item[0], item[1], item[2:]
        feat_size = len(stats)
        if did in users:
            behavior_stats_daily[(did, day)] = list(stats)
    
    for did in users:
        behavior_stats[did] = [[0] * feat_size for _ in range(days)]
            
    for did, day in behavior_stats_daily:
        idx = (datetime.strptime(day, "%Y-%m-%d") - datetime.strptime(start_ds, "%Y-%m-%d")).days
        behavior_stats[did][idx] = [
            0.0 if val is None or (isinstance(val, float) and np.isnan(val)) else float(val)
            for val in behavior_stats_daily[(did, day)]
        ]
    conn.close()
    return behavior_stats


def load_payment_stay_from_impala(attribution_ds, users):
    hql = f"""
    select 
        sdk_yidun_device_id, 
        d1_retention, d2_retention, d3_retention, d1_payment, d2_payment, d3_payment, attribution_day,
        proj_main_channel, sdk_device_name, adtrace_reattributed
        from koi_data.dwd_advertisement_feature_20461 
        where attribution_day = '{attribution_ds}'
    """
    conn = impala_connect(
            host=config['impala']['proxy_host'],
            port=config['impala']['proxy_port'],
            auth_mechanism="NOSASL"
        )
    cursor = conn.cursor(user=config['impala']['user'])
    cursor.execute(hql)
    
    payment_stay_stats = {}
    for item in cursor.fetchall():
        did, stats = item[0], item[1:]
        if did in users:
            payment_stay_stats[did] = list(stats)
    
    conn.close()
    return payment_stay_stats
    


@deprecated(reason="use load_campaign_relation_from_impala instead")
# def load_campaign_data_from_csv(config) -> tuple[dict, dict]:
#     return pad(list(map(itemgetter(index), data)), pad_len)


def load_campaign_relation_from_impala(attribution_ds) -> tuple[dict, dict, dict]:
    
    hql = f""" 
    select 
        sdk_yidun_device_id, attribution_day, channel_ty_campaign_id, channel_ty_account_id, 
        channel_ty_adgroup_id, channel_ty_csite_id, adtrace_platform 
    from koi_data.dwd_advertisement_feature_20461 
    where attribution_day = '{attribution_ds}'
    """
    conn = impala_connect(
            host=config['impala']['proxy_host'],
            port=config['impala']['proxy_port'],
            auth_mechanism="NOSASL"
        )
    cursor = conn.cursor(user=config['impala']['user'])
    cursor.execute(hql)
    
    campaign2did = defaultdict(set)
    did2campaign = {}
    did2adtrace_platform = {}
    
    for sdk_yidun_device_id, attribution_day, _, _, channel_ty_adgroup_id, _, adtrace_platform in cursor.fetchall():
        campaign2did[channel_ty_adgroup_id].add(sdk_yidun_device_id)
        did2campaign[sdk_yidun_device_id] = channel_ty_adgroup_id
        did2adtrace_platform[sdk_yidun_device_id] = adtrace_platform
    
    conn.close()
    
    return did2campaign, campaign2did, did2adtrace_platform


    
def load_behavior_stats_from_impala(ds, users):
    days = 3
    start_ds = (datetime.strptime(ds, "%Y-%m-%d") - timedelta(days=days-1)).strftime("%Y-%m-%d")
    hql = f"""
    select 
        sdk_yidun_device_id, day,
        fish_gun_fire_sum_count,gun_level_up_consume_count,recharge_count,fish_table_enter_count,skill_use_count,
        bkrpt_count,total_recharge_amount,login_count,shop_center_enter_count,achievement_reward_count,
        have_checkin_reward,startup_quest_finish_game_count,click_120215_count,click_120214_count,
        click_120093_count,click_120092_count,resource_total_down_count,resource_down_count,activity_midnight,
        activity_morning,activity_afternoon,activity_night,game_time_in_minutes,final_delta,max_delta,min_delta,
        max_gunlevel,min_gunlevel,max_boss_rate,total_catch,total_catch_boss
    from koi_data.dwd_user_behavior_10010 
    where day between '{start_ds}' and '{ds}'
    """
    conn = impala_connect(
            host=config['impala']['proxy_host'],
            port=config['impala']['proxy_port'],
            auth_mechanism="NOSASL"
        )
    cursor = conn.cursor(user=config['impala']['user'])
    cursor.execute(hql)
    
    behavior_stats_daily = {}
    behavior_stats = {}
    feat_size = None
    for item in cursor.fetchall():
        did, day, stats = item[0], item[1], item[2:]
        feat_size = len(stats)
        if did in users:
            behavior_stats_daily[(did, day)] = [0.0 if x is None or x == '' else float(x) for x in stats]
            
    for did, day in behavior_stats_daily:
        if did not in behavior_stats:
            behavior_stats[did] = [[0.0] * feat_size for _ in range(days)]
        idx = (datetime.strptime(day, "%Y-%m-%d") - datetime.strptime(start_ds, "%Y-%m-%d")).days
        behavior_stats[did][idx] = behavior_stats_daily[(did, day)]
    
    conn.close()
    return behavior_stats


def load_payment_stay_from_impala(attribution_ds, users):
    hql = f"""
    select 
        sdk_yidun_device_id, 
        d1_retention, d2_retention, d3_retention, d1_payment, d2_payment, d3_payment, attribution_day,
        proj_main_channel, sdk_device_name, adtrace_reattributed
        from koi_data.dwd_advertisement_feature_20461 
        where attribution_day = '{attribution_ds}'
    """
    conn = impala_connect(
            host=config['impala']['proxy_host'],
            port=config['impala']['proxy_port'],
            auth_mechanism="NOSASL"
        )
    cursor = conn.cursor(user=config['impala']['user'])
    cursor.execute(hql)
    data = cursor.fetchall()
    columns = [desc[0] for desc in cursor.description]
    df = pd.DataFrame(data, columns=columns)
    numeric_columns = ['d1_retention', 'd2_retention', 'd3_retention', 'd1_payment', 'd2_payment', 'd3_payment', 'adtrace_reattributed']
    df[numeric_columns] = df[numeric_columns].astype(float)
    df[numeric_columns].fillna(0, inplace=True)
    payment_stay_stats = {}
    for _, row in df.iterrows():
        did, stats = row['sdk_yidun_device_id'], row[1:].tolist()
        if did in users:
            payment_stay_stats[did] = stats 
            
    conn.close()   
    return payment_stay_stats
    


@deprecated(reason="use load_campaign_relation_from_impala instead")
def load_campaign_data_from_csv(config) -> tuple[dict, dict]:
    """ 广告和设备的从属关系 
    return:
        c2did: {campaign_id: set(did)}
        did2ct: {did: (campaign_id, day)}
    """
    if os.path.exists(config['path']['campaign_relation_cache']):
        c2did, did2ct = pickle.load(open(config['path']['campaign_relation_cache'], 'rb'))
        return c2did, did2ct
    
    c_info = {}  # {campaign_id: {day: set(did)}}
    c_list = []
    c2did, did2ct = defaultdict(set), {}  # {campaign_id: set(did)}, {did: (campaign_id, day)}
    overlap = []
    columns = ['channel_ty_adgroup_id', 'channel_ty_account_id', 'channel_ty_campaign_id', 'channel_ty_csite_id', 'adtrace_did', 'sdk_yidun_device_id', 'day']
    
    for fn in tqdm(os.listdir(config['path']['campaign_relation_path'])):
        if fn.endswith(".csv"): 
            # columns: channel_ty_adgroup_id,channel_ty_account_id,channel_ty_campaign_id,channel_ty_csite_id,adtrace_did,day
            df = pd.read_csv(os.path.join(config['path']['campaign_relation_path'], fn))
            for _, row in df.iterrows():
                if pd.isna(row['channel_ty_adgroup_id']):
                    continue
                c2did[row['channel_ty_adgroup_id']].add(row['sdk_yidun_device_id'])
                val = (row['channel_ty_adgroup_id'], row['channel_ty_csite_id'], row['attribution_day'])
                if row['sdk_yidun_device_id'] in did2ct and did2ct[row['sdk_yidun_device_id']] != val:
                    overlap.append((row['sdk_yidun_device_id'], did2ct[row['sdk_yidun_device_id']], val))
                did2ct[row['sdk_yidun_device_id']] = val
                
    pickle.dump((c2did, did2ct), open(config['path']['campaign_relation_cache'], 'wb'))
    # len(c_info) = 195230, len(c_list) = 1592330
    print(f"len(overlap): {len(overlap)}")
    print(overlap[:10])
    print(f"c2did: {len(c2did)}")
    print(f"did2ct: {len(did2ct)}")
    return c2did, did2ct


@deprecated(reason="use load_behavior_stats_from_impala instead")
def _load_behavior_partial(fn_list, config, did2ct=None) -> dict:
    """ 游戏内行为统计序列 
    return:
        behavior_stats: {did: list(daily_behaviors)}
    """
    # columns = ['min_delta', 'max_delta', 'final_delta', 'game_time_in_minutes', 'total_catch', 'day']
    columns = ['fish_gun_fire_sum_count', 'gun_level_up_consume_count',
                'recharge_count', 'fish_table_enter_count', 'skill_use_count',
                'bkrpt_count', 'total_recharge_amount', 'login_count',
                'shop_center_enter_count', 'achievement_reward_count',
                'have_checkin_reward', 'startup_quest_finish_game_count',
                'click_120215_count', 'click_120214_count', 'click_120093_count',
                'click_120092_count', 'resource_total_down_count',
                'resource_down_count', 'activity_midnight', 'activity_morning',
                'activity_afternoon', 'activity_night',
                'game_time_in_minutes', 'final_delta', 'max_delta', 'min_delta',
                'max_gunlevel', 'min_gunlevel', 'max_boss_rate', 'total_catch',
                'total_catch_boss']
    # min_delta,max_delta,sdk_yidun_device_id,final_delta,game_time_in_minutes,total_catch,day,adtrace_did
    _behavior_stats = defaultdict(list)
    for fn in fn_list:
        if fn.endswith(".csv"):
            df = pd.read_csv(os.path.join(config['path']['game_stats_folder'], fn), low_memory=False)
            df = df.fillna(0)
            for _, row in df.iterrows():
                # if did2ct is not None and row['adtrace_did'] in did2ct:
                #     _behavior_stats[row['adtrace_did']].append(row[columns].tolist())
                if did2ct is not None and row['sdk_yidun_device_id_union'] in did2ct:
                    _behavior_stats[row['sdk_yidun_device_id_union']].append(row[columns].tolist())
    return _behavior_stats


@deprecated(reason="use load_behavior_stats_from_impala instead")
def load_behavior_stats(config, did2ct) -> dict:
    """ 游戏内行为统计序列 
    return:
        behavior_stats: {did: list(daily_behaviors)}
    """
    if os.path.exists(config['path']['game_stats_cache']):
        behavior_stats = pickle.load(open(config['path']['game_stats_cache'], 'rb'))
        return behavior_stats
    
    n = 12  # multiprocessing
    fn_list = os.listdir(config['path']['game_stats_folder'])
    fn_list = [fn_list[i::n] for i in range(n)]
    with Pool(n) as p:
        behavior_stats_list = p.map(partial(_load_behavior_partial, config = config, did2ct=did2ct), fn_list)

    behavior_stats = defaultdict(list)
    for _behavior_stats in behavior_stats_list:
        for did, stats in _behavior_stats.items():
            behavior_stats[did] += stats
    
    for did in behavior_stats.keys():
        behavior_stats[did].sort(key=lambda x: x[-1])
    
    pickle.dump(behavior_stats, open(config['path']['game_stats_cache'], 'wb'))
    return behavior_stats

@deprecated(reason="use load_payment_stay_stats_from_impala instead")
def _load_payment_partial(fn_list, config, did2ct=None) -> dict:
    """ 付费、当日留存等信息统计
    return:
        payment_stay_stats: {did: list(daily_payment_stay_stats)}
    """
    # columns = ['proj_main_channel', 'sdk_device_name', 'adtrace_reattributed', 
    #            'stay1', 'stay2', 'stay3', 'pay1', 'pay2', 'pay3', 'day']
    columns = [
            'd1_retention', 'd2_retention', 'd3_retention', 'd1_payment', 'd2_payment', 'd3_payment', 
            'attribution_day',
            'proj_main_channel', 
            'sdk_device_name',
            'adtrace_reattributed', 
            ]
    _user_stats = {}
    for fn in fn_list:
        if fn.endswith(".csv"):
            df = pd.read_csv(os.path.join(config['path']['user_stats_folder'], fn))
            for _, row in df.iterrows():
                # if did2ct is not None and row['adtrace_did'] in did2ct:
                #     _user_stats[row['adtrace_did']] = row[columns].tolist()
                if did2ct is not None and row['sdk_yidun_device_id'] in did2ct:
                    _user_stats[row['sdk_yidun_device_id']] = row[columns].tolist()
    
    return _user_stats     
    
    
@deprecated(reason="use load_payment_stay_stats_from_impala instead")
def load_payment_stay_stats(config, did2ct) -> dict:
    """ 付费、当日留存等信息统计
    return:
        payment_stay_stats: {did: list(daily_payment_stay_stats)}
    """
    
    if os.path.exists(config['path']['user_stats_cache']):
        payment_stay_stats = pickle.load(open(config['path']['user_stats_cache'], 'rb'))
        return payment_stay_stats
    
    overlap = []
    n = 8
    fn_list = os.listdir(config['path']['user_stats_folder'])
    fn_list = [fn_list[i::n] for i in range(n)]
    with Pool(n) as p:
        payment_stats_list = p.map(partial(_load_payment_partial, config=config, did2ct=did2ct), fn_list)
    
    payment_stay_stats = {}
    for payment_stats in payment_stats_list:
        for did, stats in payment_stats.items():
            if did in payment_stay_stats:
                overlap.append(did)
            payment_stay_stats[did] = stats
    
    pickle.dump(payment_stay_stats, open(config['path']['user_stats_cache'], 'wb'))
    return payment_stay_stats


def pad(x, min_len, default=0):
    if len(x) >= min_len:
        return x[:min_len]
    else:
        return x + [default] * (min_len - len(x))


def reading_device_price_df(config):
    device_price_path = config["path"]["device_price_path"]
    device_price_df = pd.read_csv(device_price_path)
    device_price_df = device_price_df.drop_duplicates('device_name').reset_index(drop=True) 
    return device_price_df


class Fish3DLTVDataset:
    def __init__(self, config, ds):
        start_time = datetime.now()
        self.config = config
        attribution_ds = (datetime.strptime(ds, "%Y-%m-%d") - timedelta(days=2)).strftime("%Y-%m-%d")
        self.did2campaign, self.campaign2did, self.did2adtrace_platform = load_campaign_relation_from_impala(attribution_ds)
        self.users = list(set(self.did2campaign.keys()))
        print(f"ds: {ds}, len(self.users): {len(self.users)}")
        self.behavior_stats = load_behavior_stats_from_impala(ds, set(self.users))
        self.payment_stay_stats = load_payment_stay_from_impala(attribution_ds, set(self.users))
        self.device_dict_df = reading_device_price_df(config)
                
        self.users = set(self.users) & set(self.payment_stay_stats.keys()) & set(self.behavior_stats.keys())
        print(f"ds: {ds}, len(self.users): {len(self.users)}")
                
        end_time = datetime.now()
        print(f"Time taken: {end_time - start_time}")


    def transform(self, did):
        """ 将原始数据转换为特征 """

        data = self.behavior_stats[did]

        fish_gun_fire_sum_count     = extract_and_pad(data, 0)
        gun_level_up_consume_count  = extract_and_pad(data, 1)
        recharge_count              = extract_and_pad(data, 2)
        fish_table_enter_count      = extract_and_pad(data, 3)
        skill_use_count             = extract_and_pad(data, 4)
        bkrpt_count                 = extract_and_pad(data, 5)
        total_recharge_amount       = extract_and_pad(data, 6)
        login_count                 = extract_and_pad(data, 7)
        shop_center_enter_count     = extract_and_pad(data, 8)
        achievement_reward_count    = extract_and_pad(data, 9)
        have_checkin_reward         = extract_and_pad(data, 10)
        startup_quest_finish_game_count = extract_and_pad(data, 11)
        click_120215_count          = extract_and_pad(data, 12)
        click_120214_count          = extract_and_pad(data, 13)
        click_120093_count          = extract_and_pad(data, 14)
        click_120092_count          = extract_and_pad(data, 15)
        resource_total_down_count   = extract_and_pad(data, 16)
        resource_down_count         = extract_and_pad(data, 17)
        activity_midnight           = extract_and_pad(data, 18)
        activity_morning            = extract_and_pad(data, 19)
        activity_afternoon          = extract_and_pad(data, 20)
        activity_night              = extract_and_pad(data, 21)
        game_time_in_minutes        = extract_and_pad(data, 22)
        final_delta                 = extract_and_pad(data, 23)
        max_delta                   = extract_and_pad(data, 24)
        min_delta                   = extract_and_pad(data, 25)
        max_gunlevel                = extract_and_pad(data, 26)
        min_gunlevel                = extract_and_pad(data, 27)
        max_boss_rate               = extract_and_pad(data, 28)
        total_catch                 = extract_and_pad(data, 29)
        total_catch_boss            = extract_and_pad(data, 30)
        

        stay = self.payment_stay_stats[did][0:3]
        pay = self.payment_stay_stats[did][3:6]
        
        channel = self.payment_stay_stats[did][7]
        adtrace_reattributed = self.payment_stay_stats[did][-1]
        device_name = self.payment_stay_stats[did][-2]
        price = self.device_dict_df.set_index('device_name')['price'].get(device_name, 0)/1e4
                
        seq_feature = np.array([
            np.array(login_count),
            np.array(pay),
            
            np.log10(np.array(min_delta) + 1)/10,
            np.log10(np.array(max_delta) + 1)/10,
            np.log10(np.array(final_delta) + 1)/10,
            np.log10(np.array(fish_gun_fire_sum_count) + 1)/10,
            
            np.log10(np.array(max_gunlevel) + 1)/10,
            np.log10(np.array(min_gunlevel) + 1)/10,
            np.log10(np.array(max_boss_rate) + 1)/10,
            np.log10(np.array(total_catch_boss) + 1)/10,
            
            np.log10(np.array(gun_level_up_consume_count) + 1)/10,
            np.log10(np.array(recharge_count) + 1)/10,
            np.log10(np.array(fish_table_enter_count) + 1)/10,
            np.log10(np.array(skill_use_count) + 1)/10,
            np.log10(np.array(total_recharge_amount) + 1)/10,
            np.log10(np.array(skill_use_count) + 1)/10,
            np.log10(np.array(game_time_in_minutes) + 1)/10,
            np.log10(np.array(total_catch) + 1)/10,
            
            np.array(bkrpt_count),
            np.array(shop_center_enter_count),
            np.array(achievement_reward_count),
            np.array(have_checkin_reward),
            np.array(startup_quest_finish_game_count),
            np.array(click_120215_count),
            np.array(click_120214_count),
            np.array(click_120093_count),
            np.array(click_120092_count),
            np.array(resource_total_down_count),
            np.array(resource_down_count),
            np.array(activity_midnight),
            np.array(activity_morning),
            np.array(activity_afternoon),
            np.array(activity_night),
        ], dtype=np.float32)

        with open(config['path']['labelencoder_path'], 'r', encoding='utf-8') as f:
            label_map = json.load(f)
        
        le_loaded = LabelEncoder()
        sorted_items = sorted(label_map.items(), key=lambda x: x[1])
        le_loaded.classes_ = np.array([item[0] for item in sorted_items])
        
        num_bins = 500
        bucket_edges = np.linspace(0, 1, num_bins - 1)
        bucket_ids = np.digitize(price, bucket_edges, right = False)
        
        if channel in le_loaded.classes_:
            transformed_channel = le_loaded.transform([channel])[0]
        else:
            transformed_channel = 1
        
        user_feature = np.vstack([adtrace_reattributed, transformed_channel, bucket_ids]).flatten()
        
        seq_feature = np.array(seq_feature, dtype=np.float32)
        user_feature = np.array(user_feature, dtype=np.float32)
        
        return seq_feature, user_feature
    
    
    def setup(self):
        self.features = [self.transform(did) for did in self.users]
    
    

if __name__ == "__main__":
    ltv_dataset = Fish3DLTVDataset(config, '2025-04-28')
    # print(ltv_dataset.transform('UMKkECVH2aFBSFQQQAbFIaOpMC5DRAFV'))
    print(list(ltv_dataset.behavior_stats.keys())[:10])
    keys_dict1 = set(ltv_dataset.did2campaign.keys())
    keys_dict2 = set(ltv_dataset.behavior_stats.keys())
    keys_dict3 = set(ltv_dataset.payment_stay_stats.keys())

    # 计算交集并查看相同的键的数量
    common_keys = keys_dict1 & keys_dict3
    print(f"same key: {len(common_keys)}")
    print(f"did2casmpaign_len: {len(keys_dict1)}")
    print(f"payment_stay_stats_len: {len(keys_dict3)}")

    ltv_dataset = Fish3DLTVDataset(config)

