with
  fish_catch_stats as (
    select
        sdk_yidun_device_id,
        day,
        count(distinct rounded_time) * 5 as game_time_in_minutes, ---游戏时长,
        sum(delta) as final_delta, ---捕获鱼金币数
        max(delta) as max_delta, ---捕获鱼最大金币数
        min(delta) as min_delta, ---捕获鱼最小金币数
        max(gunlevel) as max_gunlevel, ---最大炮倍
        min(gunlevel) as min_gunlevel, ---最小炮倍
        max(truedelta / gunlevel) as max_boss_rate,
        count(1) as total_catch, ---捕获鱼次数
        sum(case when cast(proj_fishid as int) > 1500 then 1 else 0 end) as total_catch_boss ---捕获BOSS鱼次数

    from
        (
        select
            sdk_yidun_device_id,
            day,
            cast(proj_delta as bigint) as delta,
            cast(proj_gunlevel as bigint) as gunlevel,
            cast(proj_truedelta as bigint) as truedelta,
            proj_fishid,
            from_unixtime (
            cast(floor(floor(event_time / 1000 + 8 * 3600) / 300) * 300 as bigint),
            'HH:mm'
            ) as rounded_time
        from
            table.event_10010
        where
            event_id = 'fish_catch_gain'
            and day ${PartDate:ds}
            and channel_id not in ( 'midanji', 'huawei', 'honor', 'vivo', 'oppo', 'rongyao')
            and proj_chiptype = '1'
        ) a
    group by
        sdk_yidun_device_id,
        day
  ),

  event_counts as (
    select
      sdk_yidun_device_id as sdk_yidun_device_id_e,
      day as day_e,
      count( case when event_id = 'fish_gun_fire_sum' then 1 end ) as fish_gun_fire_sum_count, ---开炮次数
      count( case when event_id = 'gun_level_up_consume' then 1 end ) as gun_level_up_consume_count, ---升炮次数
      count( case when event_id = 'recharge' then 1 end ) as recharge_count, ---付费次数
      count( case when event_id = 'fish_table_enter' then 1 end ) as fish_table_enter_count, ---进渔场次数
      count( case when event_id = 'skill_use' then 1 end ) as skill_use_count, ---使用道具次数
      count( case when event_id = 'fish_bkrpt' then 1 end ) as bkrpt_count, ---破产次数
      COALESCE(sum( case when event_id = 'recharge' then cast(sdk_pay_price_cny as decimal(10, 2)) else 0 end ), 0) as total_recharge_amount, ---总付费金额
      count( case when event_id = 'sdk_s_login_succ' then 1 end ) as login_count, ---登录次数
      count( case when event_id = 'fish_new_shop_center_enter_client' then 1 end ) as shop_center_enter_count, ---进入商城次数
      count( case when event_id = 'fish_achievement_reward' then 1 end ) as achievement_reward_count, ---成就系统奖励次数
      COALESCE(max( case when event_id = 'fish_daily_checkin_reward_coin' then 1 end ),0) as have_checkin_reward, ---签到
      count( case when event_id = 'startup_quest_finish_game' then 1 end ) as startup_quest_finish_game_count, ---任务完成次数
      count( case when event_id = '120215' then 1 end ) as click_120215_count, ---点击聚合礼包入口次数
      count( case when event_id = '120214' then 1 end ) as click_120214_count, ---点击聚合礼包次级入口次数
      count( case when event_id = '120093' then 1 end ) as click_120093_count, ---点击大厅UI-背包次数
      count( case when event_id = '120092' then 1 end ) as click_120092_count, ---点击大厅UI-活动次数
      count( case when event_id = 'resource_download_icon_total_down_client' then 1 end ) as resource_total_down_count, ---点击一键下载按钮次数
      count( case when event_id = 'resource_download_icon_click_client' then 1 end ) as resource_down_count, ---点击资源下载入口icon次数
      COALESCE(max(CASE WHEN EXTRACT(HOUR FROM FROM_UNIXTIME(cast(event_time / 1000 + 8 * 3600 as bigint))) BETWEEN 0 AND 5 THEN 1 END), 0) AS activity_midnight,
      COALESCE(max(CASE WHEN EXTRACT(HOUR FROM FROM_UNIXTIME(cast(event_time / 1000 + 8 * 3600 as bigint))) BETWEEN 6 AND 11 THEN 1 END), 0) AS activity_morning,
      COALESCE(max(CASE WHEN EXTRACT(HOUR FROM FROM_UNIXTIME(cast(event_time / 1000 + 8 * 3600 as bigint))) BETWEEN 12 AND 17 THEN 1 END), 0) AS activity_afternoon,
      COALESCE(max(CASE WHEN EXTRACT(HOUR FROM FROM_UNIXTIME(cast(event_time / 1000 + 8 * 3600 as bigint))) BETWEEN 18 AND 23 THEN 1 END), 0) AS activity_night
      
    from
      table.event_10010
    where
     day ${PartDate:ds}
      and event_id in ('fish_gun_fire_sum', 'gun_level_up_consume', 'recharge', 'fish_table_enter', 'skill_use', 'fish_bkrpt', 
                       'sdk_s_login_succ', 'fish_new_shop_center_enter_client', 'fish_achievement_reward', 'fish_daily_checkin_reward_coin', 
                       'startup_quest_finish_game', '120215', '120214', '120092', '120093',
                       'resource_download_icon_total_down_client', 'resource_download_icon_click_client')
      and channel_id not in ( 'midanji', 'huawei', 'honor', 'vivo', 'oppo', 'rongyao' )
    group by
      sdk_yidun_device_id,
      day
  )
  
select
  coalesce(f.sdk_yidun_device_id, e.sdk_yidun_device_id_e) as sdk_yidun_device_id_union,
  coalesce(f.day, e.day_e) as day_union,
  e.*,
  f.*
from
  fish_catch_stats f
  full outer join event_counts e on f.sdk_yidun_device_id = e.sdk_yidun_device_id_e
  and f.day = e.day_e