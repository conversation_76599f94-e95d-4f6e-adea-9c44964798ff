import os
os.environ["CUDA_VISIBLE_DEVICES"] = ""
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

import time
import schedule
import traceback
import importlib
from pprint import pprint
from typing import Any, Type
import argparse

from config import config
from utils.logger import setup_logger
from dataset.ltv_analysis import calc_user_ltv, calc_ad_ltv


def _load_pipeline_class(class_path: str, logger) -> Type[Any]:
    """ 动态加载流水线 """
    try:
        module_path, class_name = class_path.rsplit(".", 1)
        module = importlib.import_module(module_path)
        return getattr(module, class_name)
    except Exception as e:
        logger.error(f"pipeline class load failed: {class_path}")
        raise RuntimeError(f"pipeline class load failed: {class_path}") from e


def run(ds=None, name=None):    
    # 初始化日志
    logger = setup_logger("ltv-prediction-logger", config["path"]["log_path"])
    logger.info("start ltv-prediction")
    
    # 执行LTV真实值统计, TODO
    # calc_user_ltv()
    # calc_ad_ltv()
    
    # 执行流水线
    for pipeline_config in config["pipelines"]:
        try:
            Pipeline = _load_pipeline_class(pipeline_config['class_path'], logger)
            if ds is not None:
                pipeline_config['ds'] = ds
            if name is None or name == pipeline_config['name']:  # 指定名称运行
                logger.info(f"start pipeline: {pipeline_config}")
                result = Pipeline(**pipeline_config).execute()
                logger.info(result)
            
        except Exception as e:
            logger.error("Pipeline failed")
            logger.error(f'{e}, {traceback.format_exc()}')
        

def main(run_forever=False, ds=None, name=None):    
    if run_forever:
        schedule.every().day.at("11:00", "Asia/Shanghai").do(run)
        while True:
            schedule.run_pending()
            time.sleep(1)
    else:
        run(ds=ds, name=name)
            

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--forever', action='store_true', help='是否一直运行')
    parser.add_argument('--ds', type=str, default=None, help='运行日期T-1')
    parser.add_argument('--name', type=str, default=None, help='运行名称')
    args = parser.parse_args()
    
    main(run_forever=args.forever, ds=args.ds, name=args.name)
    
    # 常驻运行
    # python -u main.py --forever
    # 指定日期运行
    # python -u main.py --ds 2025-04-16
    