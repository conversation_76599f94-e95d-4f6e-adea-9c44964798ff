import os
os.environ["CUDA_VISIBLE_DEVICES"] = "2,3"

import torch
from pytorch_lightning import Trainer
# from torch_model_whether_payment import WhetherPaymentModel
# from torch_model_payment_buckets import PaymentBucketsModel
# from model.torch_model_payment_regression_modified import PaymentRegressionModel
from model.torch_model_any_day_prediction import AnyDayModel

if __name__ == "__main__":
    # # 1. 转换 WhetherPaymentModel
    # ckpt_path_whether_payment = "./tb_logs/whether_payment/version_66/checkpoints/epoch=82-step=415.ckpt"
    # whether_payment_model = WhetherPaymentModel.load_from_checkpoint(ckpt_path_whether_payment)
    # whether_payment_model_state_dict = whether_payment_model.state_dict()  # 提取模型的 state_dict
    # torch.save(whether_payment_model_state_dict, "../pth_model/whether_payment_model.pth")
    
    # # 2. 转换 PaymentBucketsModel
    # ckpt_path_payment_buckets = "./tb_logs/payment_buckets/version_104/checkpoints/epoch=82-step=747.ckpt"
    # payment_buckets_model = PaymentBucketsModel.load_from_checkpoint(ckpt_path_payment_buckets)
    # payment_buckets_model_state_dict = payment_buckets_model.state_dict()  # 提取模型的 state_dict
    # torch.save(payment_buckets_model_state_dict, "../pth_model/payment_buckets_model.pth")  
    
    # 3. 转换 PaymentRegressionModel
    # ckpt_path_payment_regression = "./tb_logs/payment_regression/version_39/checkpoints/epoch=645-step=81396.ckpt"
    # payment_regression_model = PaymentRegressionModel.load_from_checkpoint(ckpt_path_payment_regression)
    # payment_regression_model_state_dict = payment_regression_model.state_dict()  # 提取模型的 state_dict
    # torch.save(payment_regression_model_state_dict, "../pth_model/payment_regression_model.pth")  

    ckpt_path_anyday_prediction = "./tb_logs/any_day/version_101/checkpoints/epoch=5-step=198.ckpt"
    anyday_prediction_model = AnyDayModel.load_from_checkpoint(ckpt_path_anyday_prediction)
    anyday_prediction_model_state_dict = anyday_prediction_model.state_dict()  # 提取模型的 state_dict
    torch.save(anyday_prediction_model_state_dict, "../pth_model/any_day_prediction_model.pth")