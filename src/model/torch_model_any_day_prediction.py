import os
import json
# from memory_profiler import profile

import scipy.stats
from scipy.stats import spearmanr
from scipy.stats import wasserstein_distance
import numpy as np
from sklearn.metrics import ndcg_score
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.nn.utils.rnn import pad_sequence, pack_padded_sequence, pad_packed_sequence
import pytorch_lightning as pl


def evaluate_ltv_distribution_torch(true_ltv, pred_ltv, n_groups=10):
    """
    1. 按真实值排序并分组，令每组的真实值累计和相同
    2. 计算每组的真实值/预测值累计和差异
    3. 计算整体MAPE

    Returns:
        dict: 每组累计和差异结果
        float: 整体MAPE
    """
    sorted_true, indices = torch.sort(true_ltv)
    sorted_pred = pred_ltv[indices]
    cumsum_true = torch.cumsum(sorted_true, dim=0)
    total_sum = cumsum_true[-1].item()
    target_sum_per_group = total_sum / n_groups  # 每组的理想累计和
    
    # 动态寻找分组边界
    boundaries = [0]
    current_sum = 0.0
    for i in range(len(sorted_true)):
        current_sum += sorted_true[i].item()
        if current_sum >= target_sum_per_group * len(boundaries):
            boundaries.append(i + 1)
            if len(boundaries) == n_groups:
                break
    while len(boundaries) < n_groups + 1:
        boundaries.append(len(sorted_true))
    # print("boundaries len:", len(boundaries))
    # print("boundaries:", boundaries)
    # print(f"target_sum_per_group: {target_sum_per_group:.2f}")
    # print(f"total_sum: {total_sum:.2f}")
    # print(f"n_groups: {n_groups}")
    # print(f"sorted_true len: {len(sorted_true)}")
    # print(f"total_sum: {total_sum:.2f}")
    
    # 初始化结果存储
    group_results = {
        'group': [],
        'true_sum': [],
        'pred_sum': [],
        'abs_diff': [],
        'rel_diff': [],
        'group_size': []
    }

    # 计算每组的统计量
    for i in range(n_groups):
        start = boundaries[i]
        end = boundaries[i + 1]
        
        true_sum = sorted_true[start:end].sum().item()
        pred_sum = sorted_pred[start:end].sum().item()
        abs_diff = abs(true_sum - pred_sum)
        rel_diff = abs_diff / (true_sum + 1e-6)
        
        group_results['group'].append(i)
        group_results['true_sum'].append(int(true_sum))
        group_results['pred_sum'].append(int(pred_sum))
        group_results['abs_diff'].append(int(abs_diff))
        group_results['rel_diff'].append(round(rel_diff, 4))
        group_results['group_size'].append(end - start)

    gmape = np.mean(group_results['rel_diff'])
    return group_results, gmape.item()


class PositionalEncoding(nn.Module):
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-torch.log(torch.tensor(10000.0)) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        x = x + self.pe[:, :x.size(1)]
        return x


class CombinedLossV2(nn.Module):
    def __init__(self, class_weight=None, use_mse=False, fixed_sigma=None):
        super().__init__()
        self.bce = nn.BCEWithLogitsLoss(reduction='none')
        self.mse = nn.MSELoss(reduction='none')
        self.class_weight = class_weight
        self.use_mse = use_mse
        self.fixed_sigma = fixed_sigma
   
    
    def forward(self, mean, std, logits, have_paid, payment):
        """
        mean: (B, L, 3)
        std: (B, L, 3)
        logits: (B, L, 3)
        have_paid: (B, L)
        payment: (B, L)
        """
        pred_list = []
        loss_dict = {}
        sample_cnt = 0
        total_loss = 0
        for i, day in enumerate([3, 7, 14]):
            pay_in_future = torch.flip(torch.cumsum(torch.flip(payment[:, :day], dims=[1]), dim=1), dims=[1]) # (B, day)
            label = (pay_in_future > 0).bool()
            bce_loss = self.bce(logits[:, :day-1, i], label[:, 1:day].float())
            # bce_loss_list.append(bce_loss.flatten())
            loss_dict[f"bce_loss_{day}"] = bce_loss.mean().item()
            sample_cnt += bce_loss.numel()
            total_loss += bce_loss.sum()
            
            mu = mean[:, :day-1, i][label[:, 1:day]]
            sigma = std[:, :day-1, i][label[:, 1:day]]
            target = pay_in_future[:, 1:day][label[:, 1:day]]
            
            target_log = torch.log(target)
            if self.use_mse:
                lognormal_loss = self.mse(mu, target_log)
            elif self.fixed_sigma is not None:
                lognormal_loss = 0.5 * torch.pow((target_log - mu) / self.fixed_sigma, 2)
            else:
                lognormal_loss = 0.5 * torch.pow((target_log - mu) / sigma, 2) + torch.log(target * sigma * torch.sqrt(torch.tensor(2 * torch.pi)))
            # lognormal_loss_list.append(lognormal_loss.flatten())
            loss_dict[f"lognormal_loss_{day}"] = lognormal_loss.mean().item()
            total_loss += lognormal_loss.sum()
            
        total_loss = total_loss / sample_cnt
        loss_dict["total_loss"] = total_loss.item()
        return total_loss, loss_dict


class AnyDayModel(pl.LightningModule):
    def __init__(self, 
                guiyin_dim=47,
                behav_dim=32,
                hidden_dim=128,
                num_heads=4,
                num_layers=2,
                # dropout=0.1,
                # date_embed_dim=16,
                # guiyin_embed_dim = 16,
                max_date=14,  
                output_dim=3,
                class_weight=None,
                lr=1e-5,
                weight_decay=1e-2,
                use_mse=False,
                fixed_sigma=None,
                payment_cumsum_mean=None,
                ):
        super().__init__()
        self.save_hyperparameters()
        self.max_seq_len = max_date
        self.lr = lr
        self.weight_decay = weight_decay
        self.use_mse = use_mse
        self.fixed_sigma = fixed_sigma
        # TODO:
        # # 处理 payment_cumsum_mean 为 None 的情况
        # if payment_cumsum_mean is None:
        #     # 使用默认值，避免推理时的错误
        #     self.baseline_matrix = [[1.0 for j in range(i+1)] for i in range(14)]
        # else:
        self.baseline_matrix = [[payment_cumsum_mean[i] / payment_cumsum_mean[j] for j in range(i+1)] for i in range(len(payment_cumsum_mean))]
        
        with open(os.path.join(os.path.dirname(__file__), "../../data/encoding_map.json"), "r") as f:
            encoder_dict = json.load(f)
        guiyin_input_dims = [
            max(mapping.values()) + 1
            for field, mapping in encoder_dict.items()
        ]
        self.guiyin_embeddings = nn.ModuleDict({
            str(i): nn.Embedding(num_embeddings=dim, embedding_dim=hidden_dim)
            for i, dim in enumerate(guiyin_input_dims)
        })
                        
        self.input_norm = nn.BatchNorm1d(behav_dim)
        self.input_proj = nn.Linear(behav_dim, hidden_dim)
        self.positional_encoding = PositionalEncoding(hidden_dim, max_len=self.max_seq_len)
        decoder_layer = nn.TransformerDecoderLayer(d_model=hidden_dim, nhead=num_heads, batch_first=True)
        self.transformer_decoder = nn.TransformerDecoder(decoder_layer, num_layers=num_layers)
        
        self.prob_proj = nn.Linear(hidden_dim, output_dim)
        self.mean_proj = nn.Linear(hidden_dim, output_dim)
        self.std_proj = nn.Linear(hidden_dim, output_dim)
        self.loss_func = CombinedLossV2(class_weight=class_weight, use_mse=use_mse, fixed_sigma=fixed_sigma)
    
    
    def make_tgt_mask(self, sz):
        """Generate a triangular mask"""
        return torch.triu(torch.ones(sz, sz) * float('-inf'), diagonal=1)
        
        
    def forward(self, campaign_feat, behav_feat):
        device = behav_feat.device
        B, L, D = behav_feat.shape
        
        # 处理静态归因特征
        embedded_fields = []
        for str_i, embed_layer in self.guiyin_embeddings.items():
            i = int(str_i)
            feature_values = campaign_feat[:, i].long()
            embedded = embed_layer(feature_values).unsqueeze(1)  # (B, 1, hidden_dim)
            embedded_fields.append(embedded) 
        guiyin_embedded = torch.cat(embedded_fields, dim=1)  # (B, C, hidden_dim)
        
        # 处理行为特征（变长序列）
        x = self.input_norm(behav_feat.transpose(1, 2)).transpose(1, 2)
        x = self.input_proj(x)
        x = self.positional_encoding(x)
        
        tgt_mask = self.make_tgt_mask(L).to(device)
        out = self.transformer_decoder(tgt=x, memory=guiyin_embedded, tgt_mask=tgt_mask,
                                       tgt_key_padding_mask=None)
        mean = self.mean_proj(out)  # (B, L, output_dim)
        std = F.softplus(self.std_proj(out))   # (B, L, output_dim)
        logits = self.prob_proj(out)   # (B, L, output_dim)
        return mean, std, logits
        
        
    def training_step(self, batch, batch_idx):
        """
        batch['device_id']: (B, )
        batch['behavior']: (B, L, D)
        batch['campaign']: (B, C)
        batch['payment']: (B, L)
        batch['have_paid']: (B, L)
        """
        mean, std, logits = self(batch['campaign'], batch['behavior'])
        total_loss, loss_dict = self.loss_func(mean, std, logits, batch['have_paid'], batch['payment'])
        for key, value in loss_dict.items():
            self.log(f'train/{key}', value)
        return total_loss


    def validation_step(self, batch, batch_idx):
        mean, std, logits = self(batch['campaign'], batch['behavior'])
        total_loss, loss_dict = self.loss_func(mean, std, logits, batch['have_paid'], batch['payment'])
        for key, value in loss_dict.items():
            self.log(f'val/{key}', value)
        output = {}
            
        for i, day in enumerate([3, 7, 14]):
            p = F.sigmoid(logits[:, :day-1, i])  # (B, day-1)
            mu = mean[:, :day-1, i]  # (B, day-1)
            sigma = std[:, :day-1, i]  # (B, day-1)
            if self.use_mse:
                pred = p * torch.exp(mu) + batch['payment'][:, :day-1].cumsum(dim=1)  # (B, day-1)
            elif self.fixed_sigma is not None:
                pred = p * torch.exp(mu + self.fixed_sigma ** 2 / 2) + batch['payment'][:, :day-1].cumsum(dim=1)  # (B, day-1)
            else:
                pred = p * torch.exp(mu + sigma ** 2 / 2) + batch['payment'][:, :day-1].cumsum(dim=1)  # (B, day-1)
            pred_baseline = batch['payment'][:, :day-1].cumsum(dim=1) * torch.tensor(self.baseline_matrix[day-1][:-1]).to(batch['payment'].device)
            truth = batch['payment'][:, :day].sum(axis=1, keepdim=True)  # (B, 1)
            truth_expand = truth.expand_as(pred)  # (B, day-1)
            # RMSE
            self.log_dict({
                # RMSE
                f'val/rmse_{day}': F.mse_loss(pred, truth_expand) ** 0.5,
                f'val/rmse_{day}_baseline': F.mse_loss(pred_baseline, truth_expand) ** 0.5,
                # MAE
                f'val/mae_{day}': F.l1_loss(pred, truth_expand),
                f'val/mae_{day}_baseline': F.l1_loss(pred_baseline, truth_expand),
                # R2
                f'val/r2_{day}': 1 - F.mse_loss(pred, truth_expand) / F.mse_loss(truth.mean().expand_as(truth_expand), truth_expand),
                f'val/r2_{day}_baseline': 1 - F.mse_loss(pred_baseline, truth_expand) / F.mse_loss(truth.mean().expand_as(truth_expand), truth_expand),
                # NDCG
                f'val/ndcg_{day}': ndcg_score([truth_expand.flatten().cpu().numpy()], [pred.flatten().cpu().numpy()]),
                f'val/ndcg_{day}_baseline': ndcg_score([truth_expand.flatten().cpu().numpy()], [pred_baseline.flatten().cpu().numpy()]),
            })
        
            # add histogram
            self.logger.experiment.add_histogram(f'val/mu_{day}', mu, self.current_epoch)
            self.logger.experiment.add_histogram(f'val/sigma_{day}', sigma, self.current_epoch)
            self.logger.experiment.add_histogram(f'val/logits_{day}', logits, self.current_epoch)
            self.logger.experiment.add_histogram(f'val/truth_{day}_log1p', torch.log1p(truth), self.current_epoch)
            self.logger.experiment.add_histogram(f'val/pred_{day}_log1p', torch.log1p(pred), self.current_epoch)
            self.logger.experiment.add_histogram(f'val/pred_baseline_{day}_log1p', torch.log1p(pred_baseline), self.current_epoch)
            
            output.update({f"pred_{day}": pred, f"pred_baseline_{day}": pred_baseline, f"truth_expand_{day}": truth_expand})
        
        return output
    
    
    def validation_epoch_end(self, outputs):
        for day in [3, 7, 14]:
            pred = []
            baseline = []
            truth = []
            for output in outputs:
                _pred = output[f"pred_{day}"]
                _pred_baseline = output[f"pred_baseline_{day}"]
                _truth = output[f"truth_expand_{day}"]
                pred.append(_pred.flatten())
                baseline.append(_pred_baseline.flatten())
                truth.append(_truth.flatten())
            pred = torch.cat(pred, dim=0)
            baseline = torch.cat(baseline, dim=0)
            truth = torch.cat(truth, dim=0)
            # group mape
            group_results, mape = evaluate_ltv_distribution_torch(truth, pred)
            self.log(f"val/group_mape_{day}", mape)
            print("ours:", group_results)
            group_results, mape_baseline = evaluate_ltv_distribution_torch(truth, baseline)
            self.log(f"val/group_mape_{day}_baseline", mape_baseline)
            print("baseline:", group_results)
            
            shuffled_indices = torch.randperm(len(truth), generator=torch.Generator().manual_seed(42))
            pred_shuffled = pred[shuffled_indices]
            baseline_shuffled = baseline[shuffled_indices]
            truth_shuffled = truth[shuffled_indices]
            n = 15
            for i in range(n): # 2 ** 0 ~ 2 ** 14
                self.log(f"grmse_{day}/2**{i}", F.mse_loss(pred_shuffled, truth_shuffled) ** 0.5)
                self.log(f"grmse_{day}/2**{i}_baseline", F.mse_loss(baseline_shuffled, truth_shuffled) ** 0.5)
                pred_shuffled += torch.roll(pred_shuffled, dims=0, shifts=2 ** i)
                baseline_shuffled += torch.roll(baseline_shuffled, dims=0, shifts=2 ** i)
                truth_shuffled += torch.roll(truth_shuffled, dims=0, shifts=2 ** i)
                
                
    def configure_optimizers(self):
        return optim.AdamW(self.parameters(), lr=self.lr, weight_decay=self.weight_decay)
        # scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=5, eta_min=1e-8)
        # scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=3, min_lr=1e-10)
        # return {"optimizer": optimizer, "lr_scheduler": scheduler, "monitor": "val_loss"}