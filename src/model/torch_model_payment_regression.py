import torch
import torch.nn as nn
import torch.optim as optim
import pytorch_lightning as pl

class PaymentRegressionModel(pl.LightningModule):
    def __init__(self, 
                    seq_feature_dim=33,
                    seq_length=3,
                    user_feature_dim=2,
                    hidden_dim=128,
                    num_layers=8,
                    cat_dims=[2, 26, 500],  # 用户特征中每个类别的取值数量
                    embedding_dims=[10, 10, 10],  # 每个类别的嵌入维度
                    dropout=0.2,
                    ):
        super().__init__()
        self.save_hyperparameters()
        
        # 序列特征处理 - LSTM
        self.lstm = nn.LSTM(
            input_size=seq_feature_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )
        
        # 用户类别特征处理 - Embedding
        self.embeddings = nn.ModuleList([
            nn.Embedding(cat_dim, emb_dim)
            for cat_dim, emb_dim in zip(cat_dims, embedding_dims)
        ])
        
        # 计算全连接层输入维度
        self.total_embedding_dim = sum(embedding_dims)
        self.lstm_output_dim = hidden_dim
        
        # 全连接层
        self.fc_layers = nn.Sequential(
            nn.Linear(self.lstm_output_dim + self.total_embedding_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )
                
        self.loss_fn = nn.MSELoss()
        # self.loss_fn = nn.L1Loss()
        
    def forward(self, seq_feature, user_feature):
        # seq_feature shape: (batch_size, seq_length, seq_feature_dim)
        # user_feature shape: (batch_size, user_feature_dim)
        
        # 处理序列特征
        seq_feature = seq_feature.permute(0, 2, 1)
        lstm_out, _ = self.lstm(seq_feature)
        lstm_out = lstm_out[:, -1, :]  # 取最后一个时间步的输出
        
        # 处理用户类别特征
        user_feature = user_feature.long()
        embedded = []
        for i, embedding in enumerate(self.embeddings):
            embedded.append(embedding(user_feature[:, i]))
        embedded = torch.cat(embedded, dim=1)
        
        # 特征融合
        combined = torch.cat([lstm_out, embedded], dim=1)
        
        # 全连接层处理
        logits = self.fc_layers(combined)
        return logits 

    def training_step(self, batch, batch_idx):
        seq_feature, user_feature, targets = batch  # 解包 batch
        preds = self(seq_feature, user_feature).squeeze(-1)  # (batch_size,)
        loss = self.loss_fn(preds, targets)
        self.log("train_loss", loss, sync_dist=True)
        MSE_loss = nn.MSELoss()
        self.log("mse_loss", MSE_loss(preds, targets), sync_dist=True)
        return loss  # 总 loss 用于梯度更新

    def validation_step(self, batch, batch_idx):
        seq_feature, user_feature, label = batch
        preds = self.forward(seq_feature, user_feature)
        loss = self.loss_fn(preds, label.unsqueeze(1))
        self.log("val_loss", loss, sync_dist=True)
        return loss

    def configure_optimizers(self):
        optimizer = optim.Adam(self.parameters(), lr=1e-3)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=5, eta_min=1e-5)
        # scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=10, min_lr=1e-7)
        return {"optimizer": optimizer, "lr_scheduler": scheduler, "monitor": "val_loss"}
