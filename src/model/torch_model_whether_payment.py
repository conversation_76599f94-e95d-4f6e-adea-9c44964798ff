import torch
import torch.nn as nn
import torch.optim as optim
import pytorch_lightning as pl
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_curve, auc, confusion_matrix, precision_recall_curve

class WhetherPaymentModel(pl.LightningModule):
    # def __init__(self, output_dim=1):
    #     super().__init__()
        
    #     # Fully Connected Layers for Combined Features
    #     self.encoder = nn.Sequential(
    #         nn.Linear((3 * 13) + (2 * 1) + 10, 256),
    #         nn.ReLU(),
    #         nn.Linear(256, 128),
    #         nn.ReLU(),
    #         nn.Linear(128, 64),
    #         nn.ReLU()
    #     )
        
    #     # Fully Connected Layers for Classification
    #     self.classifier = nn.Sequential(
    #         nn.Linear(64, 128),
    #         nn.ReLU(),
    #         nn.Linear(128, 64),
    #         nn.ReLU(),
    #         nn.Linear(64, output_dim),
    #         nn.Sigmoid()
    #     )
        
    #     self.embedding = nn.Embedding(6, 10)
        
    #     self.loss_fn = nn.BCELoss()
    
    def __init__(self, 
                    seq_feature_dim=33,
                    seq_length=3,
                    user_feature_dim=2,
                    hidden_dim=128,
                    num_layers=8,
                    cat_dims=[2, 26, 500],  # 用户特征中每个类别的取值数量
                    embedding_dims=[10, 10, 10],  # 每个类别的嵌入维度
                    dropout=0.2,
                    ):
        super().__init__()
        self.save_hyperparameters()
        
        # 序列特征处理 - LSTM
        self.lstm = nn.LSTM(
            input_size=seq_feature_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )
        
        # 用户类别特征处理 - Embedding
        self.embeddings = nn.ModuleList([
            nn.Embedding(cat_dim, emb_dim)
            for cat_dim, emb_dim in zip(cat_dims, embedding_dims)
        ])
        
        # 计算全连接层输入维度
        self.total_embedding_dim = sum(embedding_dims)
        self.lstm_output_dim = hidden_dim
        
        # 全连接层
        self.fc_layers = nn.Sequential(
            nn.Linear(self.lstm_output_dim + self.total_embedding_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )
                
        self.loss_fn = nn.BCELoss()
        
    # def forward(self, seq_feature, user_feature):
    #     seq_feature = seq_feature.view(seq_feature.size(0), -1)
    #     user_feature = user_feature.view(user_feature.size(0), -1)
        
    #     x_cat = user_feature[:, -1].to(torch.long)
    #     x_num = torch.cat([seq_feature, user_feature[:, :-1]], dim=1)  # 拼接
        
    #     batch_size = x_num.size(0)

    #     # Embedding
    #     cat_embed = self.cat_embedding(x_cat).unsqueeze(1)  # (B, 1, E)
    #     num_embed = x_num.unsqueeze(2) * self.num_embed_weights.unsqueeze(0)  # (B, 41, E)
    #     embed_x = torch.cat([cat_embed, num_embed], dim=1)  # (B, 42, E)

    #     # -------- FM 二阶交叉项 --------
    #     summed = torch.sum(embed_x, dim=1)              # (B, E)
    #     squared = torch.sum(embed_x ** 2, dim=1)        # (B, E)
    #     fm_vector = 0.5 * (summed**2 - squared)         # (B, E)
    #     fm_second_order = self.fm_mlp(fm_vector)

    #     # # -------- Linear 部分 --------
    #     # linear_cat_out = self.linear_cat(x_cat).squeeze(1)  # (B,)
    #     # linear_num_out = self.linear_num(x_num).squeeze(1)  # (B,)
    #     # linear_out = linear_cat_out + linear_num_out        # (B,)

    #     # -------- DNN 部分 --------
    #     dnn_input = embed_x.view(batch_size, -1)      # (B, 42 * E)
    #     dnn_out = self.dnn(dnn_input)                 # (B, 4)
        
    #     # -------- 最终输出 --------
    #     logits = fm_second_order + dnn_out  # (B, num_classes)
    #     return torch.sigmoid(logits.unsqueeze(1)) 
    
    def forward(self, seq_feature, user_feature):
        # seq_feature shape: (batch_size, seq_length, seq_feature_dim)
        # user_feature shape: (batch_size, user_feature_dim)
        
        # 处理序列特征
        seq_feature = seq_feature.permute(0, 2, 1)
        lstm_out, _ = self.lstm(seq_feature)
        lstm_out = lstm_out[:, -1, :]  # 取最后一个时间步的输出
        
        # 处理用户类别特征
        user_feature = user_feature.long()
        embedded = []
        for i, embedding in enumerate(self.embeddings):
            embedded.append(embedding(user_feature[:, i]))
        embedded = torch.cat(embedded, dim=1)
        
        # 特征融合
        combined = torch.cat([lstm_out, embedded], dim=1)
        
        # 全连接层处理
        logits = self.fc_layers(combined)
        prob = torch.sigmoid(logits)  # 转换为概率
        return prob  
    


    def _compute_metrics(self, output, label):
        pred_labels = (output >= 0.5).float()
        label, pred_labels, output = label.cpu().numpy(), pred_labels.cpu().numpy(), output.detach().cpu().numpy()

        accuracy = accuracy_score(label, pred_labels)
        precision = precision_score(label, pred_labels, zero_division=0)
        recall = recall_score(label, pred_labels)
        f1 = f1_score(label, pred_labels)
        fpr, tpr, _ = roc_curve(label, output)
        roc_auc = auc(fpr, tpr)
        precision_curve, recall_curve, _ = precision_recall_curve(label, output)
        pr_auc = auc(recall_curve, precision_curve)

        return accuracy, precision, recall, f1, roc_auc, pr_auc, fpr, tpr, precision_curve, recall_curve, confusion_matrix(label, pred_labels)

    def _log_metrics(self, prefix, loss, metrics):
        accuracy, precision, recall, f1, roc_auc, pr_auc, *_ = metrics
        self.log(f'{prefix}_loss', loss, sync_dist=True)
        self.log(f'{prefix}_accuracy', accuracy, sync_dist=True)
        self.log(f'{prefix}_precision', precision, sync_dist=True)
        self.log(f'{prefix}_recall', recall, sync_dist=True)
        self.log(f'{prefix}_f1', f1, sync_dist=True)
        self.log(f'{prefix}_roc_auc', roc_auc, sync_dist=True)
        self.log(f'{prefix}_pr_auc', pr_auc, sync_dist=True)

    def _plot_and_log(self, prefix, metrics):
        _, _, _, _, roc_auc, pr_auc, fpr, tpr, precision_curve, recall_curve, cm = metrics

        # Confusion Matrix
        plt.figure(figsize=(6, 5))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=['No Payment', 'Have Payment'], yticklabels=['No Payment', 'Have Payment'])
        plt.title('Confusion Matrix')
        plt.xlabel('Predicted Labels')
        plt.ylabel('True Labels')
        self.logger.experiment.add_figure(f"{prefix} Confusion Matrix", plt.gcf())
        plt.close()

        # ROC Curve
        plt.figure(figsize=(6, 5))
        plt.plot(fpr, tpr, color='b', label=f'ROC AUC = {roc_auc:.2f}')
        plt.plot([0, 1], [0, 1], color='gray', linestyle='--')
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curve')
        plt.legend(loc='lower right')
        self.logger.experiment.add_figure(f"{prefix} ROC Curve", plt.gcf())
        plt.close()

        # PR Curve
        plt.figure(figsize=(6, 5))
        plt.plot(recall_curve, precision_curve, label=f'PR AUC = {pr_auc:.4f}')
        plt.xlabel('Recall')
        plt.ylabel('Precision')
        plt.title('Precision-Recall Curve')
        plt.legend()
        self.logger.experiment.add_figure(f"{prefix} PR Curve", plt.gcf())
        plt.close()

    def training_step(self, batch, batch_idx):
        seq_feature, user_feature, label = batch
        output = self(seq_feature, user_feature).squeeze()
        loss = self.loss_fn(output, label.float())

        metrics = self._compute_metrics(output, label)
        self.log('current_lr', self.trainer.optimizers[0].param_groups[0]['lr'], prog_bar=True)
        self._log_metrics("train", loss, metrics)
        self._plot_and_log("train", metrics)

        return loss

    def validation_step(self, batch, batch_idx):
        seq_feature, user_feature, label = batch
        output = self(seq_feature, user_feature).squeeze()
        loss = self.loss_fn(output, label.float())

        metrics = self._compute_metrics(output, label)
        self.log('current_lr', self.trainer.optimizers[0].param_groups[0]['lr'], prog_bar=True)
        self._log_metrics("val", loss, metrics)
        self._plot_and_log("val", metrics)

        return loss

    def configure_optimizers(self):
        optimizer = optim.Adam(self.parameters(), lr=1e-3)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=5, eta_min=1e-5)
        # scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, min_lr=1e-5, verbose = True)
        return {"optimizer": optimizer, "lr_scheduler": scheduler, "monitor": "val_loss"}
