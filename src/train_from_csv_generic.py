import os
import sys
from datetime import datetime
from pprint import pprint
from typing import List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sklearn.model_selection import train_test_split
import torch
from torch.utils.data import DataLoader
import pytorch_lightning as pl
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.callbacks import EarlyStopping, ModelCheckpoint

from dataset.light_data_generic import GenericDataModule
from model.torch_model_any_day_prediction import AnyDayModel
from dataset.generate_from_csv_new import process_csv_data_generic


class GradientLogger(pl.Callback):
    """梯度监控回调"""
    def on_after_backward(self, trainer, pl_module):
        print("Debug Gradients (Max Grad > 10):")
        for name, param in pl_module.named_parameters():
            if param.requires_grad and param.grad is not None:
                max_grad = param.grad.max().item()
                if max_grad > 10:
                    print(f"Layer: {name}")
                    print(
                        f"Mean Grad: {param.grad.mean().item():.4f}, Std Grad: {param.grad.std().item():.4f}, " +
                        f"Max Grad: {max_grad:.4f}, Min Grad: {param.grad.min().item():.4f}\n")


def train_from_csv_generic(csv_file_path: str, 
                          output_dir: str = "../data",
                          model_save_dir: str = "../pth_model",
                          batch_size: int = 2048,
                          num_workers: int = 6,
                          max_epochs: int = 500,
                          devices: List[int] = [0],
                          learning_rate: float = 1e-5,
                          weight_decay: float = 1e-3,
                          hidden_dim: int = 64,
                          use_mse: bool = False,
                          fixed_sigma: float = 1.0,
                          max_days: int = 14):
    """
    从CSV数据训练any day模型（通用版本）
    
    Args:
        csv_file_path: CSV文件路径
        output_dir: 数据输出目录
        model_save_dir: 模型保存目录
        batch_size: 批次大小
        num_workers: 数据加载器工作进程数
        max_epochs: 最大训练轮数
        devices: 使用的GPU设备ID列表
        learning_rate: 学习率
        weight_decay: 权重衰减
        hidden_dim: 隐藏层维度
        use_mse: 是否使用MSE损失
        fixed_sigma: 固定标准差
        max_days: 最大天数
    """
    
    print("=" * 60)
    print("Generic Any Day Model Training from CSV")
    print("=" * 60)
    
    # 步骤1: 处理CSV数据并生成pickle文件
    print("\nStep 1: Processing CSV data...")
    try:
        merged_df, analysis = process_csv_data_generic(csv_file_path, output_dir, new_data=True, max_days=max_days)
        print(f"✓ Data processing completed successfully!")
        print(f"  Final data shape: {merged_df.shape}")
        print(f"  Output directory: {output_dir}")
        
        # 打印分析结果
        print(f"  Data analysis:")
        print(f"    Key columns: {analysis['key_columns']}")
        print(f"    Categorical columns: {len(analysis['categorical_columns'])}")
        print(f"    Numerical columns: {len(analysis['numerical_columns'])}")
        print(f"    Target columns: {analysis['target_columns']}")
        print(f"    Behavior columns: {len(analysis['behavior_columns'])}")
        
    except Exception as e:
        print(f"✗ Data processing failed: {e}")
        return
    
    # 步骤2: 设置数据模块
    print("\nStep 2: Setting up data module...")
    pickle_path = os.path.join(output_dir, 'any_day_data.pkl')
    
    if not os.path.exists(pickle_path):
        print(f"✗ Pickle file not found: {pickle_path}")
        return
    
    dm = GenericDataModule(
        pickle_path=pickle_path,
        batch_size=batch_size,
        num_workers=num_workers,
        max_seq_len=max_days
    )
    
    try:
        dm.setup()
        print(f"✓ Data module setup completed!")
        print(f"  Training samples: {len(dm.train_set)}")
        print(f"  Validation samples: {len(dm.val_set)}")
        print(f"  Payment cumsum mean shape: {len(dm.payment_cumsum_mean)}")
        
    except Exception as e:
        print(f"✗ Data module setup failed: {e}")
        return
    
    # 步骤3: 创建模型
    print("\nStep 3: Creating model...")
    try:
        model = AnyDayModel(
            hidden_dim=hidden_dim,
            payment_cumsum_mean=dm.payment_cumsum_mean,
            weight_decay=weight_decay,
            use_mse=use_mse,
            fixed_sigma=fixed_sigma,
            lr=learning_rate,
            max_date=max_days
        )
        
        print(f"✓ Model created successfully!")
        print(f"  Hidden dimension: {hidden_dim}")
        print(f"  Learning rate: {learning_rate}")
        print(f"  Weight decay: {weight_decay}")
        print(f"  Use MSE: {use_mse}")
        print(f"  Fixed sigma: {fixed_sigma}")
        print(f"  Max days: {max_days}")
        
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return
    
    # 步骤4: 设置训练器
    print("\nStep 4: Setting up trainer...")
    
    # 创建目录
    log_dir = os.path.join(os.path.dirname(__file__), '../logs/')
    os.makedirs(log_dir, exist_ok=True)
    os.makedirs(model_save_dir, exist_ok=True)
    
    # 设置日志记录器
    logger = TensorBoardLogger(
        save_dir=log_dir,
        name='adp_csv_generic',
        version=f"v1-{datetime.now().strftime('%m%d-%H%M')}",
        default_hp_metric=False,
    )
    
    # 设置回调函数
    callbacks = []
    
    # 早停回调
    early_stop_callback = EarlyStopping(
        monitor="val/total_loss",
        mode="min",
        patience=15,
        verbose=True
    )
    callbacks.append(early_stop_callback)
    
    # 模型检查点回调
    checkpoint_callback = ModelCheckpoint(
        dirpath=model_save_dir,
        filename='any_day_prediction_model_csv_generic_{epoch:02d}_{val_total_loss:.4f}',
        monitor='val/total_loss',
        mode='min',
        save_top_k=3,
        save_last=True
    )
    callbacks.append(checkpoint_callback)
    
    # 创建训练器
    trainer = pl.Trainer(
        max_epochs=max_epochs,
        devices=devices,
        accelerator="gpu" if torch.cuda.is_available() else "cpu",
        logger=logger,
        callbacks=callbacks,
        log_every_n_steps=1,
        gradient_clip_val=0.1,
        gradient_clip_algorithm="norm",
    )
    
    print(f"✓ Trainer setup completed!")
    print(f"  Max epochs: {max_epochs}")
    print(f"  Devices: {devices}")
    print(f"  Log directory: {log_dir}")
    print(f"  Model save directory: {model_save_dir}")
    
    # 步骤5: 开始训练
    print("\nStep 5: Starting training...")
    try:
        trainer.fit(model, datamodule=dm)
        
        print(f"✓ Training completed successfully!")
        
        # 保存最终模型
        final_model_path = os.path.join(model_save_dir, 'any_day_prediction_model_csv_generic_final.pth')
        torch.save(model.state_dict(), final_model_path)
        print(f"  Final model saved to: {final_model_path}")
        
        # 打印最佳模型路径
        if checkpoint_callback.best_model_path:
            print(f"  Best model saved to: {checkpoint_callback.best_model_path}")
        
    except Exception as e:
        print(f"✗ Training failed: {e}")
        return
    
    print("\n" + "=" * 60)
    print("Training completed!")
    print("=" * 60)


def main():
    """主函数"""
    # 检查CSV文件
    csv_files = [
        "../csv_data/subtask_2624_20250715105822.csv",
        "../csv_data/subtask_2625_20250716120249.csv"
    ]
    
    csv_file = None
    for file_path in csv_files:
        if os.path.exists(file_path):
            csv_file = file_path
            break
    
    if csv_file is None:
        print("No valid CSV files found in csv_data directory")
        return
    
    # 训练参数
    train_config = {
        'csv_file_path': csv_file,
        'output_dir': '../data',
        'model_save_dir': '../pth_model',
        'batch_size': 2048,
        'num_workers': 6,
        'max_epochs': 500,
        'devices': [0],  # 根据实际GPU情况调整
        'learning_rate': 1e-5,
        'weight_decay': 1e-3,
        'hidden_dim': 64,
        'use_mse': False,
        'fixed_sigma': 1.0,
        'max_days': 14
    }
    
    print("Training configuration:")
    pprint(train_config)
    
    # 开始训练
    train_from_csv_generic(**train_config)


if __name__ == "__main__":
    main() 