import os
os.environ["CUDA_VISIBLE_DEVICES"] = "2,3"

import pytorch_lightning as pl
from torch.utils.data import DataLoader
from sklearn.model_selection import train_test_split

from dataset.light_data_whether_payment import WhetherPaymentDataModule

# from model.torch_model import LightModel
from model.torch_model_whether_payment import WhetherPaymentModel
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.callbacks import EarlyStopping

logger = TensorBoardLogger("tb_logs", name="will_stay_prediction")

# Ensure dataset loading happens only once
if __name__ == "__main__":
    dm = WhetherPaymentDataModule(
        pt_path = 'data/new_base_data_3_7.pt',
        batch_size=8192
    )
    
    dm.setup()
    train_loader = dm.train_dataloader()
    
    model = WhetherPaymentModel()
    
    early_stop_callback = EarlyStopping(
        monitor="val_loss", 
        mode="min",  
        patience=10, 
        verbose=True  
    )
    
    trainer = pl.Trainer(
        max_epochs=500,
        devices=[0,1],
        accelerator="gpu",
        logger=TensorBoardLogger("tb_logs", name="whether_payment"),
        log_every_n_steps=1,
        num_sanity_val_steps=0, 
        callbacks=[early_stop_callback]
    )
    
    trainer.fit(model, datamodule=dm)
