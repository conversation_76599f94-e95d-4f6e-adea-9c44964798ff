import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

import os
import numpy as np
import pytorch_lightning as pl
# from torch.utils.data import DataLoader
# from sklearn.model_selection import train_test_split

from dataset.light_data_whether_payment import WhetherPaymentDataModule
from catboost import CatBoostClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, confusion_matrix, classification_report


def run_1_catboost():
    dm = WhetherPaymentDataModule(
        pt_path = '/wkspace/sunyi/LTV_prediction/src/data/base_data_3_7.pt',
        batch_size=8192
    )
    
    dm.setup()
    # train_loader = dm.train_dataloader()
    print(type(dm.train_data))
    print(type(dm.val_data))
    print(dm.train_data[0])
    print(dm.val_data[0])
    
    train_X, train_y = [], []
    test_X, test_y = [], []
    
    for seq_feature, user_feature, future_payment, user_id, tm in dm.train_data:
        # seq_feature = np.concatenate((seq_feature[:5, :], seq_feature[-2:, :]), axis=0)  # 去除新增特征
        train_X.append(seq_feature.flatten().tolist() + user_feature.tolist())
        train_y.append(future_payment > 0)
    
    for seq_feature, user_feature, future_payment, user_id, tm in dm.val_data:
        # seq_feature = np.concatenate((seq_feature[:5, :], seq_feature[-2:, :]), axis=0)  # 去除新增特征
        test_X.append(seq_feature.flatten().tolist() + user_feature.tolist())
        test_y.append(future_payment > 0)
    
    print(train_X[0])
    print(train_y[0])
    print(test_X[0])
    print(test_y[0])
    # exit(0)
    model = CatBoostClassifier(
        iterations=2000,
        learning_rate=2e-2,
        depth=7,
        eval_metric='Accuracy',
        random_seed=42,
        l2_leaf_reg=1e-1,
        verbose=10 ,
        thread_count=20,
    )

    # 训练模型
    model.fit(
        train_X, train_y,
        eval_set=(test_X, test_y),
    )

    # 预测与评估
    pred_y = model.predict(test_X)
    print(f"Accuracy: {accuracy_score(test_y, pred_y):.4f}")
    print(f"confusion_matrix: ")
    print(confusion_matrix(test_y, pred_y))
    print(f"classification_report: ")
    print(classification_report(test_y, pred_y, digits=4))
    print(model.get_params())
    
    # 特征重要性
    print("\nFeature Importance:")
    print(model.get_feature_importance(prettified=True))
    
    
"""
------------------------------------------------------------
使用全量数据的情况下，3日预测4-7日是否付费
Accuracy: 0.8223

confusion_matrix: 
        [[6803 1553]
        [1411 6909]]
            
classification_report:           
               precision    recall  f1-score   support

       False     0.8282    0.8141    0.8211      8356
        True     0.8165    0.8304    0.8234      8320

    accuracy                         0.8223     16676
   macro avg     0.8223    0.8223    0.8223     16676
weighted avg     0.8224    0.8223    0.8222     16676
    
------------------------------------------------------------
去除新增特征的情况下，3日预测4-7日是否付费
Accuracy: 0.8182
confusion_matrix: 
        [[6755 1601]
        [1431 6889]]
        
classification_report:           
               precision    recall  f1-score   support

       False     0.8252    0.8084    0.8167      8356
        True     0.8114    0.8280    0.8196      8320

    accuracy                         0.8182     16676
   macro avg     0.8183    0.8182    0.8182     16676
weighted avg     0.8183    0.8182    0.8182     16676
    
    
    
"""
    
if __name__ == "__main__":
    run_1_catboost()  
    