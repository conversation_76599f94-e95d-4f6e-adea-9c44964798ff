import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

import os
import numpy as np

from catboost import CatBoostClassifier, CatBoostRegressor
from dataset.light_data_payment_regression import PaymentRegressionDataModule
from sklearn.metrics import accuracy_score, confusion_matrix, classification_report, r2_score


def run_3_catboost():
    dm = PaymentRegressionDataModule(
        pt_path = '/wkspace/sunyi/LTV_prediction/src/data/base_data_3_7.pt',
        batch_size=4096
    )
    dm.setup()
    
    train_X, train_y = [], []
    test_X, test_y = [], []
    
    for seq_feature, user_feature, future_payment, user_id, tm in dm.train_data:
        seq_feature = np.concatenate((seq_feature[:5, :], seq_feature[-2:, :]), axis=0)  # 去除新增特征
        train_X.append(seq_feature.flatten().tolist() + user_feature.tolist())
        # train_y.append(np.log(future_payment + 1))
        train_y.append(future_payment)
    
    for seq_feature, user_feature, future_payment, user_id, tm in dm.val_data:
        seq_feature = np.concatenate((seq_feature[:5, :], seq_feature[-2:, :]), axis=0)  # 去除新增特征
        test_X.append(seq_feature.flatten().tolist() + user_feature.tolist())
        # test_y.append(np.log(future_payment + 1))
        test_y.append(future_payment)
        
        
    model = CatBoostRegressor(
        iterations=2000,
        learning_rate=2e-2,
        depth=7,
        eval_metric='MAE',
        loss_function='MAE', 
        random_seed=42,
        l2_leaf_reg=10,
        verbose=10,
        thread_count=30,
    )

    # 训练模型
    model.fit(
        train_X, train_y,
        eval_set=(test_X, test_y),
    )

    # 预测与评估
    pred_y = model.predict(test_X)
    print(f"RMSE: {np.sqrt(np.mean((test_y - pred_y) ** 2)):.4f}")
    print(f"MAE: {np.mean(np.abs(test_y - pred_y)):.4f}")
    print(f"R2: {r2_score(test_y, pred_y):.4f}")
    
    # 特征重要性
    print("\nFeature Importance:")
    print(model.get_feature_importance(prettified=True))
    
    
"""
------------------------------------------------------------
使用全量数据的情况
对4-7日付费做log变换, loss_function=RMSE:
    RMSE: 1.1717
    MAE: 0.9292
    R2: 0.4272

对4-7日付费不做log变换, loss_function=RMSE:
    RMSE: 847.9075
    MAE: 318.2388
    R2: 0.9833
    
------------------------------------------------------------
去除新增特征的情况
对4-7日付费做log变换后, loss_function=RMSE:
    RMSE: 1.1757
    MAE: 0.9338
    R2: 0.4233

对4-7日付费不做log变换, loss_function=RMSE:
    RMSE: 851.9816
    MAE: 316.8056
    R2: 0.9831
  
"""
    
if __name__ == "__main__":
    run_3_catboost()  
   