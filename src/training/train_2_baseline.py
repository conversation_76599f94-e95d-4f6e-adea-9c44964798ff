import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from config import config
import os
import numpy as np
import pytorch_lightning as pl
# from torch.utils.data import DataLoader
# from sklearn.model_selection import train_test_split
from catboost import CatBoostClassifier
from dataset.light_data_payment_buckets import PaymentBucketsDataModule
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, confusion_matrix, classification_report

from dataset.load_data import Fish3DLTVDataset


def load_data():
    """ 数据集v1-0330 """
    dm = PaymentBucketsDataModule(
        pt_path = '/wkspace/sunyi/LTV_prediction/src/data/base_data_3_7.pt',
        batch_size=4096
    )
    
    dm.setup()
    
    train_X, train_y = [], []
    test_X, test_y = [], []
    
    for seq_feature, user_feature, future_payment, user_id, tm in dm.train_data:
        # seq_feature = np.concatenate((seq_feature[:5, :], seq_feature[-2:, :]), axis=0)  # 去除新增特征
        train_X.append(seq_feature.flatten().tolist() + user_feature.tolist())
        label = 0 if future_payment <= 6 else 1 if future_payment <= 50 else 2 if future_payment <= 500 else 3
        train_y.append(label)
    
    for seq_feature, user_feature, future_payment, user_id, tm in dm.val_data:
        # seq_feature = np.concatenate((seq_feature[:5, :], seq_feature[-2:, :]), axis=0)  # 去除新增特征
        test_X.append(seq_feature.flatten().tolist() + user_feature.tolist())
        label = 0 if future_payment <= 6 else 1 if future_payment <= 50 else 2 if future_payment <= 500 else 3
        test_y.append(label)
        
    return train_X, train_y, test_X, test_y


def load_full_data():
    train_X, train_y = [], []
    test_X, test_y = [], []
    start_ds = '2025-01-01'
    end_ds = '2025-03-31'
    ltv_dataset = Fish3DLTVDataset(
        start_ds=start_ds,
        end_ds=end_ds,
        config=config,
        campaign_data_path=os.path.join(project_root, '../data/subtask_1034_20250408115733.csv'),
        behavior_data_path=os.path.join(project_root, '../data/'),
    )
    payment_drop_cols = [f'd{i}_payment' for i in range(4, 31)]
    retention_drop_cols = [f'd{i}_retention' for i in range(4, 31)]
    payment_label_cols = [f'd{i}_payment' for i in range(4, 8)]
    retention_label_cols = [f'd{i}_retention' for i in range(4, 8)]
    drop_cols = ['sdk_yidun_device_id', 'attribution_day']
    
    y = ltv_dataset.df[payment_label_cols].sum(axis=1)
    df = ltv_dataset.df.drop(columns=payment_drop_cols + retention_drop_cols + drop_cols)
    X = df.to_numpy()
    train_X, test_X, train_y, test_y = train_test_split(X, y, test_size=0.2, random_state=42)
    return train_X, train_y, test_X, test_y


def run_2_catboost():
    # train_X, train_y, test_X, test_y = load_data()
    train_X, train_y, test_X, test_y = load_full_data()
    model = CatBoostClassifier(
        iterations=5000,
        learning_rate=2e-2,
        depth=8,
        eval_metric='Accuracy',
        loss_function='MultiClass', 
        classes_count=4,
        random_seed=42,
        l2_leaf_reg=100.0,
        verbose=10,
        thread_count=30,
    )

    # 训练模型
    model.fit(
        train_X, train_y,
        eval_set=(test_X, test_y),
    )

    # 预测与评估
    pred_y = model.predict(test_X)
    print(f"Accuracy: {accuracy_score(test_y, pred_y):.4f}")
    print(f"confusion_matrix: ")
    print(confusion_matrix(test_y, pred_y))
    print(f"classification_report: ")
    print(classification_report(test_y, pred_y, digits=4))
    print(model.get_params())
    
    # 特征重要性
    print("\nFeature Importance:")
    print(model.get_feature_importance(prettified=True))
    
    
"""
------------------------------------------------------------
使用数据集v1-0330的情况

Accuracy: 0.5074
confusion_matrix:
[[1108  987  133   11]
 [ 641 1763  467   36]
 [ 285  889  980  169]
 [  37  151  301  380]]
 
classification_report:
              precision    recall  f1-score   support

           0     0.5350    0.4949    0.5142      2239
           1     0.4652    0.6065    0.5265      2907
           2     0.5210    0.4219    0.4662      2323
           3     0.6376    0.4373    0.5188       869

    accuracy                         0.5074      8338
   macro avg     0.5397    0.4901    0.5064      8338
weighted avg     0.5174    0.5074    0.5056      8338

    
------------------------------------------------------------
数据集v1-0330去除新增特征的情况

Accuracy: 0.5053
confusion_matrix:
[[1118  958  151   12]
 [ 641 1746  484   36]
 [ 288  885  962  188]
 [  37  144  301  387]]
 
classification_report:
              precision    recall  f1-score   support

           0     0.5365    0.4993    0.5172      2239
           1     0.4677    0.6006    0.5259      2907
           2     0.5068    0.4141    0.4558      2323
           3     0.6212    0.4453    0.5188       869

    accuracy                         0.5053      8338
   macro avg     0.5331    0.4899    0.5044      8338
weighted avg     0.5131    0.5053    0.5033      8338

  
"""
    
if __name__ == "__main__":
    load_full_data()
    # run_2_catboost()  
    