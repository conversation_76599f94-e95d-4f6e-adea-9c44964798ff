# LTV预测模型数据内容分析报告

## 1. 数据概览

### 基本信息
- **总数据量**: 641,373 条记录
- **特征维度**: 123 个特征
- **数据完整性**: 大部分特征完整，仅有少量时间相关字段存在空值

### 数据分布
- **付费用户**: 66,281 人 (10.33%)
- **非付费用户**: 575,092 人 (89.67%)
- **总体付费金额**: 3,775,153.63
- **平均付费金额**: 5.89
- **付费金额中位数**: 0.00 (说明付费分布极不均匀)

## 2. 空值分析

### 存在空值的字段
1. **adtrace_pay_times**: 473,764 条空值 (73.87%)
2. **sdk_fileinittime**: 426,268 条空值 (66.46%)
3. **sdk_timezone**: 425,959 条空值 (66.41%)
4. **click_time**: 101,554 条空值 (15.83%)

### 分析结论
- 时间相关字段存在较多空值，可能与数据收集机制有关
- 核心业务特征（付费、留存、设备信息等）基本完整
- 空值主要集中在非核心字段，对模型训练影响有限

## 3. 付费行为分析

### 付费趋势
- **Day 1**: 37,820 付费用户 (5.90%)，平均付费 23.44
- **Day 2**: 21,966 付费用户 (3.42%)，平均付费 29.03
- **Day 3**: 12,502 付费用户 (1.95%)，平均付费 32.88
- **Day 7**: 4,619 付费用户 (0.72%)，平均付费 43.18
- **Day 14**: 1,394 付费用户 (0.22%)，平均付费 45.92

### 关键发现
1. **付费衰减明显**: 从Day 1的5.90%快速下降到Day 7的0.72%
2. **平均付费递增**: 从Day 1的23.44增长到Day 14的45.92
3. **数据截止**: Day 19-30无付费数据，可能为数据收集时间限制
4. **付费集中度**: 前7天贡献了大部分付费用户和金额

## 4. 留存行为分析

### 留存趋势
- **Day 1**: 37,809 留存用户 (5.90%)
- **Day 2**: 21,592 留存用户 (3.37%)
- **Day 3**: 12,410 留存用户 (1.93%)
- **Day 7**: 4,579 留存用户 (0.71%)
- **Day 14**: 1,384 留存用户 (0.22%)

### 关键发现
1. **留存与付费高度相关**: 留存用户几乎都是付费用户
2. **留存衰减模式**: 与付费衰减模式基本一致
3. **留存率**: 整体留存率较低，符合移动游戏行业特点

## 5. 分类特征分析

### 设备特征
- **操作系统**: 主要分为3类，其中第1类占66.41%
- **语言**: 178种语言，前3种占99.85%
- **硬件**: 158种硬件类型，分布相对分散
- **运营商**: 49种运营商，前2种占97.55%

### 渠道特征
- **主渠道**: 12个主渠道，分布相对均匀
- **子渠道**: 8个子渠道，第4类占62.44%
- **虚拟渠道**: 5种类型，第5类占60.86%

### 应用特征
- **包名**: 11种包名，分布相对均匀
- **游戏类型**: 主要为类型1和2
- **平台**: 12个平台，分布相对均匀

## 6. 数值特征分析

### 关键数值特征
1. **adtrace_click_match_count**: 均值1.71，中位数1.0，最大值256
2. **sdk_disk**: 均值71.3GB，中位数0，说明大部分设备未记录磁盘信息
3. **sdk_memory**: 均值2.0GB，中位数0，说明大部分设备未记录内存信息
4. **proj_game_type**: 均值1.66，主要为类型1和2
5. **adtrace_reattributed**: 均值0.34，约34%的用户被重新归因

## 7. 付费用户特征对比

### 设备特征差异
- **磁盘空间**: 付费用户平均109.3GB vs 非付费用户67.0GB
- **内存**: 付费用户平均3.2GB vs 非付费用户1.9GB
- **游戏类型**: 付费用户偏好类型1，非付费用户偏好类型2

### 关键发现
1. **设备配置**: 付费用户设备配置明显更好
2. **游戏偏好**: 不同游戏类型的付费意愿存在差异
3. **特征区分度**: 设备特征对付费预测有较强区分能力

## 8. 留存与付费关系

### 重叠度分析
- **Day 1**: 留存且付费 37,791 (5.89%)，几乎完全重叠
- **Day 7**: 留存且付费 4,572 (0.71%)，重叠度仍然很高

### 关键发现
1. **高度相关**: 留存用户几乎都是付费用户
2. **因果关系**: 付费可能是留存的主要驱动因素
3. **预测价值**: 留存特征对付费预测有重要价值

## 9. 数据质量评估

### 优势
1. **数据完整性**: 核心业务特征完整
2. **特征丰富**: 包含设备、渠道、行为等多维度特征
3. **标签明确**: 付费和留存标签清晰
4. **时间序列**: 包含30天的时序数据

### 挑战
1. **数据不平衡**: 付费用户仅占10.33%
2. **特征稀疏**: 部分数值特征存在大量零值
3. **时间限制**: 仅包含18天的有效数据
4. **特征编码**: 分类特征需要编码处理

## 10. 建模建议

### 特征工程
1. **处理空值**: 对时间字段进行合理填充
2. **特征编码**: 对分类特征进行编码
3. **特征组合**: 结合设备、渠道、行为特征
4. **时序特征**: 利用留存序列预测付费

### 模型策略
1. **处理不平衡**: 使用采样或权重调整
2. **多任务学习**: 同时预测留存和付费
3. **时序建模**: 利用Transformer等序列模型
4. **集成学习**: 结合多个模型的预测结果

### 评估指标
1. **AUC**: 评估分类性能
2. **RMSE/MAE**: 评估金额预测精度
3. **NDCG**: 评估排序质量
4. **业务指标**: 关注实际业务价值

## 11. 结论

该数据集具有以下特点：
1. **数据规模适中**: 64万条记录，适合深度学习模型
2. **特征维度丰富**: 123个特征，包含多维度信息
3. **标签分布合理**: 付费率10.33%，符合移动游戏行业特点
4. **时序特征明显**: 留存和付费都存在明显的时间衰减模式
5. **特征区分度好**: 设备特征对付费预测有较强区分能力

建议采用基于Transformer的序列模型，结合多任务学习策略，充分利用时序特征和丰富的设备、渠道信息来提升LTV预测效果。 