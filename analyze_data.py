import pandas as pd
import numpy as np
import json
from collections import defaultdict

def analyze_data():
    print("正在加载数据...")
    # 加载数据
    df = pd.read_pickle('data/any_day_data.pkl')
    
    print(f"数据形状: {df.shape}")
    print(f"总行数: {len(df):,}")
    print(f"总列数: {len(df.columns)}")
    
    # 1. 空值分析
    print("\n" + "="*50)
    print("空值分析")
    print("="*50)
    
    null_counts = df.isnull().sum()
    null_percentages = (null_counts / len(df)) * 100
    
    print("空值统计:")
    for col in df.columns:
        if null_counts[col] > 0:
            print(f"{col}: {null_counts[col]:,} ({null_percentages[col]:.2f}%)")
    
    # 2. 付费相关分析
    print("\n" + "="*50)
    print("付费分析")
    print("="*50)
    
    payment_cols = [col for col in df.columns if 'payment' in col]
    retention_cols = [col for col in df.columns if 'retention' in col]
    
    print(f"付费列数量: {len(payment_cols)}")
    print(f"留存列数量: {len(retention_cols)}")
    
    # 总体付费情况
    total_payment = df[payment_cols].sum(axis=1)
    print(f"总体付费用户数: {(total_payment > 0).sum():,} ({(total_payment > 0).mean()*100:.2f}%)")
    print(f"总体付费金额: {total_payment.sum():,.2f}")
    print(f"平均付费金额: {total_payment.mean():.2f}")
    print(f"付费金额中位数: {total_payment.median():.2f}")
    
    # 各天付费情况
    print("\n各天付费情况:")
    for i in range(1, 31):
        col = f'd{i}_payment'
        if col in df.columns:
            day_payment = df[col]
            paid_users = (day_payment > 0).sum()
            paid_rate = (day_payment > 0).mean() * 100
            total_amount = day_payment.sum()
            avg_amount = day_payment[day_payment > 0].mean() if paid_users > 0 else 0
            print(f"Day {i:2d}: 付费用户 {paid_users:6,} ({paid_rate:5.2f}%) | 总金额 {total_amount:8,.2f} | 平均付费 {avg_amount:6.2f}")
    
    # 3. 留存分析
    print("\n" + "="*50)
    print("留存分析")
    print("="*50)
    
    print("各天留存情况:")
    for i in range(1, 31):
        col = f'd{i}_retention'
        if col in df.columns:
            retention = df[col]
            retained_users = (retention > 0).sum()
            retention_rate = (retention > 0).mean() * 100
            print(f"Day {i:2d}: 留存用户 {retained_users:6,} ({retention_rate:5.2f}%)")
    
    # 4. 分类特征分析
    print("\n" + "="*50)
    print("分类特征分析")
    print("="*50)
    
    categorical_cols = [
        'sdk_os_name', 'sdk_language', 'sdk_hardware_name', 
        'sdk_carrierinfo', 'sdk_systemversion', 'adtrace_act_name',
        'adtrace_platform', 'proj_main_channel', 'proj_sub_channel',
        'sdk_os_version', 'sdk_virtual_channel', 'sdk_package_name',
        'proj_virtual_channel', 'sdk_machine', 'sdk_model',
        'sdk_carrier_name', 'adtrace_main_platform', 'sdk_countrycode'
    ]
    
    for col in categorical_cols:
        if col in df.columns:
            unique_count = df[col].nunique()
            null_count = df[col].isnull().sum()
            null_rate = (null_count / len(df)) * 100
            
            print(f"{col}:")
            print(f"  独特值数量: {unique_count}")
            print(f"  空值数量: {null_count} ({null_rate:.2f}%)")
            
            # 显示前5个最常见的值
            value_counts = df[col].value_counts().head(5)
            print(f"  前5个最常见值:")
            for val, count in value_counts.items():
                percentage = (count / len(df)) * 100
                print(f"    {val}: {count:,} ({percentage:.2f}%)")
            print()
    
    # 5. 数值特征分析
    print("\n" + "="*50)
    print("数值特征分析")
    print("="*50)
    
    numerical_cols = [
        'adtrace_organic_traffic', 'adtrace_click_match_count', 
        'adtrace_is_greylist', 'adtrace_ctit', 'adtrace_is_blacklist',
        'sdk_split_ua_result', 'sdk_disk', 'sdk_memory', 'proj_game_type',
        'adtrace_reattributed', 'channel_ua'
    ]
    
    for col in numerical_cols:
        if col in df.columns:
            print(f"{col}:")
            print(f"  均值: {df[col].mean():.4f}")
            print(f"  中位数: {df[col].median():.4f}")
            print(f"  标准差: {df[col].std():.4f}")
            print(f"  最小值: {df[col].min():.4f}")
            print(f"  最大值: {df[col].max():.4f}")
            print(f"  空值数量: {df[col].isnull().sum()}")
            print()
    
    # 6. 付费用户特征分析
    print("\n" + "="*50)
    print("付费用户 vs 非付费用户特征对比")
    print("="*50)
    
    is_paid = total_payment > 0
    paid_df = df[is_paid]
    unpaid_df = df[~is_paid]
    
    print(f"付费用户数: {len(paid_df):,} ({is_paid.mean()*100:.2f}%)")
    print(f"非付费用户数: {len(unpaid_df):,} ({(~is_paid).mean()*100:.2f}%)")
    
    # 对比一些关键特征
    key_features = ['sdk_disk', 'sdk_memory', 'proj_game_type', 'channel_ua']
    
    for feature in key_features:
        if feature in df.columns:
            print(f"\n{feature} 对比:")
            print(f"  付费用户均值: {paid_df[feature].mean():.4f}")
            print(f"  非付费用户均值: {unpaid_df[feature].mean():.4f}")
            print(f"  差异: {paid_df[feature].mean() - unpaid_df[feature].mean():.4f}")
    
    # 7. 留存与付费关系
    print("\n" + "="*50)
    print("留存与付费关系")
    print("="*50)
    
    for i in range(1, 8):  # 只看前7天
        retention_col = f'd{i}_retention'
        payment_col = f'd{i}_payment'
        
        if retention_col in df.columns and payment_col in df.columns:
            retained = df[retention_col] > 0
            paid = df[payment_col] > 0
            
            retained_and_paid = (retained & paid).sum()
            retained_not_paid = (retained & ~paid).sum()
            not_retained_but_paid = (~retained & paid).sum()
            neither = (~retained & ~paid).sum()
            
            print(f"Day {i}:")
            print(f"  留存且付费: {retained_and_paid:,} ({retained_and_paid/len(df)*100:.2f}%)")
            print(f"  留存未付费: {retained_not_paid:,} ({retained_not_paid/len(df)*100:.2f}%)")
            print(f"  未留存但付费: {not_retained_but_paid:,} ({not_retained_but_paid/len(df)*100:.2f}%)")
            print(f"  未留存未付费: {neither:,} ({neither/len(df)*100:.2f}%)")
            print()

if __name__ == "__main__":
    analyze_data() 