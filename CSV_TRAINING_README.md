# CSV数据处理和训练指南

本指南介绍如何使用CSV数据从头开始建立pickle文件，进行编码，并训练any day模型。

## 文件结构

```
src/
├── dataset/
│   ├── generate_from_csv.py          # 从CSV生成pickle文件
│   ├── process_csv_behavior.py       # 处理CSV行为数据
│   └── light_data_any_day_prediction.py  # 数据加载模块
├── model/
│   └── torch_model_any_day_prediction.py  # 模型定义
├── train_from_csv.py                 # 从CSV训练模型
└── run_csv_training.py               # 完整训练流程脚本
```

## 快速开始

### 1. 数据处理

首先处理CSV数据并生成pickle文件：

```bash
cd src
python dataset/generate_from_csv.py
```

或者使用完整的工作流程脚本：

```bash
cd src
python run_csv_training.py
```

### 2. 训练模型

使用生成的pickle文件训练模型：

```bash
cd src
python train_from_csv.py
```

## 详细使用说明

### 数据处理脚本

#### `generate_from_csv.py`

这个脚本模仿了 `generate_any_day.py` 的结构，从CSV文件生成pickle文件。

**功能：**
- 加载CSV数据
- 分离归因数据和行为数据
- 数据清洗和预处理
- 类别特征编码
- 生成pickle文件

**使用方法：**
```python
from dataset.generate_from_csv import process_csv_data

# 处理CSV数据
merged_df = process_csv_data(
    csv_file_path="csv_data/subtask_2624_20250715105822.csv",
    output_dir="../data",
    new_data=True
)
```

#### `process_csv_behavior.py`

专门处理CSV中的行为数据，创建行为序列。

**功能：**
- 提取行为特征
- 创建14天的行为序列
- 数据清洗和标准化

### 训练脚本

#### `train_from_csv.py`

从CSV数据训练any day模型的完整脚本。

**主要参数：**
- `csv_file_path`: CSV文件路径
- `batch_size`: 批次大小 (默认: 2048)
- `num_workers`: 数据加载器工作进程数 (默认: 6)
- `max_epochs`: 最大训练轮数 (默认: 500)
- `learning_rate`: 学习率 (默认: 1e-5)
- `hidden_dim`: 隐藏层维度 (默认: 64)

#### `run_csv_training.py`

完整的工作流程脚本，包含所有步骤。

**命令行参数：**
```bash
python run_csv_training.py \
    --csv_file csv_data/subtask_2624_20250715105822.csv \
    --batch_size 2048 \
    --max_epochs 500 \
    --learning_rate 1e-5 \
    --hidden_dim 64 \
    --devices 0
```

## 工作流程

### 步骤1: 数据处理
1. 加载CSV文件
2. 分离归因数据和行为数据
3. 数据清洗（删除重复用户、缺失值处理）
4. 类别特征编码
5. 生成pickle文件

### 步骤2: 数据模块设置
1. 加载pickle文件
2. 创建训练和验证数据集
3. 设置数据加载器

### 步骤3: 模型创建
1. 创建AnyDayModel实例
2. 设置模型参数
3. 初始化模型权重

### 步骤4: 训练器设置
1. 设置TensorBoard日志记录
2. 配置早停和模型检查点回调
3. 创建PyTorch Lightning训练器

### 步骤5: 模型训练
1. 开始训练
2. 监控训练过程
3. 保存最佳模型

## 输出文件

### 数据处理输出
- `../data/any_day_data.pkl`: 处理后的数据
- `../data/encoding_map.json`: 类别特征编码映射

### 训练输出
- `../pth_model/any_day_prediction_model_csv_final.pkl`: 最终模型
- `../pth_model/any_day_prediction_model_csv_*.pkl`: 检查点模型
- `../logs/adp_csv/`: TensorBoard日志

## 注意事项

1. **数据格式**: 确保CSV文件包含必要的列，特别是：
   - `sdk_yidun_device_id`: 设备ID
   - `attribution_day`: 归因日期
   - `channel_ty_adgroup_id`: 广告组ID
   - 行为特征列（如 `fish_gun_fire_sum_count` 等）

2. **内存使用**: 大文件处理可能需要大量内存，建议：
   - 使用较小的批次大小
   - 减少数据加载器工作进程数
   - 分批处理数据

3. **GPU使用**: 确保有足够的GPU内存，可以通过以下方式调整：
   - 减少批次大小
   - 减少隐藏层维度
   - 使用梯度累积

4. **错误处理**: 脚本包含完整的错误处理，如果某个步骤失败，会显示详细的错误信息。

## 故障排除

### 常见问题

1. **CSV文件未找到**
   - 确保CSV文件在 `csv_data/` 目录中
   - 检查文件名是否正确

2. **内存不足**
   - 减少批次大小
   - 减少数据加载器工作进程数
   - 使用更小的数据集进行测试

3. **CUDA内存不足**
   - 减少批次大小
   - 减少模型隐藏层维度
   - 使用CPU训练（设置 `--devices []`）

4. **数据格式错误**
   - 检查CSV文件的列名
   - 确保必要列存在
   - 检查数据类型

### 调试模式

启用详细日志输出：
```bash
python run_csv_training.py --debug
```

## 性能优化

1. **数据加载优化**:
   - 使用SSD存储
   - 增加数据加载器工作进程数
   - 使用内存映射文件

2. **训练优化**:
   - 使用混合精度训练
   - 启用梯度累积
   - 使用分布式训练

3. **模型优化**:
   - 调整学习率调度
   - 使用更复杂的正则化
   - 尝试不同的损失函数 