{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(os.path.abspath(\"../src/\"))\n", "\n", "project_root = '/wkspace/qiushi/ltv-prediction/src'\n", "\n", "from datetime import datetime, timedelta\n", "from typing import List, Tuple, Any, Dict, Type\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm import tqdm\n", "from impala.dbapi import connect\n", "from collections import defaultdict\n", "\n", "from config import config"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def _load_data_from_impala(attribution_ds, ds) -> Tu<PERSON>[pd.DataFrame, pd.DataFrame]:\n", "    \"\"\" 从impala中获取数据 \"\"\"\n", "    conn = connect(\n", "        host=config['impala']['proxy_host'],\n", "        port=config['impala']['proxy_port'],\n", "        auth_mechanism=\"NOSASL\"\n", "    )\n", "    cursor = conn.cursor(user=config['impala']['user'])\n", "    \n", "    date1 = datetime.strptime(attribution_ds, \"%Y-%m-%d\")\n", "    date2 = datetime.strptime(ds, \"%Y-%m-%d\")\n", "\n", "    # 计算天数差并断言\n", "    assert abs((date2 - date1).days) > 14, \"日期间隔不超过14天\"\n", "    \n", "    start_date = datetime.strptime(attribution_ds, \"%Y-%m-%d\")\n", "    end_date = datetime.strptime(ds, \"%Y-%m-%d\") - <PERSON><PERSON><PERSON>(days=14)\n", "\n", "    # 生成日期列表\n", "    date_list = []\n", "    current = start_date\n", "    while current <= end_date:\n", "        date_list.append(current.strftime(\"%Y-%m-%d\"))\n", "        current += <PERSON><PERSON><PERSON>(days=1)\n", "\n", "    print(date_list)\n", "\n", "    campaign_df_list = []\n", "    print(\"Loading Campaign Data\")\n", "    for attribution_day_ in tqdm(date_list):\n", "        with open('../src/misc/impala_advertisement_feature_20461.sql', 'r') as f:\n", "            sql_template = f.read()\n", "            campaign_query = sql_template.format(attribution_day=attribution_day_)\n", "            cursor.execute(\"refresh koi_data.dwd_advertisement_feature_20461\")\n", "            cursor.execute(campaign_query)\n", "            campaign_results = cursor.fetchall()\n", "            column_names = [desc[0] for desc in cursor.description]\n", "            campaign_df_ = pd.DataFrame(campaign_results, columns=column_names)\n", "            campaign_df_list.append(campaign_df_)\n", "    \n", "    campaign_df = pd.concat(campaign_df_list)\n", "    \n", "    with open('../src/misc/impala_user_behavior_10010.sql', 'r') as f:\n", "        sql_template = f.read()\n", "        behavior_query = sql_template.format(start_ds=attribution_ds, end_ds=ds)\n", "\n", "    cursor.execute('refresh koi_data.dwd_user_behavior_10010')\n", "    cursor.execute(behavior_query)\n", "    behavior_results = cursor.fetchall()\n", "    column_names = [desc[0] for desc in cursor.description]\n", "    behavior_df = pd.DataFrame(behavior_results, columns=column_names)\n", "    \n", "    return campaign_df, behavior_df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['2025-04-13', '2025-04-14', '2025-04-15']\n", "Loading Campaign Data\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/3 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 3/3 [00:21<00:00,  7.02s/it]\n"]}], "source": ["campaign_df_raw, behavior_df_raw = _load_data_from_impala('2025-04-13', '2025-04-29')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>attribution_day</th>\n", "      <th>channel_ty_campaign_id</th>\n", "      <th>channel_ty_account_id</th>\n", "      <th>adtrace_organic_traffic</th>\n", "      <th>sdk_mntid</th>\n", "      <th>adtrace_attribution_mode</th>\n", "      <th>sdk_os_name</th>\n", "      <th>adtrace_pay_times</th>\n", "      <th>adtrace_click_match_count</th>\n", "      <th>...</th>\n", "      <th>d21_payment</th>\n", "      <th>d22_payment</th>\n", "      <th>d23_payment</th>\n", "      <th>d24_payment</th>\n", "      <th>d25_payment</th>\n", "      <th>d26_payment</th>\n", "      <th>d27_payment</th>\n", "      <th>d28_payment</th>\n", "      <th>d29_payment</th>\n", "      <th>d30_payment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>+C0Gp8ZCaf9FX0FUBVbEvOsohh9eWEcF</td>\n", "      <td>2025-04-13</td>\n", "      <td></td>\n", "      <td>10021</td>\n", "      <td>1</td>\n", "      <td>7213EDAC729F730DFA3522BF04E85AFF71D6903118030C...</td>\n", "      <td></td>\n", "      <td>iOS</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>/oatP/rht3FEWURAQVOBCWSud9hzgTLZ</td>\n", "      <td>2025-04-13</td>\n", "      <td>**********</td>\n", "      <td>********</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>oaid</td>\n", "      <td>android</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>000cd7xzZORAWBQRRFeGOA3LMSZio30Z</td>\n", "      <td>2025-04-13</td>\n", "      <td>*********</td>\n", "      <td>********</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>oaid</td>\n", "      <td>android</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1YbMWyCevqdAWRRQQRaDKAzjoVZb9K1l</td>\n", "      <td>2025-04-13</td>\n", "      <td>**********</td>\n", "      <td>********</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>oaid</td>\n", "      <td>android</td>\n", "      <td>None</td>\n", "      <td>4</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1jocNVQxQUZFTgAAREKUajGdOfYYuks7</td>\n", "      <td>2025-04-13</td>\n", "      <td></td>\n", "      <td>10021</td>\n", "      <td>1</td>\n", "      <td>4226A44CF7EC6B88A54EF8AB4F121CAB6DAD6E01@/dev/...</td>\n", "      <td></td>\n", "      <td>iPadOS</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 118 columns</p>\n", "</div>"], "text/plain": ["                sdk_yidun_device_id attribution_day channel_ty_campaign_id  \\\n", "0  +C0Gp8ZCaf9FX0FUBVbEvOsohh9eWEcF      2025-04-13                          \n", "1  /oatP/rht3FEWURAQVOBCWSud9hzgTLZ      2025-04-13             **********   \n", "2  000cd7xzZORAWBQRRFeGOA3LMSZio30Z      2025-04-13              *********   \n", "3  1YbMWyCevqdAWRRQQRaDKAzjoVZb9K1l      2025-04-13             **********   \n", "4  1jocNVQxQUZFTgAAREKUajGdOfYYuks7      2025-04-13                          \n", "\n", "  channel_ty_account_id adtrace_organic_traffic  \\\n", "0                 10021                       1   \n", "1              ********                       0   \n", "2              ********                       0   \n", "3              ********                       0   \n", "4                 10021                       1   \n", "\n", "                                           sdk_mntid adtrace_attribution_mode  \\\n", "0  7213EDAC729F730DFA3522BF04E85AFF71D6903118030C...                            \n", "1                                               None                     oaid   \n", "2                                               None                     oaid   \n", "3                                               None                     oaid   \n", "4  4226A44CF7EC6B88A54EF8AB4F121CAB6DAD6E01@/dev/...                            \n", "\n", "  sdk_os_name adtrace_pay_times adtrace_click_match_count  ... d21_payment  \\\n", "0         iOS              None                      None  ...    0.000000   \n", "1     android              None                         1  ...    0.000000   \n", "2     android              None                         1  ...    0.000000   \n", "3     android              None                         4  ...    0.000000   \n", "4      iPadOS              None                      None  ...    0.000000   \n", "\n", "  d22_payment d23_payment d24_payment d25_payment d26_payment d27_payment  \\\n", "0    0.000000    0.000000    0.000000    0.000000    0.000000    0.000000   \n", "1    0.000000    0.000000    0.000000    0.000000    0.000000    0.000000   \n", "2    0.000000    0.000000    0.000000    0.000000    0.000000    0.000000   \n", "3    0.000000    0.000000    0.000000    0.000000    0.000000    0.000000   \n", "4    0.000000    0.000000    0.000000    0.000000    0.000000    0.000000   \n", "\n", "  d28_payment d29_payment d30_payment  \n", "0    0.000000    0.000000    0.000000  \n", "1    0.000000    0.000000    0.000000  \n", "2    0.000000    0.000000    0.000000  \n", "3    0.000000    0.000000    0.000000  \n", "4    0.000000    0.000000    0.000000  \n", "\n", "[5 rows x 118 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["campaign_df_raw.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>day</th>\n", "      <th>game_time_in_minutes</th>\n", "      <th>final_delta</th>\n", "      <th>max_delta</th>\n", "      <th>min_delta</th>\n", "      <th>max_gunlevel</th>\n", "      <th>min_gunlevel</th>\n", "      <th>max_boss_rate</th>\n", "      <th>total_catch</th>\n", "      <th>...</th>\n", "      <th>click_120215_count</th>\n", "      <th>click_120214_count</th>\n", "      <th>click_120093_count</th>\n", "      <th>click_120092_count</th>\n", "      <th>resource_total_down_count</th>\n", "      <th>resource_down_count</th>\n", "      <th>activity_midnight</th>\n", "      <th>activity_morning</th>\n", "      <th>activity_afternoon</th>\n", "      <th>activity_night</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>%252F7t3nuwZRR1BXRBABBbE5QrJBnD%252FDSKo</td>\n", "      <td>2025-04-24</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>%252F8Zjzhxvj0JEXAERUFPVtVoYfpnC1qou</td>\n", "      <td>2025-04-24</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>%252FKirxpn8EZhBHRFBEBfV9cPXqh2S69xb</td>\n", "      <td>2025-04-24</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>%252FOvNEGJ6s0xAGBRFBEbRpR9WM6EQ78ce</td>\n", "      <td>2025-04-24</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>%252FacIsM1AVCxETQRVQFbR8AoZ9Gyui3if</td>\n", "      <td>2025-04-24</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 33 columns</p>\n", "</div>"], "text/plain": ["                        sdk_yidun_device_id         day game_time_in_minutes  \\\n", "0  %252F7t3nuwZRR1BXRBABBbE5QrJBnD%252FDSKo  2025-04-24                 None   \n", "1      %252F8Zjzhxvj0JEXAERUFPVtVoYfpnC1qou  2025-04-24                 None   \n", "2      %252FKirxpn8EZhBHRFBEBfV9cPXqh2S69xb  2025-04-24                 None   \n", "3      %252FOvNEGJ6s0xAGBRFBEbRpR9WM6EQ78ce  2025-04-24                 None   \n", "4      %252FacIsM1AVCxETQRVQFbR8AoZ9Gyui3if  2025-04-24                 None   \n", "\n", "  final_delta max_delta min_delta max_gunlevel min_gunlevel max_boss_rate  \\\n", "0        None      None      None         None         None          None   \n", "1        None      None      None         None         None          None   \n", "2        None      None      None         None         None          None   \n", "3        None      None      None         None         None          None   \n", "4        None      None      None         None         None          None   \n", "\n", "  total_catch  ... click_120215_count click_120214_count click_120093_count  \\\n", "0        None  ...                  0                  0                  0   \n", "1        None  ...                  0                  0                  0   \n", "2        None  ...                  0                  0                  0   \n", "3        None  ...                  0                  0                  0   \n", "4        None  ...                  0                  0                  0   \n", "\n", "  click_120092_count resource_total_down_count resource_down_count  \\\n", "0                  0                         0                   0   \n", "1                  0                         0                   0   \n", "2                  0                         0                   0   \n", "3                  0                         0                   0   \n", "4                  0                         0                   0   \n", "\n", "  activity_midnight activity_morning activity_afternoon activity_night  \n", "0                 0                1                  0              0  \n", "1                 0                1                  0              0  \n", "2                 0                1                  0              0  \n", "3                 0                1                  0              0  \n", "4                 0                1                  0              0  \n", "\n", "[5 rows x 33 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["behavior_df_raw.head()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["campaign_df_raw = campaign_df_raw.dropna(subset=['channel_ty_adgroup_id'])\n", "behavior_df_raw = behavior_df_raw.fillna(0)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def add_device_price(df):\n", "    import pandas as pd\n", "    device2price = pd.read_csv('../data/device2price.csv')\n", "    df_with_price = pd.merge(\n", "        df,\n", "        device2price,\n", "        left_on='sdk_device_name',\n", "        right_on='device_name', \n", "        how='left' \n", "    )\n", "    df_with_price = df_with_price.rename(columns={'price': 'device_price'})\n", "    return df_with_price"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def analyze_column(df, verbose=True):\n", "    \"\"\" 区分列名\n", "    ignore_cols: 忽略\n", "    X_cols: 输入特征\n", "    y_cols: 输出目标\n", "    key_cols: ID等信息\n", "    \"\"\"\n", "    ignore_cols, X_cols = [], []\n", "    key_cols = []\n", "    for column in df.columns:\n", "        distinct_vals = df[column].nunique()\n", "        missing_vals = df[column].isna().sum()\n", "        if column in ('sdk_yidun_device_id', 'attribution_day', 'adtrace_aid', 'proj_main_channel', \n", "                    'channel_ty_adgroup_id', 'channel_ty_account_id', 'channel_ty_campaign_id', 'channel_ty_csite_id'):\n", "            key_cols.append(column)\n", "        elif distinct_vals in (0, 1) or missing_vals == df.shape[0]:  # 全部相同 或 全部缺失\n", "            ignore_cols.append(column)\n", "        elif df[column].dtype == 'object' and (distinct_vals + missing_vals > 0.7 * df.shape[0] or distinct_vals > 0.2 * df.shape[0]):  # obj类型过多\n", "            ignore_cols.append(column) \n", "        elif column in ('ingest_time', 'event_time', 'sdk_sysfiletime', 'adtrace_last_active_time', 'adtrace_missionid', 'adtrace_attribution_time', \n", "                        'adtrace_pay_times_boundary', 'sdk_boottimeinsec', 'adtrace_yidun_last_active_time'): # ignore time\n", "            ignore_cols.append(column)\n", "        elif column in ('adtrace_yidun_validate_message', ): #  人工过滤\n", "            ignore_cols.append(column)\n", "        elif column.endswith('payment'):\n", "            X_cols.append(column)\n", "        else:\n", "            if verbose:\n", "                print(f\"Column: {column}\")\n", "                print(f\"  Data type: {df[column].dtype}\")\n", "                print(f\"  Distinct values: {distinct_vals}\")\n", "                print(f\"  Missing values: {missing_vals}\")\n", "                print(f\"  sample: \")\n", "                print(f\"{df[column].head(5)}\")\n", "                print(\"-\" * 40)  # 分隔线\n", "            X_cols.append(column)\n", "    return key_cols, X_cols, ignore_cols"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def get_behavior_data_list(df, df_with_attribution):\n", "    user2attribution = df_with_attribution.set_index('sdk_yidun_device_id')['attribution_day'].to_dict()\n", "    df['attribution_day'] = df['sdk_yidun_device_id'].map(user2attribution)\n", "    df['attribution_day'] = pd.to_datetime(df['attribution_day'])\n", "    df['day'] = pd.to_datetime(df['day'])\n", "    df_filter = df[(df['attribution_day'] <= df['day']) & (df['attribution_day'] + pd.Timedelta(days=13) >= df['day'])]\n", "    df_filter['day_diff'] = (df_filter['day'] - df_filter['attribution_day']).dt.days \n", "    # print(df_filter.columns)\n", "    value_columns = ['fish_gun_fire_sum_count', 'gun_level_up_consume_count', \n", "                        'recharge_count', 'fish_table_enter_count', 'skill_use_count',\n", "                        'bkrpt_count', 'total_recharge_amount', 'login_count',\n", "                        'shop_center_enter_count', 'achievement_reward_count',\n", "                        'have_checkin_reward', 'startup_quest_finish_game_count',\n", "                        'click_120215_count', 'click_120214_count', 'click_120093_count',\n", "                        'click_120092_count', 'resource_total_down_count',\n", "                        'resource_down_count', 'activity_midnight', 'activity_morning',\n", "                        'activity_afternoon', 'activity_night', \n", "                        'game_time_in_minutes', 'final_delta', 'max_delta', 'min_delta',\n", "                        'max_gunlevel', 'min_gunlevel', 'max_boss_rate', 'total_catch',\n", "                        'total_catch_boss',]\n", "\n", "    data_dict = defaultdict(dict)\n", "    df_filter = df_filter.sort_values('day')\n", "    for _, row in df_filter.iterrows():\n", "        key = (row['sdk_yidun_device_id'], row['attribution_day'])\n", "        day_diff = row['day_diff']\n", "        \n", "        # 存储每个 day_diff 对应的属性值\n", "        if key not in data_dict:\n", "            data_dict[key] = {'sdk_yidun_device_id': row['sdk_yidun_device_id'], 'attribution_day': row['attribution_day'].strftime('%Y-%m-%d')}\n", "            data_dict[key].update({\"behav.\" + col: 14 * [0.0] for col in value_columns })\n", "        \n", "        # 生成后缀 col_0, col_1\n", "        for col in value_columns:\n", "            col_name = f\"behav.{col}\"\n", "            data_dict[key][col_name][day_diff] = float(row[col])\n", "\n", "    df_result = pd.DataFrame(list(data_dict.values()))\n", "    return df_result\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["keys, X_cols, ignores = analyze_column(campaign_df_raw, verbose=False)\n", "campaign_df = add_device_price(campaign_df_raw[keys + X_cols])"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_48093/3347345377.py:7: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_filter['day_diff'] = (df_filter['day'] - df_filter['attribution_day']).dt.days\n"]}], "source": ["behavior_df = get_behavior_data_list(behavior_df_raw, campaign_df)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>attribution_day</th>\n", "      <th>behav.fish_gun_fire_sum_count</th>\n", "      <th>behav.gun_level_up_consume_count</th>\n", "      <th>behav.recharge_count</th>\n", "      <th>behav.fish_table_enter_count</th>\n", "      <th>behav.skill_use_count</th>\n", "      <th>behav.bkrpt_count</th>\n", "      <th>behav.total_recharge_amount</th>\n", "      <th>behav.login_count</th>\n", "      <th>...</th>\n", "      <th>behav.activity_night</th>\n", "      <th>behav.game_time_in_minutes</th>\n", "      <th>behav.final_delta</th>\n", "      <th>behav.max_delta</th>\n", "      <th>behav.min_delta</th>\n", "      <th>behav.max_gunlevel</th>\n", "      <th>behav.min_gunlevel</th>\n", "      <th>behav.max_boss_rate</th>\n", "      <th>behav.total_catch</th>\n", "      <th>behav.total_catch_boss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ApiBk9YVNaJAGlFRBQPFNec90CKfEYYM</td>\n", "      <td>2025-04-13</td>\n", "      <td>[5.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[3.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[22.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>...</td>\n", "      <td>[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[10.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[82850000000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[60750000000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[200000000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[450000000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[100000000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[135.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[11.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>gC0zPs85xaxBXBRUEUPXdjAMIQyGfg3s</td>\n", "      <td>2025-04-13</td>\n", "      <td>[12.0, 0.0, 18.0, 9.0, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[8.0, 0.0, 21.0, 4.0, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[64.0, 0.0, 79.0, 14.0, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[20.0, 0.0, 35.0, 5.0, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[12074000000000.0, 0.0, 10598900000000.0, 6100...</td>\n", "      <td>[9600000000000.0, 0.0, 5200000000000.0, 150000...</td>\n", "      <td>[5000000000.0, 0.0, 1000000000.0, 2000000000.0...</td>\n", "      <td>[5000000000.0, 0.0, 2000000000.0, 1000000000.0...</td>\n", "      <td>[2500000000.0, 0.0, 500000000.0, 1000000000.0,...</td>\n", "      <td>[3200.0, 0.0, 5200.0, 15.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[75.0, 0.0, 111.0, 15.0, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[1.0, 0.0, 4.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>gCQUO1ibqDJBSBQFFUPQCuRoRQk94euk</td>\n", "      <td>2025-04-13</td>\n", "      <td>[21.0, 17.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[14.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[3.0, 4.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[22.0, 54.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[1.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[50.0, 50.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[169264000000.0, 602886000000.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[18720000000.0, 59200000000.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[200000.0, 6000000.0, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[50000000.0, 100000000.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[100000.0, 3000000.0, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[468.0, 740.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[1034.0, 975.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[2.0, 9.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>gL8fdgm0Im1BWUAVUAfRz80Jg53Khzsn</td>\n", "      <td>2025-04-13</td>\n", "      <td>[92.0, 178.0, 134.0, 288.0, 48.0, 112.0, 70.0,...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[22.0, 36.0, 31.0, 192.0, 27.0, 31.0, 15.0, 15...</td>\n", "      <td>[229.0, 277.0, 143.0, 1265.0, 70.0, 105.0, 74....</td>\n", "      <td>[6.0, 11.0, 8.0, 3.0, 3.0, 9.0, 6.0, 3.0, 6.0,...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>...</td>\n", "      <td>[1.0, 0.0, 1.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[60.0, 70.0, 45.0, 315.0, 10.0, 30.0, 20.0, 20...</td>\n", "      <td>[32433700000000.0, 50410100000000.0, 828840000...</td>\n", "      <td>[10400000000000.0, 11300000000000.0, 400000000...</td>\n", "      <td>[200000000.0, 1400000000.0, 200000000.0, 25000...</td>\n", "      <td>[3000000000.0, 4500000000.0, 2000000000.0, 400...</td>\n", "      <td>[100000000.0, 700000000.0, 100000000.0, 500000...</td>\n", "      <td>[5200.0, 5650.0, 2100.0, 10760.0, 1200.0, 567....</td>\n", "      <td>[62.0, 81.0, 100.0, 435.0, 8.0, 25.0, 51.0, 10...</td>\n", "      <td>[8.0, 13.0, 4.0, 71.0, 2.0, 1.0, 4.0, 1.0, 0.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>gQRSBtF687tBXFEUEVaUHxBIFyUvTiPi</td>\n", "      <td>2025-04-13</td>\n", "      <td>[62.0, 131.0, 40.0, 21.0, 34.0, 84.0, 30.0, 67...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 15.0, 1.0,...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[54.0, 143.0, 48.0, 23.0, 29.0, 23.0, 7.0, 22....</td>\n", "      <td>[673.0, 1675.0, 517.0, 435.0, 381.0, 254.0, 52...</td>\n", "      <td>[4.0, 0.0, 0.0, 0.0, 2.0, 10.0, 4.0, 6.0, 2.0,...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>...</td>\n", "      <td>[1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, ...</td>\n", "      <td>[175.0, 495.0, 140.0, 110.0, 100.0, 75.0, 20.0...</td>\n", "      <td>[17611680000000.0, 538519400000000.0, 18754200...</td>\n", "      <td>[2760000000000.0, 22540000000000.0, 2220800000...</td>\n", "      <td>[60000000.0, 900000000.0, 7000000000.0, 200000...</td>\n", "      <td>[400000000.0, 3500000000.0, 4000000000.0, 4000...</td>\n", "      <td>[30000000.0, 450000000.0, 3000000000.0, 100000...</td>\n", "      <td>[9200.0, 8000.0, 5552.0, 6400.0, 5350.0, 4300....</td>\n", "      <td>[320.0, 1326.0, 222.0, 450.0, 206.0, 312.0, 61...</td>\n", "      <td>[39.0, 268.0, 57.0, 33.0, 62.0, 27.0, 3.0, 31....</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 33 columns</p>\n", "</div>"], "text/plain": ["                sdk_yidun_device_id attribution_day  \\\n", "0  ApiBk9YVNaJAGlFRBQPFNec90CKfEYYM      2025-04-13   \n", "1  gC0zPs85xaxBXBRUEUPXdjAMIQyGfg3s      2025-04-13   \n", "2  gCQUO1ibqDJBSBQFFUPQCuRoRQk94euk      2025-04-13   \n", "3  gL8fdgm0Im1BWUAVUAfRz80Jg53Khzsn      2025-04-13   \n", "4  gQRSBtF687tBXFEUEVaUHxBIFyUvTiPi      2025-04-13   \n", "\n", "                       behav.fish_gun_fire_sum_count  \\\n", "0  [5.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [12.0, 0.0, 18.0, 9.0, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "2  [21.0, 17.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "3  [92.0, 178.0, 134.0, 288.0, 48.0, 112.0, 70.0,...   \n", "4  [62.0, 131.0, 40.0, 21.0, 34.0, 84.0, 30.0, 67...   \n", "\n", "                    behav.gun_level_up_consume_count  \\\n", "0  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "2  [14.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "3  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 15.0, 1.0,...   \n", "\n", "                                behav.recharge_count  \\\n", "0  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "2  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "\n", "                        behav.fish_table_enter_count  \\\n", "0  [3.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [8.0, 0.0, 21.0, 4.0, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "2  [3.0, 4.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3  [22.0, 36.0, 31.0, 192.0, 27.0, 31.0, 15.0, 15...   \n", "4  [54.0, 143.0, 48.0, 23.0, 29.0, 23.0, 7.0, 22....   \n", "\n", "                               behav.skill_use_count  \\\n", "0  [22.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "1  [64.0, 0.0, 79.0, 14.0, 0.0, 0.0, 0.0, 0.0, 0....   \n", "2  [22.0, 54.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "3  [229.0, 277.0, 143.0, 1265.0, 70.0, 105.0, 74....   \n", "4  [673.0, 1675.0, 517.0, 435.0, 381.0, 254.0, 52...   \n", "\n", "                                   behav.bkrpt_count  \\\n", "0  [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "2  [1.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3  [6.0, 11.0, 8.0, 3.0, 3.0, 9.0, 6.0, 3.0, 6.0,...   \n", "4  [4.0, 0.0, 0.0, 0.0, 2.0, 10.0, 4.0, 6.0, 2.0,...   \n", "\n", "                         behav.total_recharge_amount  \\\n", "0  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "2  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "\n", "                                   behav.login_count  ...  \\\n", "0  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  ...   \n", "1  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  ...   \n", "2  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  ...   \n", "3  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  ...   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  ...   \n", "\n", "                                behav.activity_night  \\\n", "0  [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "2  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3  [1.0, 0.0, 1.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, ...   \n", "4  [1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, ...   \n", "\n", "                          behav.game_time_in_minutes  \\\n", "0  [10.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "1  [20.0, 0.0, 35.0, 5.0, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "2  [50.0, 50.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "3  [60.0, 70.0, 45.0, 315.0, 10.0, 30.0, 20.0, 20...   \n", "4  [175.0, 495.0, 140.0, 110.0, 100.0, 75.0, 20.0...   \n", "\n", "                                   behav.final_delta  \\\n", "0  [82850000000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [12074000000000.0, 0.0, 10598900000000.0, 6100...   \n", "2  [169264000000.0, 602886000000.0, 0.0, 0.0, 0.0...   \n", "3  [32433700000000.0, 50410100000000.0, 828840000...   \n", "4  [17611680000000.0, 538519400000000.0, 18754200...   \n", "\n", "                                     behav.max_delta  \\\n", "0  [60750000000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [9600000000000.0, 0.0, 5200000000000.0, 150000...   \n", "2  [18720000000.0, 59200000000.0, 0.0, 0.0, 0.0, ...   \n", "3  [10400000000000.0, 11300000000000.0, 400000000...   \n", "4  [2760000000000.0, 22540000000000.0, 2220800000...   \n", "\n", "                                     behav.min_delta  \\\n", "0  [200000000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0....   \n", "1  [5000000000.0, 0.0, 1000000000.0, 2000000000.0...   \n", "2  [200000.0, 6000000.0, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "3  [200000000.0, 1400000000.0, 200000000.0, 25000...   \n", "4  [60000000.0, 900000000.0, 7000000000.0, 200000...   \n", "\n", "                                  behav.max_gunlevel  \\\n", "0  [450000000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0....   \n", "1  [5000000000.0, 0.0, 2000000000.0, 1000000000.0...   \n", "2  [50000000.0, 100000000.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3  [3000000000.0, 4500000000.0, 2000000000.0, 400...   \n", "4  [400000000.0, 3500000000.0, 4000000000.0, 4000...   \n", "\n", "                                  behav.min_gunlevel  \\\n", "0  [100000000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0....   \n", "1  [2500000000.0, 0.0, 500000000.0, 1000000000.0,...   \n", "2  [100000.0, 3000000.0, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "3  [100000000.0, 700000000.0, 100000000.0, 500000...   \n", "4  [30000000.0, 450000000.0, 3000000000.0, 100000...   \n", "\n", "                                 behav.max_boss_rate  \\\n", "0  [135.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "1  [3200.0, 0.0, 5200.0, 15.0, 0.0, 0.0, 0.0, 0.0...   \n", "2  [468.0, 740.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0...   \n", "3  [5200.0, 5650.0, 2100.0, 10760.0, 1200.0, 567....   \n", "4  [9200.0, 8000.0, 5552.0, 6400.0, 5350.0, 4300....   \n", "\n", "                                   behav.total_catch  \\\n", "0  [11.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "1  [75.0, 0.0, 111.0, 15.0, 0.0, 0.0, 0.0, 0.0, 0...   \n", "2  [1034.0, 975.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3  [62.0, 81.0, 100.0, 435.0, 8.0, 25.0, 51.0, 10...   \n", "4  [320.0, 1326.0, 222.0, 450.0, 206.0, 312.0, 61...   \n", "\n", "                              behav.total_catch_boss  \n", "0  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  \n", "1  [1.0, 0.0, 4.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  \n", "2  [2.0, 9.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  \n", "3  [8.0, 13.0, 4.0, 71.0, 2.0, 1.0, 4.0, 1.0, 0.0...  \n", "4  [39.0, 268.0, 57.0, 33.0, 62.0, 27.0, 3.0, 31....  \n", "\n", "[5 rows x 33 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["behavior_df.head()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>attribution_day</th>\n", "      <th>channel_ty_campaign_id</th>\n", "      <th>channel_ty_account_id</th>\n", "      <th>channel_ty_adgroup_id</th>\n", "      <th>channel_ty_csite_id</th>\n", "      <th>proj_main_channel</th>\n", "      <th>adtrace_aid</th>\n", "      <th>adtrace_organic_traffic</th>\n", "      <th>adtrace_attribution_mode</th>\n", "      <th>...</th>\n", "      <th>d17_payment</th>\n", "      <th>d18_payment</th>\n", "      <th>d19_payment</th>\n", "      <th>d20_payment</th>\n", "      <th>d21_payment</th>\n", "      <th>d22_payment</th>\n", "      <th>d23_payment</th>\n", "      <th>d24_payment</th>\n", "      <th>device_name</th>\n", "      <th>device_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>+C0Gp8ZCaf9FX0FUBVbEvOsohh9eWEcF</td>\n", "      <td>2025-04-13</td>\n", "      <td></td>\n", "      <td>10021</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>appStore</td>\n", "      <td>21</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>/oatP/rht3FEWURAQVOBCWSud9hzgTLZ</td>\n", "      <td>2025-04-13</td>\n", "      <td>**********</td>\n", "      <td>********</td>\n", "      <td>**********</td>\n", "      <td>39</td>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>oaid</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NOH-AN01</td>\n", "      <td>1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>000cd7xzZORAWBQRRFeGOA3LMSZio30Z</td>\n", "      <td>2025-04-13</td>\n", "      <td>*********</td>\n", "      <td>********</td>\n", "      <td>***********</td>\n", "      <td></td>\n", "      <td>baidulm</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>oaid</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>DCO-AL00</td>\n", "      <td>2800.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1YbMWyCevqdAWRRQQRaDKAzjoVZb9K1l</td>\n", "      <td>2025-04-13</td>\n", "      <td>**********</td>\n", "      <td>********</td>\n", "      <td>**********</td>\n", "      <td>39</td>\n", "      <td>kuaish<PERSON>lmpro</td>\n", "      <td>78</td>\n", "      <td>0</td>\n", "      <td>oaid</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>V2417A</td>\n", "      <td>1360.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1jocNVQxQUZFTgAAREKUajGdOfYYuks7</td>\n", "      <td>2025-04-13</td>\n", "      <td></td>\n", "      <td>10021</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>appStore</td>\n", "      <td>21</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 99 columns</p>\n", "</div>"], "text/plain": ["                sdk_yidun_device_id attribution_day channel_ty_campaign_id  \\\n", "0  +C0Gp8ZCaf9FX0FUBVbEvOsohh9eWEcF      2025-04-13                          \n", "1  /oatP/rht3FEWURAQVOBCWSud9hzgTLZ      2025-04-13             **********   \n", "2  000cd7xzZORAWBQRRFeGOA3LMSZio30Z      2025-04-13              *********   \n", "3  1YbMWyCevqdAWRRQQRaDKAzjoVZb9K1l      2025-04-13             **********   \n", "4  1jocNVQxQUZFTgAAREKUajGdOfYYuks7      2025-04-13                          \n", "\n", "  channel_ty_account_id channel_ty_adgroup_id channel_ty_csite_id  \\\n", "0                 10021                                             \n", "1              ********            **********                  39   \n", "2              ********           ***********                       \n", "3              ********            **********                  39   \n", "4                 10021                                             \n", "\n", "  proj_main_channel adtrace_aid adtrace_organic_traffic  \\\n", "0          appStore          21                       1   \n", "1        kuaishoulm           2                       0   \n", "2           baidulm           2                       0   \n", "3     kuaishoulmpro          78                       0   \n", "4          appStore          21                       1   \n", "\n", "  adtrace_attribution_mode  ... d17_payment d18_payment d19_payment  \\\n", "0                           ...    0.000000    0.000000    0.000000   \n", "1                     oaid  ...    0.000000    0.000000    0.000000   \n", "2                     oaid  ...    0.000000    0.000000    0.000000   \n", "3                     oaid  ...    0.000000    0.000000    0.000000   \n", "4                           ...    0.000000    0.000000    0.000000   \n", "\n", "  d20_payment d21_payment d22_payment d23_payment d24_payment device_name  \\\n", "0    0.000000    0.000000    0.000000    0.000000    0.000000         NaN   \n", "1    0.000000    0.000000    0.000000    0.000000    0.000000    NOH-AN01   \n", "2    0.000000    0.000000    0.000000    0.000000    0.000000    DCO-AL00   \n", "3    0.000000    0.000000    0.000000    0.000000    0.000000      V2417A   \n", "4    0.000000    0.000000    0.000000    0.000000    0.000000         NaN   \n", "\n", "  device_price  \n", "0          NaN  \n", "1       1000.0  \n", "2       2800.0  \n", "3       1360.0  \n", "4          NaN  \n", "\n", "[5 rows x 99 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["campaign_df = campaign_df.dropna(subset=['channel_ty_adgroup_id'])\n", "campaign_df.head()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["campaign_df = campaign_df[campaign_df['channel_ty_adgroup_id'] != \"\"]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>attribution_day</th>\n", "      <th>channel_ty_campaign_id</th>\n", "      <th>channel_ty_account_id</th>\n", "      <th>channel_ty_adgroup_id</th>\n", "      <th>channel_ty_csite_id</th>\n", "      <th>proj_main_channel</th>\n", "      <th>adtrace_aid</th>\n", "      <th>adtrace_organic_traffic</th>\n", "      <th>adtrace_attribution_mode</th>\n", "      <th>...</th>\n", "      <th>d17_payment</th>\n", "      <th>d18_payment</th>\n", "      <th>d19_payment</th>\n", "      <th>d20_payment</th>\n", "      <th>d21_payment</th>\n", "      <th>d22_payment</th>\n", "      <th>d23_payment</th>\n", "      <th>d24_payment</th>\n", "      <th>device_name</th>\n", "      <th>device_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>/oatP/rht3FEWURAQVOBCWSud9hzgTLZ</td>\n", "      <td>2025-04-13</td>\n", "      <td>**********</td>\n", "      <td>********</td>\n", "      <td>**********</td>\n", "      <td>39</td>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>oaid</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NOH-AN01</td>\n", "      <td>1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>000cd7xzZORAWBQRRFeGOA3LMSZio30Z</td>\n", "      <td>2025-04-13</td>\n", "      <td>*********</td>\n", "      <td>********</td>\n", "      <td>***********</td>\n", "      <td></td>\n", "      <td>baidulm</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>oaid</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>DCO-AL00</td>\n", "      <td>2800.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1YbMWyCevqdAWRRQQRaDKAzjoVZb9K1l</td>\n", "      <td>2025-04-13</td>\n", "      <td>**********</td>\n", "      <td>********</td>\n", "      <td>**********</td>\n", "      <td>39</td>\n", "      <td>kuaish<PERSON>lmpro</td>\n", "      <td>78</td>\n", "      <td>0</td>\n", "      <td>oaid</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>V2417A</td>\n", "      <td>1360.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1n9sgzLLcFRBDABBEUeViugFY8VMEqBY</td>\n", "      <td>2025-04-13</td>\n", "      <td>7491241444088070154</td>\n", "      <td>****************</td>\n", "      <td>7491241856489177129</td>\n", "      <td>33013</td>\n", "      <td>jinritoutiaolmpro</td>\n", "      <td>82</td>\n", "      <td>0</td>\n", "      <td>ipua</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>LGE-AN20</td>\n", "      <td>1800.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>4e22FXYvECRADUAAFFaSPFzc0rZ+fDA6</td>\n", "      <td>2025-04-13</td>\n", "      <td>7488614321075453962</td>\n", "      <td>****************</td>\n", "      <td>7488614504336719898</td>\n", "      <td>33013</td>\n", "      <td>ji<PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>ipua</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>SM-F936U</td>\n", "      <td>3200.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 99 columns</p>\n", "</div>"], "text/plain": ["                sdk_yidun_device_id attribution_day channel_ty_campaign_id  \\\n", "1  /oatP/rht3FEWURAQVOBCWSud9hzgTLZ      2025-04-13             **********   \n", "2  000cd7xzZORAWBQRRFeGOA3LMSZio30Z      2025-04-13              *********   \n", "3  1YbMWyCevqdAWRRQQRaDKAzjoVZb9K1l      2025-04-13             **********   \n", "5  1n9sgzLLcFRBDABBEUeViugFY8VMEqBY      2025-04-13    7491241444088070154   \n", "7  4e22FXYvECRADUAAFFaSPFzc0rZ+fDA6      2025-04-13    7488614321075453962   \n", "\n", "  channel_ty_account_id channel_ty_adgroup_id channel_ty_csite_id  \\\n", "1              ********            **********                  39   \n", "2              ********           ***********                       \n", "3              ********            **********                  39   \n", "5      ****************   7491241856489177129               33013   \n", "7      ****************   7488614504336719898               33013   \n", "\n", "   proj_main_channel adtrace_aid adtrace_organic_traffic  \\\n", "1         kuaishoulm           2                       0   \n", "2            baidulm           2                       0   \n", "3      kuaishoulmpro          78                       0   \n", "5  jinritoutiaolmpro          82                       0   \n", "7     jinritoutiaolm           2                       0   \n", "\n", "  adtrace_attribution_mode  ... d17_payment d18_payment d19_payment  \\\n", "1                     oaid  ...    0.000000    0.000000    0.000000   \n", "2                     oaid  ...    0.000000    0.000000    0.000000   \n", "3                     oaid  ...    0.000000    0.000000    0.000000   \n", "5                     ipua  ...    0.000000    0.000000    0.000000   \n", "7                     ipua  ...    0.000000    0.000000    0.000000   \n", "\n", "  d20_payment d21_payment d22_payment d23_payment d24_payment device_name  \\\n", "1    0.000000    0.000000    0.000000    0.000000    0.000000    NOH-AN01   \n", "2    0.000000    0.000000    0.000000    0.000000    0.000000    DCO-AL00   \n", "3    0.000000    0.000000    0.000000    0.000000    0.000000      V2417A   \n", "5    0.000000    0.000000    0.000000    0.000000    0.000000    LGE-AN20   \n", "7    0.000000    0.000000    0.000000    0.000000    0.000000    SM-F936U   \n", "\n", "  device_price  \n", "1       1000.0  \n", "2       2800.0  \n", "3       1360.0  \n", "5       1800.0  \n", "7       3200.0  \n", "\n", "[5 rows x 99 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["campaign_df.head()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["merged_df_raw = campaign_df.merge(behavior_df, on=['sdk_yidun_device_id', 'attribution_day'], how='inner')"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["drop_cols = ['sdk_yidun_device_id', 'attribution_day', 'attribution_day_a', 'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']\n", "drop_cols += [f'd{i}_payment' for i in range(1, 31)] + [f'd{i}_retention' for i in range(1, 31)]"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["merged_df_raw = merged_df_raw.drop(columns=drop_cols, errors='ignore')"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# cat_columns = [col for col in merged_df_raw.select_dtypes(include=['object']).columns.tolist() \n", "#             if col not in behave_columns + ['device_name', 'sdk_device_name', 'sdk_systemversion', 'sdk_devicename',]]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# merged_df = pd.get_dummies(merged_df_raw, columns=cat_columns)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# merged_df.head()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# len(merged_df.columns)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# list(merged_df.columns)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# all_behave = []\n", "# all_user = []\n", "\n", "# for i, row in merged_df.iterrows():\n", "#     behave_list = []\n", "#     for col in behave_columns:\n", "#         behave_list.append(\n", "#             [float(item) for item in row[col]]\n", "#         )\n", "#     all_behave.append(np.array(behave_list).T)\n", "    \n", "#     user_list = []\n", "#     for col in user_columns:\n", "#         user_list.append(row[col])\n", "#     all_user.append(user_list)\n", "    \n", "#     if i > 1000:\n", "#         break"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# all_behave"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["simple_behavior_df = behavior_df.drop(columns=['sdk_yidun_device_id', 'attribution_day'])"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/31 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": [" 29%|██▉       | 9/31 [00:01<00:03,  7.12it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Same Value: behav.login_count\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 31/31 [00:05<00:00,  5.26it/s]\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "normalized_df = pd.DataFrame()\n", "payment_max = -1\n", "payment_min = -1\n", "\n", "for col in tqdm(simple_behavior_df.columns):\n", "    col_data = simple_behavior_df[col].dropna()\n", "    \n", "    # 先 flatten 得到所有值\n", "    all_values = [x for lst in col_data if isinstance(lst, list) for x in lst]\n", "\n", "    # 如果为空，跳过该列\n", "    if not all_values:\n", "        normalized_df[col] = simple_behavior_df[col]\n", "        continue\n", "\n", "    col_min = min(all_values)\n", "    col_max = max(all_values)\n", "    \n", "    if col == 'behav.total_recharge_amount':\n", "        payment_max = col_max\n", "        payment_min = col_min\n", "    \n", "    if col_min == col_max:\n", "        print(f\"Same Value: {col}\")\n", "        continue\n", "    \n", "    range_val = col_max - col_min\n", "\n", "    def normalize_list(lst):\n", "        if not isinstance(lst, list):\n", "            return lst\n", "        return [(x - col_min) / range_val for x in lst]\n", "\n", "    normalized_df[col] = simple_behavior_df[col].apply(normalize_list)\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["normalized_df = normalized_df.sample(frac=1).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0 7220.0\n"]}], "source": ["print(payment_min, payment_max)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>behav.fish_gun_fire_sum_count</th>\n", "      <th>behav.gun_level_up_consume_count</th>\n", "      <th>behav.recharge_count</th>\n", "      <th>behav.fish_table_enter_count</th>\n", "      <th>behav.skill_use_count</th>\n", "      <th>behav.bkrpt_count</th>\n", "      <th>behav.total_recharge_amount</th>\n", "      <th>behav.shop_center_enter_count</th>\n", "      <th>behav.achievement_reward_count</th>\n", "      <th>behav.have_checkin_reward</th>\n", "      <th>...</th>\n", "      <th>behav.activity_night</th>\n", "      <th>behav.game_time_in_minutes</th>\n", "      <th>behav.final_delta</th>\n", "      <th>behav.max_delta</th>\n", "      <th>behav.min_delta</th>\n", "      <th>behav.max_gunlevel</th>\n", "      <th>behav.min_gunlevel</th>\n", "      <th>behav.max_boss_rate</th>\n", "      <th>behav.total_catch</th>\n", "      <th>behav.total_catch_boss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[0.00022040996253030638, 0.0, 0.00022040996253...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0002708070048745261, 0.0, 9.026900162484202...</td>\n", "      <td>[0.0, 0.0, 0.00013555645926528398, 0.0, 0.0, 0...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>...</td>\n", "      <td>[1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.010452961672473868, 0.0, 0.0278745644599303...</td>\n", "      <td>[6.641709551737585e-11, 0.0, 2.406747216302645...</td>\n", "      <td>[5.151515151515151e-10, 0.0, 2.803030303030303...</td>\n", "      <td>[1.4285714285714286e-09, 0.0, 1.42857142857142...</td>\n", "      <td>[4.545454545454546e-08, 0.0, 4.545454545454546...</td>\n", "      <td>[4.545454545454546e-08, 0.0, 4.545454545454546...</td>\n", "      <td>[0.009, 0.0, 0.0048970588235294115, 0.0, 0.0, ...</td>\n", "      <td>[0.003264390094616307, 0.0, 0.0156588712351125...</td>\n", "      <td>[0.0009861932938856016, 0.0, 0.001972386587771...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>[0.0037469693630152083, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.11827956989247312, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.00018053800324968405, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[0.0032533550223668157, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.0013458950201884253, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0013440860215053765, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>...</td>\n", "      <td>[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.020905923344947737, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[1.2094569201336337e-08, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[2.7272727272727272e-08, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[1.4285714285714286e-09, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[9.090909090909091e-06, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.006617647058823529, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.010366988855168192, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.004930966469428008, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>[0.0, 0.007493938726030417, 0.0, 0.0, 0.009698...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0003610760064993681, 0.0, 0.0, 0.00018...</td>\n", "      <td>[0.0, 0.0012200081333875558, 0.0, 0.0, 0.00366...</td>\n", "      <td>[0.0, 0.008075370121130552, 0.0, 0.0, 0.009421...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.008064516129032258, 0.0, 0.0, 0.010752...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.011464968152866241...</td>\n", "      <td>[0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, ...</td>\n", "      <td>...</td>\n", "      <td>[0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, ...</td>\n", "      <td>[0.0, 0.006968641114982578, 0.0, 0.0, 0.006968...</td>\n", "      <td>[0.0, 2.5369504957122143e-08, 0.0, 0.0, 2.1307...</td>\n", "      <td>[0.0, 6.313131313131313e-07, 0.0, 0.0, 2.62626...</td>\n", "      <td>[0.0, 4.2857142857142856e-05, 0.0, 0.0, 4.2857...</td>\n", "      <td>[0.0, 0.0006818181818181819, 0.0, 0.0, 4.54545...</td>\n", "      <td>[0.0, 0.0006818181818181819, 0.0, 0.0, 1.36363...</td>\n", "      <td>[0.0, 0.0007352941176470588, 0.0, 0.0, 0.00458...</td>\n", "      <td>[0.0, 3.8254571421284845e-05, 0.0, 0.0, 0.0014...</td>\n", "      <td>[0.0, 0.0009861932938856016, 0.0, 0.0, 0.00098...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>[0.003967379325545515, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.13978494623655913, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0002708070048745261, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.014368984682120103, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.0013458950201884253, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.005376344086021506, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.08662420382165605, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.03484320557491289, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[3.2949993576421215e-08, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[8.484848484848484e-08, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[1.4285714285714286e-09, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[1.8181818181818182e-05, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.007411764705882353, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.00365968733263625, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[0.013806706114398421, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>[0.00022040996253030638, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[9.026900162484202e-05, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.003484320557491289, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[1.0416262352765493e-11, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[7.575757575757576e-11, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[1.4285714285714286e-09, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.0013235294117647058, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.00048455790466960804, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 30 columns</p>\n", "</div>"], "text/plain": ["                       behav.fish_gun_fire_sum_count  \\\n", "0  [0.00022040996253030638, 0.0, 0.00022040996253...   \n", "1  [0.0037469693630152083, 0.0, 0.0, 0.0, 0.0, 0....   \n", "2  [0.0, 0.007493938726030417, 0.0, 0.0, 0.009698...   \n", "3  [0.003967379325545515, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "4  [0.00022040996253030638, 0.0, 0.0, 0.0, 0.0, 0...   \n", "\n", "                    behav.gun_level_up_consume_count  \\\n", "0  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [0.11827956989247312, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "2  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3  [0.13978494623655913, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "\n", "                                behav.recharge_count  \\\n", "0  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "2  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "\n", "                        behav.fish_table_enter_count  \\\n", "0  [0.0002708070048745261, 0.0, 9.026900162484202...   \n", "1  [0.00018053800324968405, 0.0, 0.0, 0.0, 0.0, 0...   \n", "2  [0.0, 0.0003610760064993681, 0.0, 0.0, 0.00018...   \n", "3  [0.0002708070048745261, 0.0, 0.0, 0.0, 0.0, 0....   \n", "4  [9.026900162484202e-05, 0.0, 0.0, 0.0, 0.0, 0....   \n", "\n", "                               behav.skill_use_count  \\\n", "0  [0.0, 0.0, 0.00013555645926528398, 0.0, 0.0, 0...   \n", "1  [0.0032533550223668157, 0.0, 0.0, 0.0, 0.0, 0....   \n", "2  [0.0, 0.0012200081333875558, 0.0, 0.0, 0.00366...   \n", "3  [0.014368984682120103, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "\n", "                                   behav.bkrpt_count  \\\n", "0  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [0.0013458950201884253, 0.0, 0.0, 0.0, 0.0, 0....   \n", "2  [0.0, 0.008075370121130552, 0.0, 0.0, 0.009421...   \n", "3  [0.0013458950201884253, 0.0, 0.0, 0.0, 0.0, 0....   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "\n", "                         behav.total_recharge_amount  \\\n", "0  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "2  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "\n", "                       behav.shop_center_enter_count  \\\n", "0  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [0.0013440860215053765, 0.0, 0.0, 0.0, 0.0, 0....   \n", "2  [0.0, 0.008064516129032258, 0.0, 0.0, 0.010752...   \n", "3  [0.005376344086021506, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "\n", "                      behav.achievement_reward_count  \\\n", "0  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "2  [0.0, 0.0, 0.0, 0.0, 0.0, 0.011464968152866241...   \n", "3  [0.08662420382165605, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "\n", "                           behav.have_checkin_reward  ...  \\\n", "0  [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  ...   \n", "1  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  ...   \n", "2  [0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, ...  ...   \n", "3  [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  ...   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  ...   \n", "\n", "                                behav.activity_night  \\\n", "0  [1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "1  [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "2  [0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, ...   \n", "3  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "\n", "                          behav.game_time_in_minutes  \\\n", "0  [0.010452961672473868, 0.0, 0.0278745644599303...   \n", "1  [0.020905923344947737, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "2  [0.0, 0.006968641114982578, 0.0, 0.0, 0.006968...   \n", "3  [0.03484320557491289, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "4  [0.003484320557491289, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "\n", "                                   behav.final_delta  \\\n", "0  [6.641709551737585e-11, 0.0, 2.406747216302645...   \n", "1  [1.2094569201336337e-08, 0.0, 0.0, 0.0, 0.0, 0...   \n", "2  [0.0, 2.5369504957122143e-08, 0.0, 0.0, 2.1307...   \n", "3  [3.2949993576421215e-08, 0.0, 0.0, 0.0, 0.0, 0...   \n", "4  [1.0416262352765493e-11, 0.0, 0.0, 0.0, 0.0, 0...   \n", "\n", "                                     behav.max_delta  \\\n", "0  [5.151515151515151e-10, 0.0, 2.803030303030303...   \n", "1  [2.7272727272727272e-08, 0.0, 0.0, 0.0, 0.0, 0...   \n", "2  [0.0, 6.313131313131313e-07, 0.0, 0.0, 2.62626...   \n", "3  [8.484848484848484e-08, 0.0, 0.0, 0.0, 0.0, 0....   \n", "4  [7.575757575757576e-11, 0.0, 0.0, 0.0, 0.0, 0....   \n", "\n", "                                     behav.min_delta  \\\n", "0  [1.4285714285714286e-09, 0.0, 1.42857142857142...   \n", "1  [1.4285714285714286e-09, 0.0, 0.0, 0.0, 0.0, 0...   \n", "2  [0.0, 4.2857142857142856e-05, 0.0, 0.0, 4.2857...   \n", "3  [1.4285714285714286e-09, 0.0, 0.0, 0.0, 0.0, 0...   \n", "4  [1.4285714285714286e-09, 0.0, 0.0, 0.0, 0.0, 0...   \n", "\n", "                                  behav.max_gunlevel  \\\n", "0  [4.545454545454546e-08, 0.0, 4.545454545454546...   \n", "1  [9.090909090909091e-06, 0.0, 0.0, 0.0, 0.0, 0....   \n", "2  [0.0, 0.0006818181818181819, 0.0, 0.0, 4.54545...   \n", "3  [1.8181818181818182e-05, 0.0, 0.0, 0.0, 0.0, 0...   \n", "4  [4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....   \n", "\n", "                                  behav.min_gunlevel  \\\n", "0  [4.545454545454546e-08, 0.0, 4.545454545454546...   \n", "1  [4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....   \n", "2  [0.0, 0.0006818181818181819, 0.0, 0.0, 1.36363...   \n", "3  [4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....   \n", "4  [4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....   \n", "\n", "                                 behav.max_boss_rate  \\\n", "0  [0.009, 0.0, 0.0048970588235294115, 0.0, 0.0, ...   \n", "1  [0.006617647058823529, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "2  [0.0, 0.0007352941176470588, 0.0, 0.0, 0.00458...   \n", "3  [0.007411764705882353, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "4  [0.0013235294117647058, 0.0, 0.0, 0.0, 0.0, 0....   \n", "\n", "                                   behav.total_catch  \\\n", "0  [0.003264390094616307, 0.0, 0.0156588712351125...   \n", "1  [0.010366988855168192, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "2  [0.0, 3.8254571421284845e-05, 0.0, 0.0, 0.0014...   \n", "3  [0.00365968733263625, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "4  [0.00048455790466960804, 0.0, 0.0, 0.0, 0.0, 0...   \n", "\n", "                              behav.total_catch_boss  \n", "0  [0.0009861932938856016, 0.0, 0.001972386587771...  \n", "1  [0.004930966469428008, 0.0, 0.0, 0.0, 0.0, 0.0...  \n", "2  [0.0, 0.0009861932938856016, 0.0, 0.0, 0.00098...  \n", "3  [0.013806706114398421, 0.0, 0.0, 0.0, 0.0, 0.0...  \n", "4  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  \n", "\n", "[5 rows x 30 columns]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["normalized_df.head()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["22791 5698\n"]}], "source": ["training_len = int(len(normalized_df) * 0.8)\n", "# training_len = 10000\n", "val_len = len(normalized_df) - training_len\n", "print(training_len, val_len)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["training_df = normalized_df.iloc[:training_len]\n", "validation_df = normalized_df.iloc[training_len:]"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["observed_days = 3\n", "predicted_days = 7"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["5698it [00:51, 110.79it/s]\n"]}], "source": ["# 优化后的代码\n", "import numpy as np\n", "from tqdm import tqdm\n", "\n", "real_payment = []\n", "predicted_payment = []\n", "linear_payment = []\n", "\n", "# 预处理 - 将训练数据转换为numpy数组以加速计算\n", "observed_cols = training_df.columns.tolist()\n", "train_data = np.array([\n", "    np.concatenate([training_df[col].iloc[i][:observed_days] for col in observed_cols])\n", "    for i in range(len(training_df))\n", "])\n", "\n", "# for _, row in tqdm(validation_df.iloc[:500].iterrows()):\n", "for _, row in tqdm(validation_df.iterrows()):\n", "    # 将当前行转换为numpy数组\n", "    row_data = np.concatenate([row[col][:observed_days] for col in observed_cols])\n", "    \n", "    # 向量化计算曼哈顿距离 - 比单独应用函数快得多\n", "    # distances = np.sum(np.abs(train_data - row_data), axis=1)\n", "    distances = np.linalg.norm(train_data - row_data, axis=1)  # 欧几里得距离\n", "    \n", "    # 获取最近的5个样本的索引\n", "    top_k_indices = np.argsort(distances)[:20]\n", "    \n", "    # 获取相应的行\n", "    top_k_rows = training_df.iloc[top_k_indices]\n", "    \n", "    real_payment.append(np.sum(row['behav.total_recharge_amount'][observed_days:predicted_days]) * payment_max)\n", "    \n", "    # 计算预测值\n", "    mean_payment = np.mean([np.sum(p_list[observed_days:predicted_days]) for p_list in top_k_rows['behav.total_recharge_amount']]) * payment_max\n", "    predicted_payment.append(mean_payment)\n", "    linear_payment.append(np.sum(row[col][:observed_days]) * 2)\n", "    # break"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean Absolute Error (MAE): 29.4061\n", "Mean Squared Error (MSE): 37684.7885\n", "Root Mean Squared Error (RMSE): 194.1257\n", "R² Score: -0.0408\n"]}], "source": ["from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score\n", "\n", "real_payment_array = np.array(real_payment)\n", "predicted_payment_array = np.array(predicted_payment)\n", "\n", "# 计算MAE（平均绝对误差）\n", "mae = mean_absolute_error(real_payment_array, predicted_payment_array)\n", "\n", "# 计算MSE（均方误差）\n", "mse = mean_squared_error(real_payment_array, predicted_payment_array)\n", "\n", "# 计算RMSE（均方根误差）\n", "rmse = np.sqrt(mse)\n", "\n", "# 计算R²（决定系数）\n", "r2 = r2_score(real_payment_array, predicted_payment_array)\n", "\n", "# 打印评估指标\n", "print(f\"Mean Absolute Error (MAE): {mae:.4f}\")\n", "print(f\"Mean Squared Error (MSE): {mse:.4f}\")\n", "print(f\"Root Mean Squared Error (RMSE): {rmse:.4f}\")\n", "print(f\"R² Score: {r2:.4f}\")"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean Absolute Error (MAE): 17.9935\n", "Mean Squared Error (MSE): 36521.9347\n", "Root Mean Squared Error (RMSE): 191.1071\n", "R² Score: -0.0087\n"]}], "source": ["from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score\n", "\n", "real_payment_array = np.array(real_payment)\n", "predicted_payment_array = np.array(linear_payment)\n", "\n", "# 计算MAE（平均绝对误差）\n", "mae = mean_absolute_error(real_payment_array, predicted_payment_array)\n", "\n", "# 计算MSE（均方误差）\n", "mse = mean_squared_error(real_payment_array, predicted_payment_array)\n", "\n", "# 计算RMSE（均方根误差）\n", "rmse = np.sqrt(mse)\n", "\n", "# 计算R²（决定系数）\n", "r2 = r2_score(real_payment_array, predicted_payment_array)\n", "\n", "# 打印评估指标\n", "print(f\"Mean Absolute Error (MAE): {mae:.4f}\")\n", "print(f\"Mean Squared Error (MSE): {mse:.4f}\")\n", "print(f\"Root Mean Squared Error (RMSE): {rmse:.4f}\")\n", "print(f\"R² Score: {r2:.4f}\")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["# def find_best_K():\n", "#     r2_list = []\n", "#     observed_cols = training_df.columns.tolist()\n", "#     train_data = np.array([\n", "#         np.concatenate([training_df[col].iloc[i][:observed_days] for col in observed_cols])\n", "#         for i in range(len(training_df))\n", "#     ])\n", "    \n", "#     for K in tqdm(range(1, 200)):\n", "#         real_payment = []\n", "#         predicted_payment = []\n", "\n", "#         for _, row in tqdm(validation_df.iloc[:500].iterrows()):\n", "#             # 将当前行转换为numpy数组\n", "#             row_data = np.concatenate([row[col][:observed_days] for col in observed_cols])\n", "            \n", "#             # 向量化计算曼哈顿距离 - 比单独应用函数快得多\n", "#             # distances = np.sum(np.abs(train_data - row_data), axis=1)\n", "#             distances = np.linalg.norm(train_data - row_data, axis=1)  # 欧几里得距离\n", "            \n", "#             # 获取最近的5个样本的索引\n", "#             top_k_indices = np.argsort(distances)[:K]\n", "            \n", "#             # 获取相应的行\n", "#             top_k_rows = training_df.iloc[top_k_indices]\n", "            \n", "#             real_payment.append(np.sum(row['behav.total_recharge_amount'][observed_days:predicted_days]) * payment_max)\n", "            \n", "#             # 计算预测值\n", "#             mean_payment = np.mean([np.sum(p_list[observed_days:predicted_days]) for p_list in top_k_rows['behav.total_recharge_amount']]) * payment_max\n", "#             predicted_payment.append(mean_payment)\n", "#             # break\n", "            \n", "#         real_payment_array = np.array(real_payment)\n", "#         predicted_payment_array = np.array(predicted_payment)\n", "        \n", "#         r2 = r2_score(real_payment_array, predicted_payment_array)\n", "            \n", "#         r2_list.append(r2)\n", "#     print(r2_list)\n", "#     return r2_list"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0     184273.79\n", "1     147856.00\n", "2     126356.00\n", "3     125744.00\n", "4     123430.00\n", "5     140226.15\n", "6     117402.00\n", "7     150256.00\n", "8     103148.00\n", "9     115800.00\n", "10    128454.63\n", "11    134027.01\n", "12    139000.00\n", "13    121859.75\n", "dtype: float64\n"]}], "source": ["result = pd.DataFrame(behavior_df['behav.total_recharge_amount'].tolist()).sum(axis=0)\n", "print(result)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["all_payment_list = result.tolist()"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["any_day_matrix = np.full((14, 14), -1, dtype=float)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["for start_idx in range(0, len(all_payment_list)):\n", "    for end_idx in range(start_idx, len(all_payment_list)):\n", "        rate = np.sum(all_payment_list[0:end_idx+1])/np.sum(all_payment_list[0:start_idx+1])\n", "        any_day_matrix[start_idx, end_idx] = rate"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 1.        ,  1.8023713 ,  2.48806838,  3.17044432,  3.84026285,\n", "         4.60122918,  5.23833552,  6.05373092,  6.61348497,  7.24189772,\n", "         7.93898345,  8.66630887,  9.42062124, 10.08191849],\n", "       [-1.        ,  1.        ,  1.38044163,  1.75904061,  2.13067244,\n", "         2.55287531,  2.9063576 ,  3.35875906,  3.66932439,  4.01798327,\n", "         4.40474361,  4.80828167,  5.22679275,  5.59369676],\n", "       [-1.        , -1.        ,  1.        ,  1.27425932,  1.54347159,\n", "         1.84931782,  2.10538246,  2.43310472,  2.65808007,  2.9106506 ,\n", "         3.19082205,  3.48314738,  3.78631927,  4.05210667],\n", "       [-1.        , -1.        , -1.        ,  1.        ,  1.21126961,\n", "         1.45128844,  1.65224019,  1.90942667,  2.08598048,  2.28419016,\n", "         2.50406021,  2.73346825,  2.97138833,  3.17997021],\n", "       [-1.        , -1.        , -1.        , -1.        ,  1.        ,\n", "         1.19815475,  1.36405651,  1.57638452,  1.72214383,  1.88578178,\n", "         2.0673021 ,  2.25669679,  2.45311886,  2.6253199 ],\n", "       [-1.        , -1.        , -1.        , -1.        , -1.        ,\n", "         1.        ,  1.13846438,  1.31567689,  1.43733005,  1.57390502,\n", "         1.72540492,  1.8834769 ,  2.04741404,  2.19113591],\n", "       [-1.        , -1.        , -1.        , -1.        , -1.        ,\n", "        -1.        ,  1.        ,  1.15565925,  1.26251649,  1.38248069,\n", "         1.51555459,  1.65440126,  1.79839974,  1.9246416 ],\n", "       [-1.        , -1.        , -1.        , -1.        , -1.        ,\n", "        -1.        , -1.        ,  1.        ,  1.09246431,  1.19627017,\n", "         1.31141994,  1.43156493,  1.55616782,  1.66540578],\n", "       [-1.        , -1.        , -1.        , -1.        , -1.        ,\n", "        -1.        , -1.        , -1.        ,  1.        ,  1.09501991,\n", "         1.2004236 ,  1.31039972,  1.42445644,  1.52444869],\n", "       [-1.        , -1.        , -1.        , -1.        , -1.        ,\n", "        -1.        , -1.        , -1.        , -1.        ,  1.        ,\n", "         1.09625733,  1.19669031,  1.30084981,  1.39216527],\n", "       [-1.        , -1.        , -1.        , -1.        , -1.        ,\n", "        -1.        , -1.        , -1.        , -1.        , -1.        ,\n", "         1.        ,  1.09161443,  1.18662815,  1.26992562],\n", "       [-1.        , -1.        , -1.        , -1.        , -1.        ,\n", "        -1.        , -1.        , -1.        , -1.        , -1.        ,\n", "        -1.        ,  1.        ,  1.08703964,  1.16334632],\n", "       [-1.        , -1.        , -1.        , -1.        , -1.        ,\n", "        -1.        , -1.        , -1.        , -1.        , -1.        ,\n", "        -1.        , -1.        ,  1.        ,  1.07019678],\n", "       [-1.        , -1.        , -1.        , -1.        , -1.        ,\n", "        -1.        , -1.        , -1.        , -1.        , -1.        ,\n", "        -1.        , -1.        , -1.        ,  1.        ]])"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["any_day_matrix"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.1053824590725045\n"]}], "source": ["print(any_day_matrix[2,6])"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open('../data/any_day_matrix.json', 'w') as f:\n", "    json.dump(any_day_matrix.tolist(), f)\n"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["2592499"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["len(normalized_df) * 91"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}