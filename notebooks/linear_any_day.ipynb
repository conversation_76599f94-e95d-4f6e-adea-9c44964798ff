{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(os.path.abspath(\"..\"))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from src.config import config\n", "from src.pipelines.adp_inference import PipelineADPInference\n", "from model.torch_model_any_day_prediction import AnyDayModel"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import time\n", "import os\n", "import traceback\n", "import torch\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "from typing import List, Tuple, Any, Dict, Type\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from impala.dbapi import connect as impala_connect\n", "def load_payment_stay_from_impala(attribution_ds, ds):\n", "    hql = f\"\"\"\n", "    select \n", "        sdk_yidun_device_id, \n", "        d1_payment, d2_payment, d3_payment, d4_payment, d5_payment, d6_payment, d7_payment, \n", "        d8_payment, d9_payment, d10_payment, d11_payment, d12_payment, d13_payment, d14_payment, \n", "        attribution_day, channel_ty_adgroup_id,\n", "        proj_main_channel, sdk_device_name, adtrace_reattributed\n", "    from koi_data.dwd_advertisement_feature_20461 \n", "    where attribution_day between '{attribution_ds}' and '{ds}'\n", "    and adtrace_aid in ('2', '21', '87', '82', '78')\n", "    \"\"\"\n", "    conn = impala_connect(\n", "            host=config['impala']['proxy_host'],\n", "            port=config['impala']['proxy_port'],\n", "            auth_mechanism=\"NOSASL\"\n", "        )\n", "    cursor = conn.cursor(user=config['impala']['user'])\n", "    cursor.execute(hql)\n", "\n", "    columns = [col[0] for col in cursor.description]\n", "    data = cursor.fetchall()\n", "    df = pd.DataFrame(data, columns=columns)\n", "    \n", "    conn.close()\n", "    return df"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["user_ltv_df_real = load_payment_stay_from_impala('2025-04-13', '2025-05-13')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>d1_payment</th>\n", "      <th>d2_payment</th>\n", "      <th>d3_payment</th>\n", "      <th>d4_payment</th>\n", "      <th>d5_payment</th>\n", "      <th>d6_payment</th>\n", "      <th>d7_payment</th>\n", "      <th>d8_payment</th>\n", "      <th>d9_payment</th>\n", "      <th>d10_payment</th>\n", "      <th>d11_payment</th>\n", "      <th>d12_payment</th>\n", "      <th>d13_payment</th>\n", "      <th>d14_payment</th>\n", "      <th>attribution_day</th>\n", "      <th>channel_ty_adgroup_id</th>\n", "      <th>proj_main_channel</th>\n", "      <th>sdk_device_name</th>\n", "      <th>adtrace_reattributed</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>/YcMuQj4U7hEWBVQRAfXGD+emAo8KT+/</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2025-04-22</td>\n", "      <td>8924279655</td>\n", "      <td>kuaish<PERSON>lmpro</td>\n", "      <td>V2285A</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2BCJsGbZpnJEDRERAFfCeWL3CXunsJZL</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2025-04-22</td>\n", "      <td>20133403399</td>\n", "      <td>qqqm</td>\n", "      <td>KOZ-AL40</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3QvkPzIgn9JFCxAEFUfWXbRPpeC3VYuu</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>50.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2025-04-22</td>\n", "      <td></td>\n", "      <td>appStore</td>\n", "      <td>iPhone</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3YP1XSMCU6VEWAEFRROHTkmojlmOpubY</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2025-04-22</td>\n", "      <td>19933785198</td>\n", "      <td>qqqm</td>\n", "      <td>V2410A</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3Yhi7rSuK1dEXRVBQROHOWOw2bD1bOLe</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2025-04-22</td>\n", "      <td>9151232722</td>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>V2353A</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                sdk_yidun_device_id d1_payment d2_payment d3_payment  \\\n", "0  /YcMuQj4U7hEWBVQRAfXGD+emAo8KT+/   0.000000   0.000000   0.000000   \n", "1  2BCJsGbZpnJEDRERAFfCeWL3CXunsJZL   0.000000   0.000000   0.000000   \n", "2  3QvkPzIgn9JFCxAEFUfWXbRPpeC3VYuu   0.000000   0.000000  50.000000   \n", "3  3YP1XSMCU6VEWAEFRROHTkmojlmOpubY   0.000000   0.000000   0.000000   \n", "4  3Yhi7rSuK1dEXRVBQROHOWOw2bD1bOLe   0.000000   0.000000   0.000000   \n", "\n", "  d4_payment d5_payment d6_payment d7_payment d8_payment d9_payment  \\\n", "0   0.000000   0.000000   0.000000   0.000000   0.000000   0.000000   \n", "1   0.000000   0.000000   0.000000   0.000000   0.000000   0.000000   \n", "2   0.000000   0.000000   0.000000   0.000000   0.000000   0.000000   \n", "3   0.000000   0.000000   0.000000   0.000000   0.000000   0.000000   \n", "4   0.000000   0.000000   0.000000   0.000000   0.000000   0.000000   \n", "\n", "  d10_payment d11_payment d12_payment d13_payment d14_payment attribution_day  \\\n", "0    0.000000    0.000000    0.000000    0.000000    0.000000      2025-04-22   \n", "1    0.000000    0.000000    0.000000    0.000000    0.000000      2025-04-22   \n", "2    0.000000    0.000000    0.000000    0.000000    0.000000      2025-04-22   \n", "3    0.000000    0.000000    0.000000    0.000000    0.000000      2025-04-22   \n", "4    0.000000    0.000000    0.000000    0.000000    0.000000      2025-04-22   \n", "\n", "  channel_ty_adgroup_id proj_main_channel sdk_device_name adtrace_reattributed  \n", "0            8924279655     kuaishoulmpro          V2285A                    1  \n", "1           20133403399              qqqm        KOZ-AL40                    0  \n", "2                                appStore          iPhone                    0  \n", "3           19933785198              qqqm          V2410A                    1  \n", "4            9151232722        kuaishoulm          V2353A                    0  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["user_ltv_df_real.head()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from sklearn.linear_model import LinearRegression"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_34789/3463136422.py:1: FutureWarning: errors='ignore' is deprecated and will raise in a future version. Use to_numeric without passing `errors` and catch exceptions explicitly instead\n", "  training_df = user_ltv_df_real.copy().apply(lambda x: pd.to_numeric(x, errors='ignore'))\n"]}], "source": ["training_df = user_ltv_df_real.copy().apply(lambda x: pd.to_numeric(x, errors='ignore'))"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["for end_idx in range(1, 15):\n", "    sum_cols = [f\"d{i}_payment\" for i in range(1, end_idx+1)]\n", "    training_df[f\"sum_{end_idx}_days\"] = training_df[sum_cols].sum(axis=1)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>d1_payment</th>\n", "      <th>d2_payment</th>\n", "      <th>d3_payment</th>\n", "      <th>d4_payment</th>\n", "      <th>d5_payment</th>\n", "      <th>d6_payment</th>\n", "      <th>d7_payment</th>\n", "      <th>d8_payment</th>\n", "      <th>d9_payment</th>\n", "      <th>...</th>\n", "      <th>sum_5_days</th>\n", "      <th>sum_6_days</th>\n", "      <th>sum_7_days</th>\n", "      <th>sum_8_days</th>\n", "      <th>sum_9_days</th>\n", "      <th>sum_10_days</th>\n", "      <th>sum_11_days</th>\n", "      <th>sum_12_days</th>\n", "      <th>sum_13_days</th>\n", "      <th>sum_14_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>/YcMuQj4U7hEWBVQRAfXGD+emAo8KT+/</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2BCJsGbZpnJEDRERAFfCeWL3CXunsJZL</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3QvkPzIgn9JFCxAEFUfWXbRPpeC3VYuu</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>50.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>50.0</td>\n", "      <td>50.0</td>\n", "      <td>50.0</td>\n", "      <td>50.0</td>\n", "      <td>50.0</td>\n", "      <td>50.0</td>\n", "      <td>50.0</td>\n", "      <td>50.0</td>\n", "      <td>50.0</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3YP1XSMCU6VEWAEFRROHTkmojlmOpubY</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3Yhi7rSuK1dEXRVBQROHOWOw2bD1bOLe</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 34 columns</p>\n", "</div>"], "text/plain": ["                sdk_yidun_device_id  d1_payment  d2_payment  d3_payment  \\\n", "0  /YcMuQj4U7hEWBVQRAfXGD+emAo8KT+/         0.0         0.0         0.0   \n", "1  2BCJsGbZpnJEDRERAFfCeWL3CXunsJZL         0.0         0.0         0.0   \n", "2  3QvkPzIgn9JFCxAEFUfWXbRPpeC3VYuu         0.0         0.0        50.0   \n", "3  3YP1XSMCU6VEWAEFRROHTkmojlmOpubY         0.0         0.0         0.0   \n", "4  3Yhi7rSuK1dEXRVBQROHOWOw2bD1bOLe         0.0         0.0         0.0   \n", "\n", "   d4_payment  d5_payment  d6_payment  d7_payment  d8_payment  d9_payment  \\\n", "0         0.0         0.0         0.0         0.0         0.0         0.0   \n", "1         0.0         0.0         0.0         0.0         0.0         0.0   \n", "2         0.0         0.0         0.0         0.0         0.0         0.0   \n", "3         0.0         0.0         0.0         0.0         0.0         0.0   \n", "4         0.0         0.0         0.0         0.0         0.0         0.0   \n", "\n", "   ...  sum_5_days  sum_6_days  sum_7_days  sum_8_days  sum_9_days  \\\n", "0  ...         0.0         0.0         0.0         0.0         0.0   \n", "1  ...         0.0         0.0         0.0         0.0         0.0   \n", "2  ...        50.0        50.0        50.0        50.0        50.0   \n", "3  ...         0.0         0.0         0.0         0.0         0.0   \n", "4  ...         0.0         0.0         0.0         0.0         0.0   \n", "\n", "  sum_10_days  sum_11_days sum_12_days sum_13_days  sum_14_days  \n", "0         0.0          0.0         0.0         0.0          0.0  \n", "1         0.0          0.0         0.0         0.0          0.0  \n", "2        50.0         50.0        50.0        50.0         50.0  \n", "3         0.0          0.0         0.0         0.0          0.0  \n", "4         0.0          0.0         0.0         0.0          0.0  \n", "\n", "[5 rows x 34 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["training_df.head()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["with open('../data/any_day_matrix.json', 'r') as f:\n", "    baseline_matrix = np.array(json.load(f))"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Now analysising: start_idx=1, end_idx=2\n", "Learned coefficient (slope): 1.2620661835551439\n", "Rate matrix: 1.8023712976218702\n", "Check matrix: 1.6319470913097471\n", "Now analysising: start_idx=1, end_idx=3\n", "Learned coefficient (slope): 1.4249715588177856\n", "Rate matrix: 2.4880683791221747\n", "Check matrix: 2.1450865835169455\n", "Now analysising: start_idx=1, end_idx=4\n", "Learned coefficient (slope): 1.5776731748146797\n", "Rate matrix: 3.1704443154938096\n", "Check matrix: 2.6254293631045584\n", "Now analysising: start_idx=1, end_idx=5\n", "Learned coefficient (slope): 1.6813801343770356\n", "Rate matrix: 3.840262850186128\n", "Check matrix: 3.056412114973299\n", "Now analysising: start_idx=1, end_idx=6\n", "Learned coefficient (slope): 1.8015545861863964\n", "Rate matrix: 4.601229181860318\n", "Check matrix: 3.521625439423921\n", "Now analysising: start_idx=1, end_idx=7\n", "Learned coefficient (slope): 1.8963375690983206\n", "Rate matrix: 5.238335522376786\n", "Check matrix: 3.9746375213762173\n", "Now analysising: start_idx=1, end_idx=8\n", "Learned coefficient (slope): 2.001240931171231\n", "Rate matrix: 6.053730918542457\n", "Check matrix: 4.438975716772688\n", "Now analysising: start_idx=1, end_idx=9\n", "Learned coefficient (slope): 2.138318038241176\n", "Rate matrix: 6.61348496712419\n", "Check matrix: 4.8683231229994925\n", "Now analysising: start_idx=1, end_idx=10\n", "Learned coefficient (slope): 2.2432833745045477\n", "Rate matrix: 7.241897721862669\n", "Check matrix: 5.266011502352355\n", "Now analysising: start_idx=1, end_idx=11\n", "Learned coefficient (slope): 2.3402235086342915\n", "Rate matrix: 7.938983454999215\n", "Check matrix: 5.662536010448175\n", "Now analysising: start_idx=1, end_idx=12\n", "Learned coefficient (slope): 2.427166206960184\n", "Rate matrix: 8.666308865737227\n", "Check matrix: 6.035678796197979\n", "Now analysising: start_idx=1, end_idx=13\n", "Learned coefficient (slope): 2.4990293086850697\n", "Rate matrix: 9.420621239732464\n", "Check matrix: 6.405605948788057\n", "Now analysising: start_idx=1, end_idx=14\n", "Learned coefficient (slope): 2.571454288973638\n", "Rate matrix: 10.081918486617113\n", "Check matrix: 6.768637584176748\n", "Now analysising: start_idx=2, end_idx=3\n", "Learned coefficient (slope): 1.1607324716602951\n", "Rate matrix: 1.380441633976886\n", "Check matrix: 1.314433902262952\n", "Now analysising: start_idx=2, end_idx=4\n", "Learned coefficient (slope): 1.3047253709142337\n", "Rate matrix: 1.7590406148150697\n", "Check matrix: 1.608771128111436\n", "Now analysising: start_idx=2, end_idx=5\n", "Learned coefficient (slope): 1.4044052077161346\n", "Rate matrix: 2.1306724398314283\n", "Check matrix: 1.8728622583715768\n", "Now analysising: start_idx=2, end_idx=6\n", "Learned coefficient (slope): 1.543147443752204\n", "Rate matrix: 2.552875308173952\n", "Check matrix: 2.1579286841937875\n", "Now analysising: start_idx=2, end_idx=7\n", "Learned coefficient (slope): 1.6435708789558081\n", "Rate matrix: 2.9063576019483226\n", "Check matrix: 2.435518616100663\n", "Now analysising: start_idx=2, end_idx=8\n", "Learned coefficient (slope): 1.753245190925875\n", "Rate matrix: 3.358759056211127\n", "Check matrix: 2.720048793499863\n", "Now analysising: start_idx=2, end_idx=9\n", "Learned coefficient (slope): 1.8693078897996307\n", "Rate matrix: 3.66932439273213\n", "Check matrix: 2.983137841247253\n", "Now analysising: start_idx=2, end_idx=10\n", "Learned coefficient (slope): 1.9766223625740846\n", "Rate matrix: 4.0179832709375445\n", "Check matrix: 3.2268273465446895\n", "Now analysising: start_idx=2, end_idx=11\n", "Learned coefficient (slope): 2.0717939368248324\n", "Rate matrix: 4.404743609418474\n", "Check matrix: 3.4698036723136716\n", "Now analysising: start_idx=2, end_idx=12\n", "Learned coefficient (slope): 2.1495796714663045\n", "Rate matrix: 4.8082816660318235\n", "Check matrix: 3.6984524978404427\n", "Now analysising: start_idx=2, end_idx=13\n", "Learned coefficient (slope): 2.2166278715000987\n", "Rate matrix: 5.226792754723988\n", "Check matrix: 3.92513089603115\n", "Now analysising: start_idx=2, end_idx=14\n", "Learned coefficient (slope): 2.283974298919162\n", "Rate matrix: 5.593696759330139\n", "Check matrix: 4.147583962874962\n", "Now analysising: start_idx=3, end_idx=4\n", "Learned coefficient (slope): 1.1328808898555258\n", "Rate matrix: 1.2742593178296757\n", "Check matrix: 1.22392698890507\n", "Now analysising: start_idx=3, end_idx=5\n", "Learned coefficient (slope): 1.2302690806966625\n", "Rate matrix: 1.543471587200118\n", "Check matrix: 1.4248432387107672\n", "Now analysising: start_idx=3, end_idx=6\n", "Learned coefficient (slope): 1.3530565311225906\n", "Rate matrix: 1.8493178163711463\n", "Check matrix: 1.6417171532769048\n", "Now analysising: start_idx=3, end_idx=7\n", "Learned coefficient (slope): 1.4497730156136772\n", "Rate matrix: 2.1053824590725045\n", "Check matrix: 1.8529030724995998\n", "Now analysising: start_idx=3, end_idx=8\n", "Learned coefficient (slope): 1.5526508493517304\n", "Rate matrix: 2.4331047206501206\n", "Check matrix: 2.069369017960492\n", "Now analysising: start_idx=3, end_idx=9\n", "Learned coefficient (slope): 1.6553103391735073\n", "Rate matrix: 2.6580800683048427\n", "Check matrix: 2.26952289963872\n", "Now analysising: start_idx=3, end_idx=10\n", "Learned coefficient (slope): 1.746367228559883\n", "Rate matrix: 2.910650600534424\n", "Check matrix: 2.4549179239742123\n", "Now analysising: start_idx=3, end_idx=11\n", "Learned coefficient (slope): 1.8342801286828445\n", "Rate matrix: 3.1908220536126097\n", "Check matrix: 2.639770372888281\n", "Now analysising: start_idx=3, end_idx=12\n", "Learned coefficient (slope): 1.9073310468925984\n", "Rate matrix: 3.483147383913468\n", "Check matrix: 2.8137226919308174\n", "Now analysising: start_idx=3, end_idx=13\n", "Learned coefficient (slope): 1.9678315822237549\n", "Rate matrix: 3.786319266296126\n", "Check matrix: 2.9861759418055\n", "Now analysising: start_idx=3, end_idx=14\n", "Learned coefficient (slope): 2.0285335369177906\n", "Rate matrix: 4.05210667488735\n", "Check matrix: 3.155414628102949\n", "Now analysising: start_idx=4, end_idx=5\n", "Learned coefficient (slope): 1.0962210412783833\n", "Rate matrix: 1.2112696101990965\n", "Check matrix: 1.1641570548137334\n", "Now analysising: start_idx=4, end_idx=6\n", "Learned coefficient (slope): 1.2161968582384721\n", "Rate matrix: 1.4512884390917484\n", "Check matrix: 1.3413521951547058\n", "Now analysising: start_idx=4, end_idx=7\n", "Learned coefficient (slope): 1.309764772733564\n", "Rate matrix: 1.6522401913123943\n", "Check matrix: 1.5139000032650758\n", "Now analysising: start_idx=4, end_idx=8\n", "Learned coefficient (slope): 1.4069984316725717\n", "Rate matrix: 1.9094266658329762\n", "Check matrix: 1.6907618156306516\n", "Now analysising: start_idx=4, end_idx=9\n", "Learned coefficient (slope): 1.5013286503260834\n", "Rate matrix: 2.08598048380929\n", "Check matrix: 1.854295983512092\n", "Now analysising: start_idx=4, end_idx=10\n", "Learned coefficient (slope): 1.5892675744527733\n", "Rate matrix: 2.2841901642845017\n", "Check matrix: 2.00577154211657\n", "Now analysising: start_idx=4, end_idx=11\n", "Learned coefficient (slope): 1.6708177553956995\n", "Rate matrix: 2.5040602089119757\n", "Check matrix: 2.156803793704909\n", "Now analysising: start_idx=4, end_idx=12\n", "Learned coefficient (slope): 1.7402603284807645\n", "Rate matrix: 2.73346824714296\n", "Check matrix: 2.2989301791996475\n", "Now analysising: start_idx=4, end_idx=13\n", "Learned coefficient (slope): 1.794655463521193\n", "Rate matrix: 2.9713883299240864\n", "Check matrix: 2.439831761923107\n", "Now analysising: start_idx=4, end_idx=14\n", "Learned coefficient (slope): 1.8513906443756818\n", "Rate matrix: 3.1799702134326284\n", "Check matrix: 2.5781069105484766\n", "Now analysising: start_idx=5, end_idx=6\n", "Learned coefficient (slope): 1.1139939070199825\n", "Rate matrix: 1.198154751734587\n", "Check matrix: 1.1522089649401506\n", "Now analysising: start_idx=5, end_idx=7\n", "Learned coefficient (slope): 1.2034229365938787\n", "Rate matrix: 1.3640565051746123\n", "Check matrix: 1.3004259150474347\n", "Now analysising: start_idx=5, end_idx=8\n", "Learned coefficient (slope): 1.2930359722215272\n", "Rate matrix: 1.5763845222857722\n", "Check matrix: 1.452348554380556\n", "Now analysising: start_idx=5, end_idx=9\n", "Learned coefficient (slope): 1.3797487840171856\n", "Rate matrix: 1.7221438284631092\n", "Check matrix: 1.5928228720039683\n", "Now analysising: start_idx=5, end_idx=10\n", "Learned coefficient (slope): 1.4653344317837085\n", "Rate matrix: 1.8857817822318261\n", "Check matrix: 1.7229389572676654\n", "Now analysising: start_idx=5, end_idx=11\n", "Learned coefficient (slope): 1.5448023249347633\n", "Rate matrix: 2.067302100067039\n", "Check matrix: 1.8526742459590217\n", "Now analysising: start_idx=5, end_idx=12\n", "Learned coefficient (slope): 1.610882642893243\n", "Rate matrix: 2.256696794938709\n", "Check matrix: 1.9747594791387313\n", "Now analysising: start_idx=5, end_idx=13\n", "Learned coefficient (slope): 1.6632518523493223\n", "Rate matrix: 2.4531188637975316\n", "Check matrix: 2.095792618216348\n", "Now analysising: start_idx=5, end_idx=14\n", "Learned coefficient (slope): 1.7158655667300817\n", "Rate matrix: 2.6253199012480275\n", "Check matrix: 2.214569675017755\n", "Now analysising: start_idx=6, end_idx=7\n", "Learned coefficient (slope): 1.090323558105624\n", "Rate matrix: 1.1384643788290676\n", "Check matrix: 1.1286372130553446\n", "Now analysising: start_idx=6, end_idx=8\n", "Learned coefficient (slope): 1.1768703595457315\n", "Rate matrix: 1.3156768939935481\n", "Check matrix: 1.2604905868407261\n", "Now analysising: start_idx=6, end_idx=9\n", "Learned coefficient (slope): 1.260004506787926\n", "Rate matrix: 1.437330049369612\n", "Check matrix: 1.382407983682634\n", "Now analysising: start_idx=6, end_idx=10\n", "Learned coefficient (slope): 1.3388166225057403\n", "Rate matrix: 1.573905023121388\n", "Check matrix: 1.4953354900837457\n", "Now analysising: start_idx=6, end_idx=11\n", "Learned coefficient (slope): 1.4166001773387331\n", "Rate matrix: 1.7254049170811816\n", "Check matrix: 1.6079325038538088\n", "Now analysising: start_idx=6, end_idx=12\n", "Learned coefficient (slope): 1.4804627548728335\n", "Rate matrix: 1.88347689784784\n", "Check matrix: 1.7138900487910251\n", "Now analysising: start_idx=6, end_idx=13\n", "Learned coefficient (slope): 1.531623695725552\n", "Rate matrix: 2.0474140425067078\n", "Check matrix: 1.8189344832299672\n", "Now analysising: start_idx=6, end_idx=14\n", "Learned coefficient (slope): 1.5840824088493972\n", "Rate matrix: 2.1911359091530636\n", "Check matrix: 1.9220208680920887\n", "Now analysising: start_idx=7, end_idx=8\n", "Learned coefficient (slope): 1.0885376251164225\n", "Rate matrix: 1.1556592533415468\n", "Check matrix: 1.1168252936020426\n", "Now analysising: start_idx=7, end_idx=9\n", "Learned coefficient (slope): 1.170221921104282\n", "Rate matrix: 1.2625164880854098\n", "Check matrix: 1.2248470701584473\n", "Now analysising: start_idx=7, end_idx=10\n", "Learned coefficient (slope): 1.2464938914707242\n", "Rate matrix: 1.38248069275578\n", "Check matrix: 1.3249035853032958\n", "Now analysising: start_idx=7, end_idx=11\n", "Learned coefficient (slope): 1.3216250910098168\n", "Rate matrix: 1.51555459192829\n", "Check matrix: 1.4246672759450838\n", "Now analysising: start_idx=7, end_idx=12\n", "Learned coefficient (slope): 1.3839144175578433\n", "Rate matrix: 1.654401255650205\n", "Check matrix: 1.5185482358421774\n", "Now analysising: start_idx=7, end_idx=13\n", "Learned coefficient (slope): 1.4346160304418056\n", "Rate matrix: 1.798399739667316\n", "Check matrix: 1.6116201576465057\n", "Now analysising: start_idx=7, end_idx=14\n", "Learned coefficient (slope): 1.4856144959203195\n", "Rate matrix: 1.9246416048666264\n", "Check matrix: 1.7029571999393567\n", "Now analysising: start_idx=8, end_idx=9\n", "Learned coefficient (slope): 1.083280173360931\n", "Rate matrix: 1.092464309384353\n", "Check matrix: 1.0967221795344617\n", "Now analysising: start_idx=8, end_idx=10\n", "Learned coefficient (slope): 1.1573140896891199\n", "Rate matrix: 1.1962701711238735\n", "Check matrix: 1.1863123022851214\n", "Now analysising: start_idx=8, end_idx=11\n", "Learned coefficient (slope): 1.2321439952780489\n", "Rate matrix: 1.3114199428128308\n", "Check matrix: 1.2756402313831678\n", "Now analysising: start_idx=8, end_idx=12\n", "Learned coefficient (slope): 1.2924350175947867\n", "Rate matrix: 1.4315649278682827\n", "Check matrix: 1.3597007916470778\n", "Now analysising: start_idx=8, end_idx=13\n", "Learned coefficient (slope): 1.3427157026064667\n", "Rate matrix: 1.5561678189027677\n", "Check matrix: 1.4430369430912737\n", "Now analysising: start_idx=8, end_idx=14\n", "Learned coefficient (slope): 1.392799204116889\n", "Rate matrix: 1.6654057840160021\n", "Check matrix: 1.52481969175939\n", "Now analysising: start_idx=9, end_idx=10\n", "Learned coefficient (slope): 1.0726789399861123\n", "Rate matrix: 1.0950199112665011\n", "Check matrix: 1.0816889859824745\n", "Now analysising: start_idx=9, end_idx=11\n", "Learned coefficient (slope): 1.1454826656710162\n", "Rate matrix: 1.2004236033595166\n", "Check matrix: 1.1631389017085925\n", "Now analysising: start_idx=9, end_idx=12\n", "Learned coefficient (slope): 1.203571838001806\n", "Rate matrix: 1.310399722509037\n", "Check matrix: 1.2397859886669251\n", "Now analysising: start_idx=9, end_idx=13\n", "Learned coefficient (slope): 1.2531108140051488\n", "Rate matrix: 1.4244564381052687\n", "Check matrix: 1.3157725539058727\n", "Now analysising: start_idx=9, end_idx=14\n", "Learned coefficient (slope): 1.3022960091105158\n", "Rate matrix: 1.5244486888130235\n", "Check matrix: 1.3903427141472124\n", "Now analysising: start_idx=10, end_idx=11\n", "Learned coefficient (slope): 1.0738217582232168\n", "Rate matrix: 1.096257329212494\n", "Check matrix: 1.075298830608078\n", "Now analysising: start_idx=10, end_idx=12\n", "Learned coefficient (slope): 1.1317714003808457\n", "Rate matrix: 1.1966903149673576\n", "Check matrix: 1.146157541338793\n", "Now analysising: start_idx=10, end_idx=13\n", "Learned coefficient (slope): 1.1808644997209956\n", "Rate matrix: 1.300849805057646\n", "Check matrix: 1.2164056128488587\n", "Now analysising: start_idx=10, end_idx=14\n", "Learned coefficient (slope): 1.2283312629982732\n", "Rate matrix: 1.3921652685290853\n", "Check matrix: 1.2853442460490565\n", "Now analysising: start_idx=11, end_idx=12\n", "Learned coefficient (slope): 1.0576479916247266\n", "Rate matrix: 1.0916144258091394\n", "Check matrix: 1.0658967616384785\n", "Now analysising: start_idx=11, end_idx=13\n", "Learned coefficient (slope): 1.1063957040724108\n", "Rate matrix: 1.1866281486958201\n", "Check matrix: 1.1312256446526454\n", "Now analysising: start_idx=11, end_idx=14\n", "Learned coefficient (slope): 1.1545158950331147\n", "Rate matrix: 1.2699256200450302\n", "Check matrix: 1.1953367840288627\n", "Now analysising: start_idx=12, end_idx=13\n", "Learned coefficient (slope): 1.0487888219978463\n", "Rate matrix: 1.0870396365605497\n", "Check matrix: 1.0612900661352462\n", "Now analysising: start_idx=12, end_idx=14\n", "Learned coefficient (slope): 1.0956195764042858\n", "Rate matrix: 1.1633463153473085\n", "Check matrix: 1.1214376729988476\n", "Now analysising: start_idx=13, end_idx=14\n", "Learned coefficient (slope): 1.0478786134823117\n", "Rate matrix: 1.0701967768426521\n", "Check matrix: 1.0566740505568215\n"]}], "source": ["for start_idx in range(1, 14):\n", "    for end_idx in range(start_idx+1, 15):\n", "        print(f\"Now analysising: start_idx={start_idx}, end_idx={end_idx}\")\n", "        X = training_df[[f\"sum_{start_idx}_days\"]]\n", "        y = training_df[f\"sum_{end_idx}_days\"]\n", "\n", "        model = LinearRegression(fit_intercept=False)\n", "        model.fit(X, y)\n", "\n", "        # 输出回归系数\n", "        print(\"Learned coefficient (slope):\", model.coef_[0])\n", "        print(\"Rate matrix:\", baseline_matrix[start_idx-1, end_idx-1])\n", "        print(\"Check matrix:\", sum(y)/sum(training_df[f\"sum_{start_idx}_days\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}