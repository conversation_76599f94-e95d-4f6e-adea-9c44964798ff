{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"2,3\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(os.path.abspath(\"..\"))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import zipfile\n", "import pandas as pd\n", "from io import BytesIO\n", "from tqdm import tqdm\n", "import numpy as np\n", "import os\n", "import random\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pickle\n", "\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score\n", "from sklearn.model_selection import train_test_split\n", "\n", "import torch\n", "from pytorch_lightning import Trainer\n", "# from model.torch_model_whether_payment import WhetherPaymentModel\n", "from src.model.torch_model_payment_regression import PaymentRegressionModel"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["PaymentRegressionModel(\n", "  (model): Sequential(\n", "    (0): Linear(in_features=111, out_features=512, bias=True)\n", "    (1): ReLU()\n", "    (2): Linear(in_features=512, out_features=512, bias=True)\n", "    (3): ReLU()\n", "    (4): Linear(in_features=512, out_features=256, bias=True)\n", "    (5): ReLU()\n", "    (6): Linear(in_features=256, out_features=256, bias=True)\n", "    (7): ReLU()\n", "    (8): Linear(in_features=256, out_features=128, bias=True)\n", "    (9): ReLU()\n", "    (10): Linear(in_features=128, out_features=128, bias=True)\n", "    (11): ReLU()\n", "    (12): Linear(in_features=128, out_features=64, bias=True)\n", "    (13): ReLU()\n", "    (14): Linear(in_features=64, out_features=32, bias=True)\n", "    (15): ReLU()\n", "    (16): Linear(in_features=32, out_features=2, bias=True)\n", "  )\n", "  (loss_fn): ZeroInflatedMAELoss(\n", "    (bce): BCEWithLogitsLoss()\n", "  )\n", ")"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# model_whether_payment = WhetherPaymentModel()\n", "model_payment_regression = PaymentRegressionModel()\n", "# 加载 .pth 文件\n", "# state_dict_whether_payment = torch.load(\"../pth_model/whether_payment_model.pth\")\n", "state_dict_payment_regression = torch.load(\"../pth_model/payment_regression_model.pth\")\n", "\n", "# 加载状态字典到模型中\n", "# model_whether_payment.load_state_dict(state_dict_whether_payment)\n", "model_payment_regression.load_state_dict(state_dict_payment_regression)\n", "\n", "# model_whether_payment.eval()\n", "model_payment_regression.eval()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["pt_path = '../src/data/base_data_clean.pickle'\n", "\n", "full_df = pd.read_pickle(pt_path) \n", "\n", "seed = 1111  # 你可以换成任何固定的数\n", "random.seed(seed)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>attribution_day</th>\n", "      <th>channel_ty_campaign_id</th>\n", "      <th>channel_ty_csite_id</th>\n", "      <th>channel_ty_adgroup_id</th>\n", "      <th>channel_ty_account_id</th>\n", "      <th>proj_main_channel</th>\n", "      <th>sdk_proj_app_id</th>\n", "      <th>adtrace_attribution_mode</th>\n", "      <th>sdk_virtual_channel</th>\n", "      <th>...</th>\n", "      <th>behav.game_time_in_minutes_2</th>\n", "      <th>behav.final_delta_2</th>\n", "      <th>behav.max_delta_2</th>\n", "      <th>behav.min_delta_2</th>\n", "      <th>behav.max_gunlevel_2</th>\n", "      <th>behav.min_gunlevel_2</th>\n", "      <th>behav.max_boss_rate_2</th>\n", "      <th>behav.total_catch_2</th>\n", "      <th>behav.total_catch_boss_2</th>\n", "      <th>target</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UNxCzF/X1hxBCkVQQRfAwIY53A/PxUmE</td>\n", "      <td>2025-01-16</td>\n", "      <td>unknown</td>\n", "      <td>SITE_SET_MOMENTS</td>\n", "      <td>***********</td>\n", "      <td>********</td>\n", "      <td>appStore</td>\n", "      <td>10010.0</td>\n", "      <td>caid</td>\n", "      <td>unknown</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>aFHcRU2LyM1ESRAAVFPSYqTTSOu0PCAj</td>\n", "      <td>2025-03-25</td>\n", "      <td>unknown</td>\n", "      <td>SITE_SET_QQ_MUSIC_GAME</td>\n", "      <td>***********</td>\n", "      <td>********</td>\n", "      <td>qqqm</td>\n", "      <td>10010.0</td>\n", "      <td>gdt_click_id</td>\n", "      <td>gdt</td>\n", "      <td>...</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SoIkucJnEk1FH1UFQFOFTU03HiSi+oeU</td>\n", "      <td>2025-02-06</td>\n", "      <td>unknown</td>\n", "      <td>unknown</td>\n", "      <td>unknown</td>\n", "      <td>10021</td>\n", "      <td>appStore</td>\n", "      <td>10010.0</td>\n", "      <td>unknown</td>\n", "      <td>unknown</td>\n", "      <td>...</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>k191AGs/r2hESUAAUVLUoEthKuljS0tu</td>\n", "      <td>2025-01-27</td>\n", "      <td>*********</td>\n", "      <td>unknown</td>\n", "      <td>***********</td>\n", "      <td>********</td>\n", "      <td>b<PERSON><PERSON><PERSON></td>\n", "      <td>10010.0</td>\n", "      <td>bd_vid</td>\n", "      <td>unknown</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>F6wnHZjgMn1AGVEBVFKDZ0HIOhcs0w2a</td>\n", "      <td>2025-02-16</td>\n", "      <td>unknown</td>\n", "      <td>SITE_SET_WECHAT</td>\n", "      <td>***********</td>\n", "      <td>********</td>\n", "      <td>qqqm</td>\n", "      <td>10010.0</td>\n", "      <td>gdt_click_id</td>\n", "      <td>gdt</td>\n", "      <td>...</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 149 columns</p>\n", "</div>"], "text/plain": ["                sdk_yidun_device_id attribution_day channel_ty_campaign_id  \\\n", "0  UNxCzF/X1hxBCkVQQRfAwIY53A/PxUmE      2025-01-16                unknown   \n", "1  aFHcRU2LyM1ESRAAVFPSYqTTSOu0PCAj      2025-03-25                unknown   \n", "2  SoIkucJnEk1FH1UFQFOFTU03HiSi+oeU      2025-02-06                unknown   \n", "3  k191AGs/r2hESUAAUVLUoEthKuljS0tu      2025-01-27              *********   \n", "4  F6wnHZjgMn1AGVEBVFKDZ0HIOhcs0w2a      2025-02-16                unknown   \n", "\n", "      channel_ty_csite_id channel_ty_adgroup_id channel_ty_account_id  \\\n", "0        SITE_SET_MOMENTS           ***********              ********   \n", "1  SITE_SET_QQ_MUSIC_GAME           ***********              ********   \n", "2                 unknown               unknown                 10021   \n", "3                 unknown           ***********              ********   \n", "4         SITE_SET_WECHAT           ***********              ********   \n", "\n", "  proj_main_channel  sdk_proj_app_id adtrace_attribution_mode  \\\n", "0          appStore          10010.0                     caid   \n", "1              qqqm          10010.0             gdt_click_id   \n", "2          appStore          10010.0                  unknown   \n", "3         baidusslm          10010.0                   bd_vid   \n", "4              qqqm          10010.0             gdt_click_id   \n", "\n", "  sdk_virtual_channel  ...  behav.game_time_in_minutes_2 behav.final_delta_2  \\\n", "0             unknown  ...                           0.0                 0.0   \n", "1                 gdt  ...                          -1.0                -1.0   \n", "2             unknown  ...                          -1.0                -1.0   \n", "3             unknown  ...                           0.0                 0.0   \n", "4                 gdt  ...                          -1.0                -1.0   \n", "\n", "  behav.max_delta_2 behav.min_delta_2 behav.max_gunlevel_2  \\\n", "0               0.0               0.0                  0.0   \n", "1              -1.0              -1.0                 -1.0   \n", "2              -1.0              -1.0                 -1.0   \n", "3               0.0               0.0                  0.0   \n", "4              -1.0              -1.0                 -1.0   \n", "\n", "  behav.min_gunlevel_2 behav.max_boss_rate_2 behav.total_catch_2  \\\n", "0                  0.0                   0.0                 0.0   \n", "1                 -1.0                  -1.0                -1.0   \n", "2                 -1.0                  -1.0                -1.0   \n", "3                  0.0                   0.0                 0.0   \n", "4                 -1.0                  -1.0                -1.0   \n", "\n", "  behav.total_catch_boss_2 target  \n", "0                      0.0    0.0  \n", "1                     -1.0    0.0  \n", "2                     -1.0    0.0  \n", "3                      0.0    0.0  \n", "4                     -1.0    0.0  \n", "\n", "[5 rows x 149 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["full_df.head()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def transform_new_data(df_new, label_encoders_path='../src/preprocessors_label_encoders.pkl', scaler_path='../src/preprocessors_scaler.pkl'):\n", "    with open(label_encoders_path, 'rb') as f:\n", "        label_encoders = pickle.load(f)\n", "    with open(scaler_path, 'rb') as f:\n", "        scaler = pickle.load(f)\n", "    \n", "    key_df = df_new[['sdk_yidun_device_id', 'attribution_day', 'proj_main_channel', \n", "                    'channel_ty_adgroup_id', 'channel_ty_account_id', 'channel_ty_campaign_id', 'channel_ty_csite_id']]\n", "    df_new = df_new.drop(['sdk_yidun_device_id', 'attribution_day', 'proj_main_channel', \n", "                        'channel_ty_adgroup_id', 'channel_ty_account_id', 'channel_ty_campaign_id', 'channel_ty_csite_id'], axis=1)\n", "    \n", "    df_new = df_new.astype({col: float for col in df_new.select_dtypes(include='number').columns})\n", "    df_new = df_new.drop(columns=df_new.select_dtypes(include=['object']).columns)\n", "    \n", "    X = df_new.drop('target', axis=1)\n", "    y = df_new['target']\n", "    base = df_new[['d1_payment', 'd2_payment', 'd3_payment']].sum(axis=1)\n", "\n", "    # 对相同的列做 LabelEncoding（如果存在）\n", "    for col, encoder in label_encoders.items():\n", "        if col in X.columns:\n", "            X[col] = encoder.transform(X[col])\n", "\n", "    # 转 float + 同样列顺序 + 缩放\n", "    X = X.astype(float)\n", "    X_scaled = scaler.transform(X)\n", "\n", "    return torch.tensor(X_scaled, dtype=torch.float32), torch.tensor(y), torch.tensor(base), key_df"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["X, y, base, key_df = transform_new_data(full_df)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1.0000e+00, 0.0000e+00, 3.5750e-01, 0.0000e+00, 7.8261e-01, 0.0000e+00,\n", "        0.0000e+00, 0.0000e+00, 2.4594e-04, 1.2787e-01, 1.0000e+00, 1.0000e+00,\n", "        5.5142e-03, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "        1.9153e-02, 1.8388e-02, 0.0000e+00, 4.2220e-03, 9.8361e-02, 4.5455e-02,\n", "        6.8353e-04, 2.6867e-03, 5.9880e-03, 1.0000e+00, 1.4599e-02, 1.3793e-02,\n", "        5.0000e-01, 5.4393e-02, 1.1013e-03, 1.2361e-03, 1.3514e-02, 1.9960e-03,\n", "        2.5641e-02, 1.5625e-02, 5.0000e-01, 5.0000e-01, 1.0000e+00, 5.0000e-01,\n", "        9.9909e-03, 2.4759e-08, 2.9240e-07, 1.3606e-08, 4.5455e-05, 4.5455e-06,\n", "        5.5760e-03, 4.6804e-03, 1.4993e-03, 3.5286e-04, 2.0833e-02, 1.5385e-02,\n", "        2.5227e-04, 1.5798e-04, 6.2500e-03, 1.0000e+00, 4.8309e-03, 1.5601e-03,\n", "        5.0000e-01, 5.6497e-03, 1.3717e-03, 1.6234e-03, 6.6667e-03, 3.0581e-03,\n", "        5.5556e-02, 2.0833e-02, 5.0000e-01, 5.0000e-01, 5.0000e-01, 5.0000e-01,\n", "        6.9396e-04, 3.5041e-17, 1.1478e-15, 1.4286e-13, 4.5455e-11, 4.5455e-11,\n", "        2.2831e-05, 1.0088e-05, 9.9305e-04, 5.1335e-04, 2.0408e-02, 2.7778e-02,\n", "        2.8106e-04, 1.6586e-04, 6.0976e-03, 1.0000e+00, 4.4053e-03, 1.8692e-03,\n", "        5.0000e-01, 8.8496e-03, 5.8858e-04, 9.7943e-04, 1.0753e-02, 2.8986e-03,\n", "        1.7241e-02, 3.8462e-02, 5.0000e-01, 5.0000e-01, 5.0000e-01, 5.0000e-01,\n", "        6.9638e-04, 3.6476e-17, 1.1364e-15, 7.6923e-14, 4.5455e-11, 4.5455e-11,\n", "        2.0833e-05, 1.3086e-05, 1.1223e-03])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["X[0]"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1254/1254 [00:56<00:00, 22.14it/s]\n"]}], "source": ["from torch.utils.data import DataLoader, TensorDataset\n", "\n", "BATCH_SIZE = 1024\n", "dataset = TensorDataset(X, y, base)\n", "loader = DataLoader(dataset, batch_size=BATCH_SIZE, shuffle=False)\n", "\n", "all_predictions = []\n", "\n", "for batch_idx, (X_batch, y_batch, base_batch) in enumerate(tqdm(loader)):\n", "    outputs = model_payment_regression(X_batch).detach().cpu()\n", "    # print(outputs)\n", "    paid_probs = torch.sigmoid(outputs[:, 0])\n", "    paid_amounts = outputs[:, 1]\n", "    \n", "    predicted_LTVs = np.expm1(((paid_probs > 0.5).float() * paid_amounts).numpy())\n", "    # predicted_LTVs = ((paid_probs > 0.5).float() * paid_amounts).numpy()\n", "    for i in range(len(X_batch)):\n", "        idx = batch_idx * BATCH_SIZE + i\n", "        all_predictions.append([\n", "            float(base_batch[i]),\n", "            2.06 * float(base_batch[i]),\n", "            float(y_batch[i]) + float(base_batch[i]),\n", "            float(predicted_LTVs[i]) + float(base_batch[i]),\n", "            key_df.at[idx, 'sdk_yidun_device_id'],\n", "            key_df.at[idx, 'attribution_day']\n", "        ])\n", "    \n", "    # if batch_idx == 100:\n", "    #     break\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["result_df = pd.DataFrame(all_predictions, columns = ['3day_LTV', 'Predicted_LTV_baseline', 'Real_LTV', 'Predicted_LTV', 'sdk_yidun_device_id', 'attribution_day'])"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>3day_LTV</th>\n", "      <th>Predicted_LTV_baseline</th>\n", "      <th>Real_LTV</th>\n", "      <th>Predicted_LTV</th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>attribution_day</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>UNxCzF/X1hxBCkVQQRfAwIY53A/PxUmE</td>\n", "      <td>2025-01-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>aFHcRU2LyM1ESRAAVFPSYqTTSOu0PCAj</td>\n", "      <td>2025-03-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>SoIkucJnEk1FH1UFQFOFTU03HiSi+oeU</td>\n", "      <td>2025-02-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>k191AGs/r2hESUAAUVLUoEthKuljS0tu</td>\n", "      <td>2025-01-27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>F6wnHZjgMn1AGVEBVFKDZ0HIOhcs0w2a</td>\n", "      <td>2025-02-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>WVD/Xg1D3a5AWQFRFBOXBDDEmAoV99My</td>\n", "      <td>2025-03-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>********************************</td>\n", "      <td>2025-03-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>cJqRzHlcqZ9BTVBFRBLFDtt87xfzHQLD</td>\n", "      <td>2025-02-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>mrjrILNVWBJFHEQQURaBZm82gyGSWfVw</td>\n", "      <td>2025-01-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1ySqi7grt+dBWEBUEBLXA0Ea9T8k1RnG</td>\n", "      <td>2025-01-08</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   3day_LTV  Predicted_LTV_baseline  Real_LTV  Predicted_LTV  \\\n", "0       0.0                     0.0       0.0            0.0   \n", "1       0.0                     0.0       0.0            0.0   \n", "2       0.0                     0.0       0.0            0.0   \n", "3       0.0                     0.0       0.0            0.0   \n", "4       0.0                     0.0       0.0            0.0   \n", "5       0.0                     0.0       0.0            0.0   \n", "6       0.0                     0.0       0.0            0.0   \n", "7       0.0                     0.0       0.0            0.0   \n", "8       0.0                     0.0       0.0            0.0   \n", "9       0.0                     0.0       0.0            0.0   \n", "\n", "                sdk_yidun_device_id attribution_day  \n", "0  UNxCzF/X1hxBCkVQQRfAwIY53A/PxUmE      2025-01-16  \n", "1  aFHcRU2LyM1ESRAAVFPSYqTTSOu0PCAj      2025-03-25  \n", "2  SoIkucJnEk1FH1UFQFOFTU03HiSi+oeU      2025-02-06  \n", "3  k191AGs/r2hESUAAUVLUoEthKuljS0tu      2025-01-27  \n", "4  F6wnHZjgMn1AGVEBVFKDZ0HIOhcs0w2a      2025-02-16  \n", "5  WVD/Xg1D3a5AWQFRFBOXBDDEmAoV99My      2025-03-31  \n", "6  ********************************      2025-03-30  \n", "7  cJqRzHlcqZ9BTVBFRBLFDtt87xfzHQLD      2025-02-03  \n", "8  mrjrILNVWBJFHEQQURaBZm82gyGSWfVw      2025-01-31  \n", "9  1ySqi7grt+dBWEBUEBLXA0Ea9T8k1RnG      2025-01-08  "]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.set_option('display.max_columns', 500)\n", "\n", "result_df.head(10)"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.hist(np.log1p(result_df['Real_LTV']), bins = 50)\n", "plt.yscale('log')\n", "plt.grid()"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.hist(np.log1p(result_df['Predicted_LTV']), bins = 50)\n", "plt.yscale('log')\n", "plt.grid()"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAicAAAGdCAYAAADJ6dNTAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAKYhJREFUeJzt3X9QHPd9//HXgdDJlwpczBiZCIgy48RGWHIExCMkYtFWeE6uPJKTVK0aWUmkTjScayiTNlJVN0iphWsaDZ1yyKGdqabtqGHUVkraqINv2gSUuh6Dokttk9ZWi4MSIzNSXJ0Ek+N8bP/wl/sa8/NgYT/LPh8z+WP3Vp997/vO3Cu7n93zWZZlCQAAwBAZThcAAADwfoQTAABgFMIJAAAwCuEEAAAYhXACAACMQjgBAABGIZwAAACjEE4AAIBRVjhdQLrGxsb01ltvafXq1fL5fE6XAwAA5sCyLN26dUsFBQXKyJj53Ijrwslbb72lwsJCp8sAAADzcPXqVa1du3bGbVwXTlavXi3pvYPLzs62dexEIqEXXnhBNTU1ysrKsnVsr6GX9qGX9qGX9qGX9vFKL2OxmAoLC1Pf4zNxXTgZv5STnZ29KOEkEAgoOzt7WX9AlgK9tA+9tA+9tA+9tI/XejmXKRlMiAUAAEZxTTgJh8MqKSlRRUWF06UAAIBF5JpwEgqF1NfXp56eHqdLAQAAi8g14QQAAHgD4QQAABiFcAIAAIxCOAEAAEYhnAAAAKMQTgAAgFEIJwAAwCiEEwAAYBTCCQAAMIoj4aS/v1/V1dUqKSnRAw88oOHhYSfKAAAABnLkV4k///nP64/+6I9UVVWln/3sZ/L7/U6UMa3Sxk7Fk9P/auKbzz66hNUAAOAtSx5OXnvtNWVlZamqqkqSlJubu9QlAAAAg6V9Wae7u1s7d+5UQUGBfD6fzp8/P2mbtrY2rVu3TqtWrVJZWZkuXryYeu2NN97QL/zCL+ixxx7Tpk2bdOLEiQUdAAAAWF7SDifDw8PauHGjWltbp3y9o6ND9fX1Onr0qC5fvqyqqioFg0ENDAxIkhKJhC5evKhwOKx///d/VyQSUSQSWdhRAACAZSPtyzrBYFDBYHDa10+ePKkDBw7o4MGDkqSWlhZ1dnbq1KlTampq0tq1a1VRUaHCwkJJ0o4dOxSNRrV9+/Ypx4vH44rH46nlWCwm6b2Qk0gk0i1/RuPj+TOsOW2H6Y33iF4tHL20D720D720j1d6mc7x2TrnZHR0VJcuXdLhw4cnrK+pqdGLL74oSaqoqNDbb7+td955Rzk5Oeru7taXvvSlacdsamrSsWPHJq1/4YUXFAgE7Cw/5WvlYzO+fuHChUXZ73LEWTH70Ev70Ev70Ev7LPdejoyMzHlbW8PJ9evXlUwmlZ+fP2F9fn6+rl279t4OV6zQiRMn9KlPfUqWZammpka/+qu/Ou2YR44cUUNDQ2o5FoupsLBQNTU1ys7OtrN8JRIJRSIRPd2bofjY9HfrvNr4iK37XY7Ge7l9+3ZlZWU5XY6r0Uv70Ev70Ev7eKWX41c+5mJR7tbx+SZ+sVuWNWHdbJeG3s/v98vv9yscDiscDiuZTEqSsrKyFu1NjI/5ZryVeDl/eOy2mO+T19BL+9BL+9BL+yz3XqZzbLY+hC0vL0+ZmZmpsyTjhoaGJp1NSVcoFFJfX596enoWNA4AADCbreFk5cqVKisrm3TdLBKJqLKy0s5dAQCAZSrtyzq3b9/WlStXUsv9/f2KRqPKzc1VUVGRGhoatG/fPpWXl2vz5s1qb2/XwMCADh06tKBCP3hZBwAALE9ph5Pe3l5VV1enlscnq+7fv1+nT5/Wnj17dOPGDR0/flyDg4MqLS3VhQsXVFxcvKBCQ6GQQqGQYrGYcnJyFjQWAAAwV9rhZNu2bbKsmZ8DUltbq9ra2nkXBQAAvMuRXyWej3A4rJKSElVUVDhdCgAAWESuCSfcrQMAgDe4JpwAAABvIJwAAACjuCacMOcEAABvcE04Yc4JAADe4JpwAgAAvIFwAgAAjOKacMKcEwAAvME14YQ5JwAAeINrwgkAAPAGwgkAADAK4QQAABiFcAIAAIzimnDC3ToAAHiDa8IJd+sAAOANrgknAADAGwgnAADAKIQTAABgFMIJAAAwCuEEAAAYxTXhhFuJAQDwBteEE24lBgDAG1wTTgAAgDcQTgAAgFEIJwAAwCiEEwAAYBTCCQAAMArhBAAAGIVwAgAAjOKacMJD2AAA8AbXhBMewgYAgDe4JpwAAABvIJwAAACjEE4AAIBRCCcAAMAohBMAAGAUwgkAADAK4QQAABiFcAIAAIxCOAEAAEZxJJysWLFCDz74oB588EEdPHjQiRIAAIChVjix0zvvvFPRaNSJXQMAAMNxWQcAABgl7XDS3d2tnTt3qqCgQD6fT+fPn5+0TVtbm9atW6dVq1aprKxMFy9enPB6LBZTWVmZtm7dqq6urnkXDwAAlp+0w8nw8LA2btyo1tbWKV/v6OhQfX29jh49qsuXL6uqqkrBYFADAwOpbd58801dunRJzz//vJ544gnFYrH5HwEAAFhW0p5zEgwGFQwGp3395MmTOnDgQGqia0tLizo7O3Xq1Ck1NTVJkgoKCiRJpaWlKikp0euvv67y8vIpx4vH44rH46nl8SCTSCSUSCTSLX9G4+P5M6w5bYfpjfeIXi0cvbQPvbQPvbSPV3qZzvH5LMua+Zt4pn/s8+ncuXPatWuXJGl0dFSBQEBnz57V7t27U9vV1dUpGo2qq6tL77zzjgKBgPx+v37yk59oy5Ytunz5snJzc6fcR2Njo44dOzZp/ZkzZxQIBOZbOgAAWEIjIyPau3evbt68qezs7Bm3tfVunevXryuZTCo/P3/C+vz8fF27dk2S9KMf/Uhf+tKXlJGRIZ/Ppz/90z+dNphI0pEjR9TQ0JBajsViKiwsVE1NzawHl65EIqFIJKKnezMUH/NNu92rjY/Yut/laLyX27dvV1ZWltPluBq9tA+9tA+9tI9XepnOFI5FuZXY55v4xW5ZVmpdZWWlXnnllTmP5ff75ff7J63PyspatDcxPuZTPDl9OFnOHx67Leb75DX00j700j700j7LvZfpHJuttxLn5eUpMzMzdZZk3NDQ0KSzKekKh8MqKSlRRUXFgsYBAABmszWcrFy5UmVlZYpEIhPWRyIRVVZWLmjsUCikvr4+9fT0LGgcAABgtrQv69y+fVtXrlxJLff39ysajSo3N1dFRUVqaGjQvn37VF5ers2bN6u9vV0DAwM6dOjQggoNh8MKh8NKJpMLGgcAAJgt7XDS29ur6urq1PL4ZNX9+/fr9OnT2rNnj27cuKHjx49rcHBQpaWlunDhgoqLixdUaCgUUigUUiwWU05OzoLGAgAA5ko7nGzbtk2z3X1cW1ur2traeRcFAAC8i9/WAQAARnFNOOFuHQAAvME14YS7dQAA8AbXhBMAAOANrgknXNYBAMAbXBNOuKwDAIA3uCacAAAAbyCcAAAAoxBOAACAUVwTTpgQCwCAN7gmnDAhFgAAb3BNOAEAAN5AOAEAAEYhnAAAAKO4JpwwIRYAAG9wTThhQiwAAN7gmnACAAC8YYXTBQDT+cjh78y6zZvPProElQAAlhJnTgAAgFEIJwAAwCiEEwAAYBTXhBNuJQYAwBtcE064lRgAAG9wTTgBAADeQDgBAABGIZwAAACjEE4AAIBRCCcAAMAohBMAAGAUwgkAADAK4QQAABjFNeGEJ8QCAOANrgknPCEWAABvcE04AQAA3kA4AQAARiGcAAAAoxBOAACAUQgnAADAKIQTAABgFMIJAAAwCuEEAAAYxbFwMjIyouLiYn35y192qgQAAGAgx8LJM888o4ceesip3QMAAEM5Ek7eeOMN/ed//qd27NjhxO4BAIDBVqT7D7q7u9Xc3KxLly5pcHBQ586d065duyZs09bWpubmZg0ODmr9+vVqaWlRVVVV6vUvf/nLam5u1osvvrjgAwC86iOHvzPrNm8+++gSVAIA9kr7zMnw8LA2btyo1tbWKV/v6OhQfX29jh49qsuXL6uqqkrBYFADAwOSpG9961v62Mc+po997GMLqxwAACxLaZ85CQaDCgaD075+8uRJHThwQAcPHpQktbS0qLOzU6dOnVJTU5NeeuklffOb39TZs2d1+/ZtJRIJZWdn6w//8A+nHC8ejysej6eWY7GYJCmRSCiRSKRb/ozGx/NnWHPaDtMb79FCeuXPnPl9WOj4bjFdL+lP+uz4XOI99NI+XullOsfnsyxr9r9w0/1jn2/CZZ3R0VEFAgGdPXtWu3fvTm1XV1enaDSqrq6uCf/+9OnTevXVV/Unf/In0+6jsbFRx44dm7T+zJkzCgQC8y0dAAAsoZGREe3du1c3b95Udnb2jNumfeZkJtevX1cymVR+fv6E9fn5+bp27dq8xjxy5IgaGhpSy7FYTIWFhaqpqZn14NKVSCQUiUT0dG+G4mO+abd7tfERW/e7HI33cvv27crKyprXGKWNnbNu44X3Yrpe0p/02fG5xHvopX280svxKx9zYWs4GefzTfxityxr0jpJ+vznPz/rWH6/X36/f9L6rKysRXsT42M+xZPTh5Pl/OGx20Lep5neg/eP7xUf7CX9mb/F/PvhNfTSPsu9l+kcm63hJC8vT5mZmZPOkgwNDU06m5KucDiscDisZDK5oHGAqXDnCwCYw9bnnKxcuVJlZWWKRCIT1kciEVVWVi5o7FAopL6+PvX09CxoHAAAYLa0z5zcvn1bV65cSS339/crGo0qNzdXRUVFamho0L59+1ReXq7Nmzervb1dAwMDOnTokK2FAwCA5SntcNLb26vq6urU8vhk1f379+v06dPas2ePbty4oePHj2twcFClpaW6cOGCiouLF1Qol3UAAPCGtMPJtm3bNNvdx7W1taqtrZ13UVMJhUIKhUKKxWLKycmxdWwAAGAOx374DwAAYCquCSfhcFglJSWqqKhwuhQAALCIXBNOuFsHAABvcE04AQAA3kA4AQAARnFNOGHOCQAA3uCacMKcEwAAvME14QQAAHgD4QQAABjFNeGEOScAAHiDa8IJc04AAPAG14QTAADgDYQTAABgFMIJAAAwimvCCRNiAQDwBteEEybEAgDgDa4JJwAAwBtWOF0AsJx85PB3Zt3mzWcfXYJKAMC9OHMCAACMQjgBAABGIZwAAACjuGbOSTgcVjgcVjKZdLoUAPPEnBwAc+GaMyfcSgwAgDe4JpwAAABvIJwAAACjEE4AAIBRCCcAAMAohBMAAGAUwgkAADCKa55zAmBxzOXZI3PB80kA2IUzJwAAwCiuCSfhcFglJSWqqKhwuhQAALCIXHNZJxQKKRQKKRaLKScnx+lyABhuqstV/kxLz31SKm3sVDzp41IUYCjXnDkBAADe4JozJ4CXvP//9X/w/+0DwHLHmRMAAGAUwgkAADAK4QQAABiFcAIAAIxCOAEAAEYhnAAAAKMQTgAAgFGW/Dknt27d0i/90i8pkUgomUzqqaee0m/91m8tdRmAJ9j1o36Y2Vz6bNfTaJdyX4BTljycBAIBdXV1KRAIaGRkRKWlpXr88cd11113LXUpAAzEly+AJb+sk5mZqUAgIEn6+c9/rmQyKcuylroMAABgqLTPnHR3d6u5uVmXLl3S4OCgzp07p127dk3Ypq2tTc3NzRocHNT69evV0tKiqqqq1Ov/+7//q4cfflhvvPGGmpublZeXt+ADAeAsLiEBsEva4WR4eFgbN27UF77wBX3605+e9HpHR4fq6+vV1tamLVu26Bvf+IaCwaD6+vpUVFQkSbrzzjv1wx/+UG+//bYef/xxfeYzn1F+fv7CjwaAJxCEgOUt7XASDAYVDAanff3kyZM6cOCADh48KElqaWlRZ2enTp06paampgnb5ufna8OGDeru7tZnP/vZKceLx+OKx+Op5VgsJklKJBJKJBLplj+j8fH8GTNfZrJ7v8vReI8W0it/5uyX++x6L+za12KMM/55nO1zidl9sJemfX5M29dc9sHfw4XzSi/TOT6ftYAJHz6fb8JlndHRUQUCAZ09e1a7d+9ObVdXV6doNKquri69/fbbuuOOO5Sdna1YLKbNmzfrb//2b7Vhw4Yp99HY2Khjx45NWn/mzJnU3BUAAGC2kZER7d27Vzdv3lR2dvaM29p6t87169eVTCYnXaLJz8/XtWvXJEk/+clPdODAAVmWJcuy9OSTT04bTCTpyJEjamhoSC3HYjEVFhaqpqZm1oNLVyKRUCQS0dO9GYqPTf/T9K82PmLrfpej8V5u375dWVlZ8xqjtLFz1m3sei/s2tdijOPPsPS18rFZP5eY3Qd7adrnx7R9zcSO/8bxHq/0cvzKx1wsyq3EPt/EP6CWZaXWlZWVKRqNznksv98vv9+vcDiscDisZDIpScrKylq0NzE+5lM8Of2XwHL+8NhtIe/TTO/B+8e3g137WsxxZvtcYu7GezmX92Ju81vc91m1y2L+Lfaa5d7LdI7N1nCSl5enzMzM1FmScUNDQwue8BoKhRQKhRSLxZSTk7OgsQBAYmItYCpbn3OycuVKlZWVKRKJTFgfiURUWVlp564AAMAylfaZk9u3b+vKlSup5f7+fkWjUeXm5qqoqEgNDQ3at2+fysvLtXnzZrW3t2tgYECHDh1aUKEfvKwDAACWp7TDSW9vr6qrq1PL45NV9+/fr9OnT2vPnj26ceOGjh8/rsHBQZWWlurChQsqLi5eUKFc1gEAwBvSDifbtm2b9XHztbW1qq2tnXdRAIDFxW8YwWRL/sN/88VlHQCYGyb6wu1cE064rAPAzdwYGGaq2Z9p6blPLmEx8JQl/1ViAACAmRBOAACAUVwTTsLhsEpKSlRRUeF0KQAAYBG5JpyEQiH19fWpp6fH6VIAAMAics2EWACAO3HbMtLlmjMnAADAG1wTTphzAgCAN7gmnDDnBAAAb3BNOAEAAN5AOAEAAEbhbh0AwLyVNnYqnvQ5XQaWGc6cAAAAo7gmnHC3DgAA3uCacMLdOgAAeINrwgkAAPAGwgkAADAKd+sAABzH7+/g/ThzAgAAjEI4AQAARnFNOOFWYgAAvME14YRbiQEA8AbXhBMAAOANhBMAAGAUbiUGALgCtxt7B2dOAACAUQgnAADAKIQTAABgFMIJAAAwimvCCQ9hAwDAG1xzt04oFFIoFFIsFlNOTo7T5QAADMQdPcuDa86cAAAAbyCcAAAAoxBOAACAUQgnAADAKIQTAABgFNfcrQMAgB24o8d8nDkBAABGIZwAAACjEE4AAIBRljycXL16Vdu2bVNJSYk2bNigs2fPLnUJAADAYEs+IXbFihVqaWnRgw8+qKGhIW3atEk7duzQhz70oaUuBQAAGGjJw8k999yje+65R5J09913Kzc3Vz/72c8IJwAAQNI8wkl3d7eam5t16dIlDQ4O6ty5c9q1a9eEbdra2tTc3KzBwUGtX79eLS0tqqqqmjRWb2+vxsbGVFhYOO8DAADAbtxu7Ky055wMDw9r48aNam1tnfL1jo4O1dfX6+jRo7p8+bKqqqoUDAY1MDAwYbsbN27oiSeeUHt7+/wqBwAAy1LaZ06CwaCCweC0r588eVIHDhzQwYMHJUktLS3q7OzUqVOn1NTUJEmKx+PavXu3jhw5osrKyhn3F4/HFY/HU8uxWEySlEgklEgk0i1/RuPj+TOsOW2H6Y33aCG98mfO/D4sdPzF2NdijDP+eZztc4nZ0Uv70Ev7/v7Y8ffSDdI5Pp9lWfP+ZPl8vgmXdUZHRxUIBHT27Fnt3r07tV1dXZ2i0ai6urpkWZb27t2rj3/842psbJx1H42NjTp27Nik9WfOnFEgEJhv6QAAYAmNjIxo7969unnzprKzs2fc1tYJsdevX1cymVR+fv6E9fn5+bp27Zok6d/+7d/U0dGhDRs26Pz585Kkv/7rv9YDDzww5ZhHjhxRQ0NDajkWi6mwsFA1NTWzHly6EomEIpGInu7NUHzMN+12rzY+Yut+l6PxXm7fvl1ZWVnzGqO0sXPWbex6L+za12KM48+w9LXysVk/l5gdvbQPvbTv748dfy/dYPzKx1wsyt06Pt/ED6plWal1W7du1djY2JzH8vv98vv9CofDCofDSiaTkqSsrKxFexPjYz7Fk9P/x7acPzx2W8j7NNN78P7x7WDXvhZznNk+l5g7emkfL/fS7u+CxfxeM0E6x2brQ9jy8vKUmZmZOksybmhoaNLZlHSFQiH19fWpp6dnQeMAAACz2RpOVq5cqbKyMkUikQnrI5HIrBNfAQAApHlc1rl9+7auXLmSWu7v71c0GlVubq6KiorU0NCgffv2qby8XJs3b1Z7e7sGBgZ06NChBRX6wcs6AABgeUo7nPT29qq6ujq1PD5Zdf/+/Tp9+rT27NmjGzdu6Pjx4xocHFRpaakuXLig4uLiBRUaCoUUCoUUi8WUk5OzoLEAAIC50g4n27Zt02x3H9fW1qq2tnbeRQEAYDqeIrt4lvxXiecrHA6rpKREFRUVTpcCAAAWkWvCCXfrAADgDa4JJwAAwBsIJwAAwCiuCSfMOQEAwBtcE06YcwIAgDe4JpwAAABvIJwAAACjEE4AAIBRXBNOmBALAIA3uCacMCEWAABvcE04AQAA3kA4AQAARiGcAAAAo7gmnDAhFgAAb3BNOGFCLAAA3uCacAIAALxhhdMFYPn5yOHvyJ9p6blPSqWNnYonfZO2efPZRx2oDADgBpw5AQAARiGcAAAAoxBOAACAUVwz5yQcDiscDiuZTDpdCgAAc/KRw9+ZdZs3vlazBJW4i2vOnHArMQAA3uCacAIAALyBcAIAAIxCOAEAAEZxzYRYAACWo9LGzhkfWil578GVnDkBAABGIZwAAACjEE4AAIBRCCcAAMAorgkn4XBYJSUlqqiocLoUAACwiFwTTnhCLAAA3uCacAIAALyBcAIAAIxCOAEAAEbhCbHLwFx+kttrTxcEALgX4QTL3lzCGwDAHFzWAQAARiGcAAAAoxBOAACAURwJJ7t379Yv/uIv6jOf+YwTuwcAAAZzJJw89dRT+qu/+isndg0AAAznSDiprq7W6tWrndg1AAAwXNrhpLu7Wzt37lRBQYF8Pp/Onz8/aZu2tjatW7dOq1atUllZmS5evGhHrQAAwAPSDifDw8PauHGjWltbp3y9o6ND9fX1Onr0qC5fvqyqqioFg0ENDAwsuFgAALD8pf0QtmAwqGAwOO3rJ0+e1IEDB3Tw4EFJUktLizo7O3Xq1Ck1NTWlXWA8Hlc8Hk8tx2IxSVIikVAikUh7vJmMj+fPsOa0nSn8mTPXKy1tzf5MK9XD6Xo5l3rsOq65jDMXTtU8Wy8xd/TSPvTSPnPppWnfO/ORzjH4LMua9yfL5/Pp3Llz2rVrlyRpdHRUgUBAZ8+e1e7du1Pb1dXVKRqNqqurK7Xue9/7nlpbW/V3f/d3M+6jsbFRx44dm7T+zJkzCgQC8y0dAAAsoZGREe3du1c3b95Udnb2jNva+vj669evK5lMKj8/f8L6/Px8Xbt2LbX8yCOP6Ac/+IGGh4e1du1anTt3ThUVFVOOeeTIETU0NKSWY7GYCgsLVVNTM+vBpSuRSCgSiejp3gzFx3zTbvdq4yO27nehShs7Z91mKWsubeyUP8PS18rHpu3lXOqx67jmMs5cOFXzbL3E3NFL+9BL+8yll6Z978zH+JWPuViU39bx+SY217KsCes6O+f+ZeH3++X3+yetz8rKUlZW1vyLnEF8zKd4cvr/2BZrv/M1U63jlrLm99czXS/nUo9dxzWXcebC6Zpn+1xi7uilfeilfWbqpWnfO/ORzjHYGk7y8vKUmZk54SyJJA0NDU06m5KucDiscDisZDK5oHGA+eIHBAFgadj6nJOVK1eqrKxMkUhkwvpIJKLKysoFjR0KhdTX16eenp4FjQMAAMyW9pmT27dv68qVK6nl/v5+RaNR5ebmqqioSA0NDdq3b5/Ky8u1efNmtbe3a2BgQIcOHbK1cAAAsDylHU56e3tVXV2dWh6frLp//36dPn1ae/bs0Y0bN3T8+HENDg6qtLRUFy5cUHFx8YIK5bIOAADekHY42bZtm2a7+7i2tla1tbXzLmoqoVBIoVBIsVhMOTk5to4NAADM4chv6wAAAEzHNeEkHA6rpKRk2uehAACA5cE14YS7dQAA8AbXhBMAAOANi/KE2MXA3TqYCg9GA4DlxzVnTrisAwCAN7gmnAAAAG8gnAAAAKMQTgAAgFGYEOuguUzmfPPZR5egEiwlJvECSNdc/24sl+8M15w5YUIsAADe4JpwAgAAvIFwAgAAjEI4AQAARmFC7CJh0uPM6A8AYDquOXPChFgAALzBNeEEAAB4A+EEAAAYhXACAACMQjgBAABGIZwAAACjcCux4Zbyllt+6wcAlj83/K13zZkTbiUGAMAbXBNOAACANxBOAACAUQgnAADAKIQTAABgFMIJAAAwCuEEAAAYhXACAACMQjgBAABG4Qmx87CUT221ixtrBgCkZ7n8rXfNmROeEAsAgDe4JpwAAABvIJwAAACjEE4AAIBRCCcAAMAohBMAAGAUwgkAADAK4QQAABiFcAIAAIxCOAEAAEZxJJz80z/9kz7+8Y/r3nvv1V/8xV84UQIAADDUkv+2zrvvvquGhgZ997vfVXZ2tjZt2qTHH39cubm5S10KAAAw0JKfOXn55Ze1fv16ffjDH9bq1au1Y8cOdXZ2LnUZAADAUGmHk+7ubu3cuVMFBQXy+Xw6f/78pG3a2tq0bt06rVq1SmVlZbp48WLqtbfeeksf/vCHU8tr167VT3/60/lVDwAAlp20L+sMDw9r48aN+sIXvqBPf/rTk17v6OhQfX292tratGXLFn3jG99QMBhUX1+fioqKZFnWpH/j8/mm3V88Hlc8Hk8tx2IxSVIikVAikUi3/BmNj+fPmFwj3jOXnvszrVQP6eXC0Uv70Ev70Ev7mNhLu79f0x3TZ02VFub6j30+nTt3Trt27Uqte+ihh7Rp0yadOnUqte7+++/Xrl271NTUpBdffFHNzc06d+6cJKmurk4PPfSQ9u7dO+U+GhsbdezYsUnrz5w5o0AgMN/SAQDAEhoZGdHevXt18+ZNZWdnz7itreFkdHRUgUBAZ8+e1e7du1Pb1dXVKRqNqqurS++++67uv/9+fe9730tNiH3ppZd01113TbmPqc6cFBYW6vr167MeXLoSiYQikYie7s1QfGz6szmYnT/D0tfKx+ilDeilfeilfeilfUzs5auNj9g+ZiwWU15e3pzCia1361y/fl3JZFL5+fkT1ufn5+vatWvv7XDFCn39619XdXW1xsbG9Hu/93vTBhNJ8vv98vv9k9ZnZWUpKyvLzvJT4mM+xZNmfEDcjl7ah17ah17ah17ax6ReLsb3azpjLsqtxB+cQ2JZ1oR1jz32mB577LG0xgyHwwqHw0omk7bUCAAAzGTrrcR5eXnKzMxMnSUZNzQ0NOlsSrpCoZD6+vrU09OzoHEAAIDZbA0nK1euVFlZmSKRyIT1kUhElZWVdu4KAAAsU2lf1rl9+7auXLmSWu7v71c0GlVubq6KiorU0NCgffv2qby8XJs3b1Z7e7sGBgZ06NChBRXKZR0AALwh7XDS29ur6urq1HJDQ4Mkaf/+/Tp9+rT27NmjGzdu6Pjx4xocHFRpaakuXLig4uLiBRUaCoUUCoUUi8WUk5OzoLEAAIC50g4n27Ztm/JBau9XW1ur2traeRcFAAC8y5FfJZ6PcDiskpISVVRUOF0KAABYRK4JJ9ytAwCAN7gmnAAAAG8gnAAAAKO4Jpww5wQAAG9wTThhzgkAAN7gmnACAAC8YVF++G8xjT9jJRaL2T52IpHQyMiIkvFMjRnyy5Bulcy0NDKSpJc2oJf2oZf2oZf2MbGXi/EdOz7mbM9KkySfNZetDDD++PrR0VH993//t9PlAACAebh69arWrl074zauCSfjxsbG9NZbb2n16tXy+exNmLFYTIWFhbp69aqys7NtHdtr6KV96KV96KV96KV9vNJLy7J069YtFRQUKCNj5lklrrusk5GRMWviWqjs7Oxl/QFZSvTSPvTSPvTSPvTSPl7o5Vx/G48JsQAAwCiEEwAAYBTCyfv4/X599atfld/vd7oU16OX9qGX9qGX9qGX9qGXk7luQiwAAFjeOHMCAACMQjgBAABGIZwAAACjEE4AAIBRCCf/T1tbm9atW6dVq1aprKxMFy9edLok12lqalJFRYVWr16tu+++W7t27dJ//dd/OV3WstDU1CSfz6f6+nqnS3Gln/70p/rc5z6nu+66S4FAQA8++KAuXbrkdFmu8+677+oP/uAPtG7dOt1xxx366Ec/quPHj2tsbMzp0ozX3d2tnTt3qqCgQD6fT+fPn5/wumVZamxsVEFBge644w5t27ZNr732mjPFGoBwIqmjo0P19fU6evSoLl++rKqqKgWDQQ0MDDhdmqt0dXUpFArppZdeUiQS0bvvvquamhoNDw87XZqr9fT0qL29XRs2bHC6FFd65513tGXLFmVlZemf//mf1dfXp69//eu68847nS7Ndf74j/9Yzz//vFpbW/WjH/1Izz33nJqbm/Vnf/ZnTpdmvOHhYW3cuFGtra1Tvv7cc8/p5MmTam1tVU9Pj9asWaPt27fr1q1bS1ypISxYn/zkJ61Dhw5NWHffffdZhw8fdqii5WFoaMiSZHV1dTldimvdunXLuvfee61IJGI9/PDDVl1dndMluc5XvvIVa+vWrU6XsSw8+uij1he/+MUJ6x5//HHrc5/7nEMVuZMk69y5c6nlsbExa82aNdazzz6bWvfzn//cysnJsZ5//nkHKnSe58+cjI6O6tKlS6qpqZmwvqamRi+++KJDVS0PN2/elCTl5uY6XIl7hUIhPfroo/qVX/kVp0txrW9/+9sqLy/XZz/7Wd199936xCc+oT//8z93uixX2rp1q/7lX/5Fr7/+uiTphz/8ob7//e9rx44dDlfmbv39/bp27dqE7yG/36+HH37Ys99DrvvhP7tdv35dyWRS+fn5E9bn5+fr2rVrDlXlfpZlqaGhQVu3blVpaanT5bjSN7/5Tf3gBz9QT0+P06W42v/8z//o1KlTamho0O///u/r5Zdf1lNPPSW/368nnnjC6fJc5Stf+Ypu3ryp++67T5mZmUomk3rmmWf0G7/xG06X5mrj3zVTfQ/9+Mc/dqIkx3k+nIzz+XwTli3LmrQOc/fkk0/qP/7jP/T973/f6VJc6erVq6qrq9MLL7ygVatWOV2Oq42Njam8vFwnTpyQJH3iE5/Qa6+9plOnThFO0tTR0aG/+Zu/0ZkzZ7R+/XpFo1HV19eroKBA+/fvd7o81+N76P/zfDjJy8tTZmbmpLMkQ0NDk1Is5ua3f/u39e1vf1vd3d1au3at0+W40qVLlzQ0NKSysrLUumQyqe7ubrW2tioejyszM9PBCt3jnnvuUUlJyYR1999/v/7+7//eoYrc63d/93d1+PBh/fqv/7ok6YEHHtCPf/xjNTU1EU4WYM2aNZLeO4Nyzz33pNZ7+XvI83NOVq5cqbKyMkUikQnrI5GIKisrHarKnSzL0pNPPql/+Id/0L/+679q3bp1TpfkWr/8y7+sV155RdFoNPW/8vJy/eZv/qai0SjBJA1btmyZdEv766+/ruLiYocqcq+RkRFlZEz82sjMzORW4gVat26d1qxZM+F7aHR0VF1dXZ79HvL8mRNJamho0L59+1ReXq7Nmzervb1dAwMDOnTokNOluUooFNKZM2f0rW99S6tXr06djcrJydEdd9zhcHXusnr16klzdT70oQ/prrvuYg5Pmn7nd35HlZWVOnHihH7t135NL7/8strb29Xe3u50aa6zc+dOPfPMMyoqKtL69et1+fJlnTx5Ul/84hedLs14t2/f1pUrV1LL/f39ikajys3NVVFRkerr63XixAnde++9uvfee3XixAkFAgHt3bvXwaod5OzNQuYIh8NWcXGxtXLlSmvTpk3c/joPkqb831/+5V86XdqywK3E8/eP//iPVmlpqeX3+6377rvPam9vd7okV4rFYlZdXZ1VVFRkrVq1yvroRz9qHT161IrH406XZrzvfve7U/593L9/v2VZ791O/NWvftVas2aN5ff7rU996lPWK6+84mzRDvJZlmU5lIsAAAAm8fycEwAAYBbCCQAAMArhBAAAGIVwAgAAjEI4AQAARiGcAAAAoxBOAACAUQgnAADAKIQTAABgFMIJAAAwCuEEAAAYhXACAACM8n8/qs5GOQsBOQAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.hist(np.log1p(result_df['Predicted_LTV_baseline']), bins = 50)\n", "plt.yscale('log')\n", "plt.grid()"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["result_df['diff'] = abs(result_df['Predicted_LTV'] - result_df['Real_LTV'])"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>3day_LTV</th>\n", "      <th>Predicted_LTV_baseline</th>\n", "      <th>Real_LTV</th>\n", "      <th>Predicted_LTV</th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>attribution_day</th>\n", "      <th>diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>168311</th>\n", "      <td>21848.0</td>\n", "      <td>45006.88</td>\n", "      <td>55744.0</td>\n", "      <td>21856.006785</td>\n", "      <td>itpz+70TJ/5AWlUFVVbTJI0CLnQW4lr6</td>\n", "      <td>2025-01-31</td>\n", "      <td>33887.993215</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191581</th>\n", "      <td>9158.0</td>\n", "      <td>18865.48</td>\n", "      <td>41822.0</td>\n", "      <td>9166.974793</td>\n", "      <td>sLLfYTN8WD9BSgURVUbUOlYgmyvorG5a</td>\n", "      <td>2025-01-26</td>\n", "      <td>32655.025207</td>\n", "    </tr>\n", "    <tr>\n", "      <th>849720</th>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>22156.0</td>\n", "      <td>0.000000</td>\n", "      <td>V4n4hAJdx+REXwQVUBeEGiXKlzjrlCL2</td>\n", "      <td>2025-02-02</td>\n", "      <td>22156.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>630209</th>\n", "      <td>18908.0</td>\n", "      <td>38950.48</td>\n", "      <td>40678.0</td>\n", "      <td>18918.180257</td>\n", "      <td>NbjscA7LnfRFHEUFRAPHMbQJF1m9jrWy</td>\n", "      <td>2025-02-01</td>\n", "      <td>21759.819743</td>\n", "    </tr>\n", "    <tr>\n", "      <th>297117</th>\n", "      <td>1980.0</td>\n", "      <td>4078.80</td>\n", "      <td>22604.0</td>\n", "      <td>1980.000000</td>\n", "      <td>AkpKu6AY+mBACwEFBEODVOw31V95bcro</td>\n", "      <td>2025-01-24</td>\n", "      <td>20624.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        3day_LTV  Predicted_LTV_baseline  Real_LTV  Predicted_LTV  \\\n", "168311   21848.0                45006.88   55744.0   21856.006785   \n", "191581    9158.0                18865.48   41822.0    9166.974793   \n", "849720       0.0                    0.00   22156.0       0.000000   \n", "630209   18908.0                38950.48   40678.0   18918.180257   \n", "297117    1980.0                 4078.80   22604.0    1980.000000   \n", "\n", "                     sdk_yidun_device_id attribution_day          diff  \n", "168311  itpz+70TJ/5AWlUFVVbTJI0CLnQW4lr6      2025-01-31  33887.993215  \n", "191581  sLLfYTN8WD9BSgURVUbUOlYgmyvorG5a      2025-01-26  32655.025207  \n", "849720  V4n4hAJdx+REXwQVUBeEGiXKlzjrlCL2      2025-02-02  22156.000000  \n", "630209  NbjscA7LnfRFHEUFRAPHMbQJF1m9jrWy      2025-02-01  21759.819743  \n", "297117  AkpKu6AY+mBACwEFBEODVOw31V95bcro      2025-01-24  20624.000000  "]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["result_df.sort_values(by='diff', ascending=False).head(5)"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>3day_LTV</th>\n", "      <th>Predicted_LTV_baseline</th>\n", "      <th>Real_LTV</th>\n", "      <th>Predicted_LTV</th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>attribution_day</th>\n", "      <th>diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>564899</th>\n", "      <td>25290.0</td>\n", "      <td>52097.40</td>\n", "      <td>25290.0</td>\n", "      <td>25290.000000</td>\n", "      <td>rx0AeuGomRhAS0QREFaTI0xgbU/Vo/Gg</td>\n", "      <td>2025-02-21</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>168311</th>\n", "      <td>21848.0</td>\n", "      <td>45006.88</td>\n", "      <td>55744.0</td>\n", "      <td>21856.006785</td>\n", "      <td>itpz+70TJ/5AWlUFVVbTJI0CLnQW4lr6</td>\n", "      <td>2025-01-31</td>\n", "      <td>33887.993215</td>\n", "    </tr>\n", "    <tr>\n", "      <th>286470</th>\n", "      <td>20422.0</td>\n", "      <td>42069.32</td>\n", "      <td>27734.0</td>\n", "      <td>20428.544560</td>\n", "      <td>0Zgz3+ONHaVFSRERERKE5UomEEVB/x1U</td>\n", "      <td>2025-01-30</td>\n", "      <td>7305.455440</td>\n", "    </tr>\n", "    <tr>\n", "      <th>630209</th>\n", "      <td>18908.0</td>\n", "      <td>38950.48</td>\n", "      <td>40678.0</td>\n", "      <td>18918.180257</td>\n", "      <td>NbjscA7LnfRFHEUFRAPHMbQJF1m9jrWy</td>\n", "      <td>2025-02-01</td>\n", "      <td>21759.819743</td>\n", "    </tr>\n", "    <tr>\n", "      <th>886898</th>\n", "      <td>18174.0</td>\n", "      <td>37438.44</td>\n", "      <td>18174.0</td>\n", "      <td>18182.257073</td>\n", "      <td>NEAuaXcu8shFHhVQVFOCdzSBb8pIVb/e</td>\n", "      <td>2025-02-28</td>\n", "      <td>8.257073</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        3day_LTV  Predicted_LTV_baseline  Real_LTV  Predicted_LTV  \\\n", "564899   25290.0                52097.40   25290.0   25290.000000   \n", "168311   21848.0                45006.88   55744.0   21856.006785   \n", "286470   20422.0                42069.32   27734.0   20428.544560   \n", "630209   18908.0                38950.48   40678.0   18918.180257   \n", "886898   18174.0                37438.44   18174.0   18182.257073   \n", "\n", "                     sdk_yidun_device_id attribution_day          diff  \n", "564899  rx0AeuGomRhAS0QREFaTI0xgbU/Vo/Gg      2025-02-21      0.000000  \n", "168311  itpz+70TJ/5AWlUFVVbTJI0CLnQW4lr6      2025-01-31  33887.993215  \n", "286470  0Zgz3+ONHaVFSRERERKE5UomEEVB/x1U      2025-01-30   7305.455440  \n", "630209  NbjscA7LnfRFHEUFRAPHMbQJF1m9jrWy      2025-02-01  21759.819743  \n", "886898  NEAuaXcu8shFHhVQVFOCdzSBb8pIVb/e      2025-02-28      8.257073  "]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["result_df.sort_values(by='3day_LTV', ascending=False).head(5)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Metric</th>\n", "      <th>Model</th>\n", "      <th>Baseline</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MAE</td>\n", "      <td>11.671165</td>\n", "      <td>14.754733</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MSE</td>\n", "      <td>45060.617911</td>\n", "      <td>41692.473539</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>RMSE</td>\n", "      <td>212.274864</td>\n", "      <td>204.187349</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>R2</td>\n", "      <td>0.579858</td>\n", "      <td>0.611262</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Metric         Model      Baseline\n", "0    MAE     11.671165     14.754733\n", "1    MSE  45060.617911  41692.473539\n", "2   RMSE    212.274864    204.187349\n", "3     R2      0.579858      0.611262"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["model_mae_user = (result_df['Real_LTV'] - result_df['Predicted_LTV']).abs().mean()\n", "model_mse_user = ((result_df['Real_LTV'] - result_df['Predicted_LTV']) ** 2).mean()\n", "model_rmse_user = np.sqrt(model_mse_user)\n", "model_r2_user = r2_score(result_df['Real_LTV'], result_df['Predicted_LTV'])\n", "\n", "baseline_mae_user = (result_df['Real_LTV'] - result_df['Predicted_LTV_baseline']).abs().mean()\n", "baseline_mse_user = ((result_df['Real_LTV'] - result_df['Predicted_LTV_baseline']) ** 2).mean()\n", "baseline_rmse_user = np.sqrt(baseline_mse_user)\n", "baseline_r2_user = r2_score(result_df['Real_LTV'], result_df['Predicted_LTV_baseline'])\n", "\n", "df_user_evaluation = pd.DataFrame({\n", "    'Metric': ['MA<PERSON>', 'MSE', 'RMS<PERSON>', 'R2'],\n", "    'Model': [model_mae_user, model_mse_user, model_rmse_user, model_r2_user],\n", "    'Baseline': [baseline_mae_user, baseline_mse_user, baseline_rmse_user, baseline_r2_user]\n", "})\n", "df_user_evaluation\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["did_adid_df_raw = key_df[['channel_ty_adgroup_id', 'attribution_day', 'sdk_yidun_device_id']]"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_167655/3041686418.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  did_adid_df_raw['day'] = pd.to_datetime(did_adid_df_raw['attribution_day'])\n"]}], "source": ["did_adid_df_raw['day'] = pd.to_datetime(did_adid_df_raw['attribution_day'])"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>channel_ty_adgroup_id</th>\n", "      <th>attribution_day</th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>day</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>***********</td>\n", "      <td>2025-01-16</td>\n", "      <td>UNxCzF/X1hxBCkVQQRfAwIY53A/PxUmE</td>\n", "      <td>2025-01-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>***********</td>\n", "      <td>2025-03-25</td>\n", "      <td>aFHcRU2LyM1ESRAAVFPSYqTTSOu0PCAj</td>\n", "      <td>2025-03-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>unknown</td>\n", "      <td>2025-02-06</td>\n", "      <td>SoIkucJnEk1FH1UFQFOFTU03HiSi+oeU</td>\n", "      <td>2025-02-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>***********</td>\n", "      <td>2025-01-27</td>\n", "      <td>k191AGs/r2hESUAAUVLUoEthKuljS0tu</td>\n", "      <td>2025-01-27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>***********</td>\n", "      <td>2025-02-16</td>\n", "      <td>F6wnHZjgMn1AGVEBVFKDZ0HIOhcs0w2a</td>\n", "      <td>2025-02-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1283135</th>\n", "      <td>29102167881</td>\n", "      <td>2025-01-27</td>\n", "      <td>ws23/cmusplAGRFEFVPGIIAudO7cHp6s</td>\n", "      <td>2025-01-27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1283136</th>\n", "      <td>unknown</td>\n", "      <td>2025-02-27</td>\n", "      <td>x5hwmI3wWJBEGxEUBRbV3+gcL9fKX6lC</td>\n", "      <td>2025-02-27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1283137</th>\n", "      <td>1061988689626935560</td>\n", "      <td>2025-01-12</td>\n", "      <td>L8dnHia3ghpBGlEEEBfSWyujG8Osel28</td>\n", "      <td>2025-01-12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1283138</th>\n", "      <td>7431815633</td>\n", "      <td>2025-03-29</td>\n", "      <td>LbrQs3BPUUZAWAFBQFKDGlTrPKiO5b+C</td>\n", "      <td>2025-03-29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1283139</th>\n", "      <td>__AID__</td>\n", "      <td>2025-01-15</td>\n", "      <td>/xSfcuAYjJNBCFFVEFPCdCWZF/MOP7Q0</td>\n", "      <td>2025-01-15</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1283140 rows × 4 columns</p>\n", "</div>"], "text/plain": ["        channel_ty_adgroup_id attribution_day  \\\n", "0                 ***********      2025-01-16   \n", "1                 ***********      2025-03-25   \n", "2                     unknown      2025-02-06   \n", "3                 ***********      2025-01-27   \n", "4                 ***********      2025-02-16   \n", "...                       ...             ...   \n", "1283135           29102167881      2025-01-27   \n", "1283136               unknown      2025-02-27   \n", "1283137   1061988689626935560      2025-01-12   \n", "1283138            7431815633      2025-03-29   \n", "1283139               __AID__      2025-01-15   \n", "\n", "                      sdk_yidun_device_id        day  \n", "0        UNxCzF/X1hxBCkVQQRfAwIY53A/PxUmE 2025-01-16  \n", "1        aFHcRU2LyM1ESRAAVFPSYqTTSOu0PCAj 2025-03-25  \n", "2        SoIkucJnEk1FH1UFQFOFTU03HiSi+oeU 2025-02-06  \n", "3        k191AGs/r2hESUAAUVLUoEthKuljS0tu 2025-01-27  \n", "4        F6wnHZjgMn1AGVEBVFKDZ0HIOhcs0w2a 2025-02-16  \n", "...                                   ...        ...  \n", "1283135  ws23/cmusplAGRFEFVPGIIAudO7cHp6s 2025-01-27  \n", "1283136  x5hwmI3wWJBEGxEUBRbV3+gcL9fKX6lC 2025-02-27  \n", "1283137  L8dnHia3ghpBGlEEEBfSWyujG8Osel28 2025-01-12  \n", "1283138  LbrQs3BPUUZAWAFBQFKDGlTrPKiO5b+C 2025-03-29  \n", "1283139  /xSfcuAYjJNBCFFVEFPCdCWZF/MOP7Q0 2025-01-15  \n", "\n", "[1283140 rows x 4 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["did_adid_df_raw"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["did_adid_df_raw = did_adid_df_raw[did_adid_df_raw['channel_ty_adgroup_id'] != 'unknown']\n", "did_adid_df_raw = did_adid_df_raw[did_adid_df_raw['channel_ty_adgroup_id'] != '__AID__']"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>channel_ty_adgroup_id</th>\n", "      <th>attribution_day</th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>day</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>***********</td>\n", "      <td>2025-01-16</td>\n", "      <td>UNxCzF/X1hxBCkVQQRfAwIY53A/PxUmE</td>\n", "      <td>2025-01-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>***********</td>\n", "      <td>2025-03-25</td>\n", "      <td>aFHcRU2LyM1ESRAAVFPSYqTTSOu0PCAj</td>\n", "      <td>2025-03-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>***********</td>\n", "      <td>2025-01-27</td>\n", "      <td>k191AGs/r2hESUAAUVLUoEthKuljS0tu</td>\n", "      <td>2025-01-27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>***********</td>\n", "      <td>2025-02-16</td>\n", "      <td>F6wnHZjgMn1AGVEBVFKDZ0HIOhcs0w2a</td>\n", "      <td>2025-02-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>29092911993</td>\n", "      <td>2025-03-30</td>\n", "      <td>********************************</td>\n", "      <td>2025-03-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1283133</th>\n", "      <td>26525665291</td>\n", "      <td>2025-02-02</td>\n", "      <td>2JHJn8KjGaRACwBBRFPAK0+mHaa2L/+r</td>\n", "      <td>2025-02-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1283134</th>\n", "      <td>21542648727</td>\n", "      <td>2025-03-22</td>\n", "      <td>XWKf6eR2AFJATFEFBAaQWSeQ8yvh0wc2</td>\n", "      <td>2025-03-22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1283135</th>\n", "      <td>29102167881</td>\n", "      <td>2025-01-27</td>\n", "      <td>ws23/cmusplAGRFEFVPGIIAudO7cHp6s</td>\n", "      <td>2025-01-27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1283137</th>\n", "      <td>1061988689626935560</td>\n", "      <td>2025-01-12</td>\n", "      <td>L8dnHia3ghpBGlEEEBfSWyujG8Osel28</td>\n", "      <td>2025-01-12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1283138</th>\n", "      <td>7431815633</td>\n", "      <td>2025-03-29</td>\n", "      <td>LbrQs3BPUUZAWAFBQFKDGlTrPKiO5b+C</td>\n", "      <td>2025-03-29</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>925896 rows × 4 columns</p>\n", "</div>"], "text/plain": ["        channel_ty_adgroup_id attribution_day  \\\n", "0                 ***********      2025-01-16   \n", "1                 ***********      2025-03-25   \n", "3                 ***********      2025-01-27   \n", "4                 ***********      2025-02-16   \n", "6                 29092911993      2025-03-30   \n", "...                       ...             ...   \n", "1283133           26525665291      2025-02-02   \n", "1283134           21542648727      2025-03-22   \n", "1283135           29102167881      2025-01-27   \n", "1283137   1061988689626935560      2025-01-12   \n", "1283138            7431815633      2025-03-29   \n", "\n", "                      sdk_yidun_device_id        day  \n", "0        UNxCzF/X1hxBCkVQQRfAwIY53A/PxUmE 2025-01-16  \n", "1        aFHcRU2LyM1ESRAAVFPSYqTTSOu0PCAj 2025-03-25  \n", "3        k191AGs/r2hESUAAUVLUoEthKuljS0tu 2025-01-27  \n", "4        F6wnHZjgMn1AGVEBVFKDZ0HIOhcs0w2a 2025-02-16  \n", "6        ******************************** 2025-03-30  \n", "...                                   ...        ...  \n", "1283133  2JHJn8KjGaRACwBBRFPAK0+mHaa2L/+r 2025-02-02  \n", "1283134  XWKf6eR2AFJATFEFBAaQWSeQ8yvh0wc2 2025-03-22  \n", "1283135  ws23/cmusplAGRFEFVPGIIAudO7cHp6s 2025-01-27  \n", "1283137  L8dnHia3ghpBGlEEEBfSWyujG8Osel28 2025-01-12  \n", "1283138  LbrQs3BPUUZAWAFBQFKDGlTrPKiO5b+C 2025-03-29  \n", "\n", "[925896 rows x 4 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["did_adid_df_raw"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# did_adid_df_cleaned = did_adid_df_raw.dropna(subset=['channel_ty_adgroup_id', 'sdk_yidun_device_id'])\n", "# result_did_adid_dict = did_adid_df_cleaned.groupby(\n", "#     did_adid_df_cleaned[['channel_ty_adgroup_id', 'day']].assign(day=pd.to_datetime(did_adid_df_cleaned['day']).dt.strftime('%Y-%m-%d %H:%M:%S'))  # 转换为日期时间字符串\n", "#     .apply(tuple, axis=1)\n", "# )['sdk_yidun_device_id'].apply(list).to_dict()\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["df_merged = did_adid_df_raw.merge(result_df, on=['sdk_yidun_device_id', 'attribution_day'], how='left')\n", "df_campaign = df_merged.groupby(['channel_ty_adgroup_id', 'attribution_day'])[['Predicted_LTV', 'Real_LTV', '3day_LTV', 'Predicted_LTV_baseline']].sum().reset_index()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>channel_ty_adgroup_id</th>\n", "      <th>attribution_day</th>\n", "      <th>Predicted_LTV</th>\n", "      <th>Real_LTV</th>\n", "      <th>3day_LTV</th>\n", "      <th>Predicted_LTV_baseline</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>2025-01-15</td>\n", "      <td>24.0</td>\n", "      <td>24.0</td>\n", "      <td>24.0</td>\n", "      <td>49.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>2025-01-24</td>\n", "      <td>18.0</td>\n", "      <td>18.0</td>\n", "      <td>18.0</td>\n", "      <td>37.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>2025-01-25</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>12.36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.0177882485854136e+18</td>\n", "      <td>2025-01-07</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.0287097605231584e+18</td>\n", "      <td>2025-01-21</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    channel_ty_adgroup_id attribution_day  Predicted_LTV  Real_LTV  3day_LTV  \\\n", "0                       0      2025-01-15           24.0      24.0      24.0   \n", "1                       0      2025-01-24           18.0      18.0      18.0   \n", "2                       0      2025-01-25            6.0       6.0       6.0   \n", "3  1.0177882485854136e+18      2025-01-07            0.0       0.0       0.0   \n", "4  1.0287097605231584e+18      2025-01-21            0.0       0.0       0.0   \n", "\n", "   Predicted_LTV_baseline  \n", "0                   49.44  \n", "1                   37.08  \n", "2                   12.36  \n", "3                    0.00  \n", "4                    0.00  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df_campaign.head()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["def compare_columns(row):\n", "    diff1 = abs(row['Predicted_LTV'] - row['Real_LTV'])\n", "    diff2 = abs(row['Predicted_LTV_baseline'] - row['Real_LTV'])\n", "    \n", "    if diff1 < diff2:\n", "        return 'Better'\n", "    elif diff1 > diff2:\n", "        return 'Worse'\n", "    else:\n", "        return 'Equal'\n", "\n", "df_campaign['Comparison'] = df_campaign.apply(compare_columns, axis=1)\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["             Count  Per<PERSON><PERSON>\n", "Comparison                    \n", "Equal       111577   75.461247\n", "Better       24427   16.520357\n", "Worse        11856    8.018396\n"]}], "source": ["value_counts = df_campaign['Comparison'].value_counts()  # 获取值和它们的计数\n", "percentage = df_campaign['Comparison'].value_counts(normalize=True) * 100  # 计算占比（以百分比表示）\n", "\n", "result = pd.DataFrame({\n", "    'Count': value_counts,\n", "    'Percentage': percentage\n", "})\n", "\n", "print(result)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Metric</th>\n", "      <th>Model</th>\n", "      <th>Baseline</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MAE</td>\n", "      <td>70.521731</td>\n", "      <td>76.989315</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MSE</td>\n", "      <td>426930.957333</td>\n", "      <td>249541.131702</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>RMSE</td>\n", "      <td>653.399539</td>\n", "      <td>499.540921</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>R2</td>\n", "      <td>0.475753</td>\n", "      <td>0.693578</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Metric          Model       Baseline\n", "0    MAE      70.521731      76.989315\n", "1    MSE  426930.957333  249541.131702\n", "2   RMSE     653.399539     499.540921\n", "3     R2       0.475753       0.693578"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["model_mae_campaign = (df_campaign['Real_LTV'] - df_campaign['Predicted_LTV']).abs().mean()\n", "model_mse_campaign = ((df_campaign['Real_LTV'] - df_campaign['Predicted_LTV']) ** 2).mean()\n", "model_rmse_campaign = np.sqrt(model_mse_campaign)\n", "model_r2_campaign = r2_score(df_campaign['Real_LTV'], df_campaign['Predicted_LTV'])\n", "\n", "baseline_mae_campaign = (df_campaign['Real_LTV'] - df_campaign['Predicted_LTV_baseline']).abs().mean()\n", "baseline_mse_campaign = ((df_campaign['Real_LTV'] - df_campaign['Predicted_LTV_baseline']) ** 2).mean()\n", "baseline_rmse_campaign = np.sqrt(baseline_mse_campaign)\n", "baseline_r2_campaign = r2_score(df_campaign['Real_LTV'], df_campaign['Predicted_LTV_baseline'])\n", "\n", "df_campaign_evaluation = pd.DataFrame({\n", "    'Metric': ['MA<PERSON>', 'MSE', 'RMS<PERSON>', 'R2'],\n", "    'Model': [model_mae_campaign, model_mse_campaign, model_rmse_campaign, model_r2_campaign],\n", "    'Baseline': [baseline_mae_campaign, baseline_mse_campaign, baseline_rmse_campaign, baseline_r2_campaign]\n", "})\n", "df_campaign_evaluation"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x7f4d7971c590>"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist(df_campaign[df_campaign['Comparison'] == 'Worse']['3day_LTV'], bins = 50, range = (0, 20000), label = 'Worse', alpha = 0.5)\n", "plt.hist(df_campaign[df_campaign['Comparison'] == 'Better']['3day_LTV'], bins = 50, range = (0, 20000), label = 'Better', alpha = 0.5)\n", "plt.yscale('log')\n", "plt.legend()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x7f4d7849c590>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df_campaign['base_line_error'] = abs(df_campaign['Predicted_LTV_baseline'] - df_campaign['Real_LTV'])\n", "df_campaign['prediction_error'] = abs(df_campaign['Predicted_LTV'] - df_campaign['Real_LTV'])\n", "\n", "df_campaign['cumulative_sum_baseline'] = df_campaign['base_line_error'].cumsum()\n", "df_campaign['cumulative_sum_prediction'] = df_campaign['prediction_error'].cumsum()\n", "\n", "df_campaign['cumulative_average_baseline'] = df_campaign['cumulative_sum_baseline'] / (df_campaign.index + 1)\n", "df_campaign['cumulative_average_prediction'] = df_campaign['cumulative_sum_prediction'] / (df_campaign.index + 1)\n", "\n", "plt.plot(range(len(df_campaign)), df_campaign['cumulative_sum_baseline'], c = 'r', label = 'baseline')\n", "plt.plot(range(len(df_campaign)), df_campaign['cumulative_sum_prediction'], c = 'b', label = 'prediction')\n", "plt.xlabel('Number of Ads Considered (N)')\n", "plt.ylabel('Cumulative Prediction Error for the First N Ads')\n", "plt.title('Cumulative Prediction Error Over N Ads Sorted by # of New Players')\n", "plt.legend()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["1176601"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["len(result_df[result_df['Real_LTV'] == 0])"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["1283140"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["len(result_df)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sdk_yidun_device_id</th>\n", "      <th>attribution_day</th>\n", "      <th>channel_ty_campaign_id</th>\n", "      <th>channel_ty_csite_id</th>\n", "      <th>channel_ty_adgroup_id</th>\n", "      <th>channel_ty_account_id</th>\n", "      <th>proj_main_channel</th>\n", "      <th>sdk_proj_app_id</th>\n", "      <th>adtrace_attribution_mode</th>\n", "      <th>sdk_virtual_channel</th>\n", "      <th>proj_cloud_id</th>\n", "      <th>sdk_os_version</th>\n", "      <th>proj_project_id</th>\n", "      <th>adtrace_platform</th>\n", "      <th>sdk_hardware_name</th>\n", "      <th>sdk_virtual_channel_version</th>\n", "      <th>sdk_systemversion</th>\n", "      <th>sdk_split_ua_result</th>\n", "      <th>adtrace_assist_platform</th>\n", "      <th>sdk_carrier_name</th>\n", "      <th>sdk_memory</th>\n", "      <th>sdk_device_name</th>\n", "      <th>channel_ty_video_id</th>\n", "      <th>sdk_timezone</th>\n", "      <th>adtrace_is_greylist</th>\n", "      <th>proj_game_type</th>\n", "      <th>proj_virtual_channel</th>\n", "      <th>adtrace_click_match_count</th>\n", "      <th>sdk_machine</th>\n", "      <th>adtrace_main_platform</th>\n", "      <th>sdk_package_name</th>\n", "      <th>proj_client_id</th>\n", "      <th>sdk_countrycode</th>\n", "      <th>adtrace_pay_times</th>\n", "      <th>sdk_language</th>\n", "      <th>adtrace_namespace</th>\n", "      <th>channel_ty_creative_id</th>\n", "      <th>sdk_disk</th>\n", "      <th>adtrace_reattributed</th>\n", "      <th>sdk_carrierinfo</th>\n", "      <th>adtrace_device_caid_main_version</th>\n", "      <th>proj_sub_channel</th>\n", "      <th>adtrace_ctit</th>\n", "      <th>sdk_os_name</th>\n", "      <th>adtrace_orderid</th>\n", "      <th>adtrace_organic_traffic</th>\n", "      <th>sdk_model</th>\n", "      <th>adtrace_yidun_validate</th>\n", "      <th>sdk_mntid</th>\n", "      <th>adtrace_is_blacklist</th>\n", "      <th>channel_ua</th>\n", "      <th>sdk_devicename</th>\n", "      <th>attribution_day_a</th>\n", "      <th>d1_payment</th>\n", "      <th>d2_payment</th>\n", "      <th>d3_payment</th>\n", "      <th>device_name</th>\n", "      <th>device_price</th>\n", "      <th>behav.fish_gun_fire_sum_count_0</th>\n", "      <th>behav.gun_level_up_consume_count_0</th>\n", "      <th>behav.recharge_count_0</th>\n", "      <th>behav.fish_table_enter_count_0</th>\n", "      <th>behav.skill_use_count_0</th>\n", "      <th>behav.bkrpt_count_0</th>\n", "      <th>behav.login_count_0</th>\n", "      <th>behav.shop_center_enter_count_0</th>\n", "      <th>behav.achievement_reward_count_0</th>\n", "      <th>behav.have_checkin_reward_0</th>\n", "      <th>behav.startup_quest_finish_game_count_0</th>\n", "      <th>behav.click_120215_count_0</th>\n", "      <th>behav.click_120214_count_0</th>\n", "      <th>behav.click_120093_count_0</th>\n", "      <th>behav.click_120092_count_0</th>\n", "      <th>behav.resource_total_down_count_0</th>\n", "      <th>behav.resource_down_count_0</th>\n", "      <th>behav.activity_midnight_0</th>\n", "      <th>behav.activity_morning_0</th>\n", "      <th>behav.activity_afternoon_0</th>\n", "      <th>behav.activity_night_0</th>\n", "      <th>behav.game_time_in_minutes_0</th>\n", "      <th>behav.final_delta_0</th>\n", "      <th>behav.max_delta_0</th>\n", "      <th>behav.min_delta_0</th>\n", "      <th>behav.max_gunlevel_0</th>\n", "      <th>behav.min_gunlevel_0</th>\n", "      <th>behav.max_boss_rate_0</th>\n", "      <th>behav.total_catch_0</th>\n", "      <th>behav.total_catch_boss_0</th>\n", "      <th>behav.fish_gun_fire_sum_count_1</th>\n", "      <th>behav.gun_level_up_consume_count_1</th>\n", "      <th>behav.recharge_count_1</th>\n", "      <th>behav.fish_table_enter_count_1</th>\n", "      <th>behav.skill_use_count_1</th>\n", "      <th>behav.bkrpt_count_1</th>\n", "      <th>behav.login_count_1</th>\n", "      <th>behav.shop_center_enter_count_1</th>\n", "      <th>behav.achievement_reward_count_1</th>\n", "      <th>behav.have_checkin_reward_1</th>\n", "      <th>behav.startup_quest_finish_game_count_1</th>\n", "      <th>behav.click_120215_count_1</th>\n", "      <th>behav.click_120214_count_1</th>\n", "      <th>behav.click_120093_count_1</th>\n", "      <th>behav.click_120092_count_1</th>\n", "      <th>behav.resource_total_down_count_1</th>\n", "      <th>behav.resource_down_count_1</th>\n", "      <th>behav.activity_midnight_1</th>\n", "      <th>behav.activity_morning_1</th>\n", "      <th>behav.activity_afternoon_1</th>\n", "      <th>behav.activity_night_1</th>\n", "      <th>behav.game_time_in_minutes_1</th>\n", "      <th>behav.final_delta_1</th>\n", "      <th>behav.max_delta_1</th>\n", "      <th>behav.min_delta_1</th>\n", "      <th>behav.max_gunlevel_1</th>\n", "      <th>behav.min_gunlevel_1</th>\n", "      <th>behav.max_boss_rate_1</th>\n", "      <th>behav.total_catch_1</th>\n", "      <th>behav.total_catch_boss_1</th>\n", "      <th>behav.fish_gun_fire_sum_count_2</th>\n", "      <th>behav.gun_level_up_consume_count_2</th>\n", "      <th>behav.recharge_count_2</th>\n", "      <th>behav.fish_table_enter_count_2</th>\n", "      <th>behav.skill_use_count_2</th>\n", "      <th>behav.bkrpt_count_2</th>\n", "      <th>behav.login_count_2</th>\n", "      <th>behav.shop_center_enter_count_2</th>\n", "      <th>behav.achievement_reward_count_2</th>\n", "      <th>behav.have_checkin_reward_2</th>\n", "      <th>behav.startup_quest_finish_game_count_2</th>\n", "      <th>behav.click_120215_count_2</th>\n", "      <th>behav.click_120214_count_2</th>\n", "      <th>behav.click_120093_count_2</th>\n", "      <th>behav.click_120092_count_2</th>\n", "      <th>behav.resource_total_down_count_2</th>\n", "      <th>behav.resource_down_count_2</th>\n", "      <th>behav.activity_midnight_2</th>\n", "      <th>behav.activity_morning_2</th>\n", "      <th>behav.activity_afternoon_2</th>\n", "      <th>behav.activity_night_2</th>\n", "      <th>behav.game_time_in_minutes_2</th>\n", "      <th>behav.final_delta_2</th>\n", "      <th>behav.max_delta_2</th>\n", "      <th>behav.min_delta_2</th>\n", "      <th>behav.max_gunlevel_2</th>\n", "      <th>behav.min_gunlevel_2</th>\n", "      <th>behav.max_boss_rate_2</th>\n", "      <th>behav.total_catch_2</th>\n", "      <th>behav.total_catch_boss_2</th>\n", "      <th>target</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>437330</th>\n", "      <td>cwCaRZO/lgFATURREFPBoPJhcRTfw53G</td>\n", "      <td>2025-01-03</td>\n", "      <td>unknown</td>\n", "      <td>SITE_SET_MOBILE_UNION</td>\n", "      <td>***********</td>\n", "      <td>********</td>\n", "      <td>qqqm</td>\n", "      <td>10010.0</td>\n", "      <td>oaid</td>\n", "      <td>gdt</td>\n", "      <td>3</td>\n", "      <td>14</td>\n", "      <td>10010</td>\n", "      <td>gdt_v2</td>\n", "      <td>mt6895</td>\n", "      <td>1.0.0</td>\n", "      <td>unknown</td>\n", "      <td>14_PGX110</td>\n", "      <td>gdt_v2</td>\n", "      <td>chinaMobile</td>\n", "      <td>-1.0</td>\n", "      <td>PGX110</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "      <td>gdt</td>\n", "      <td>4410.0</td>\n", "      <td>unknown</td>\n", "      <td>gdt</td>\n", "      <td>com.tencent.tmgp.tuyoo.fishqm</td>\n", "      <td>Android_5.508_tyGuest,tyAccount,yidunlogin.wei...</td>\n", "      <td>unknown</td>\n", "      <td>15.0</td>\n", "      <td>unknown</td>\n", "      <td>3dbuyu10010</td>\n", "      <td>***********</td>\n", "      <td>-1.0</td>\n", "      <td>1</td>\n", "      <td>unknown</td>\n", "      <td>-1.0</td>\n", "      <td>bydzz</td>\n", "      <td>1642203.0</td>\n", "      <td>android</td>\n", "      <td>-1.0</td>\n", "      <td>0</td>\n", "      <td>unknown</td>\n", "      <td>-1.0</td>\n", "      <td>unknown</td>\n", "      <td>0.0</td>\n", "      <td>Mozilla/5.0 (Linux; Android 14; PGX110 Build/S...</td>\n", "      <td>unknown</td>\n", "      <td>2025-01-03</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>PGX110</td>\n", "      <td>700.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1249649</th>\n", "      <td>cwCaRZO/lgFATURREFPBoPJhcRTfw53G</td>\n", "      <td>2025-03-04</td>\n", "      <td>unknown</td>\n", "      <td>SITE_SET_MOMENTS</td>\n", "      <td>***********</td>\n", "      <td>********</td>\n", "      <td>qqqm</td>\n", "      <td>10010.0</td>\n", "      <td>oaid</td>\n", "      <td>gdt</td>\n", "      <td>3</td>\n", "      <td>14</td>\n", "      <td>10010</td>\n", "      <td>gdt_v2</td>\n", "      <td>mt6895</td>\n", "      <td>1.0.0</td>\n", "      <td>unknown</td>\n", "      <td>14_PGX110</td>\n", "      <td>gdt_v2</td>\n", "      <td>chinaMobile</td>\n", "      <td>-1.0</td>\n", "      <td>PGX110</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "      <td>gdt</td>\n", "      <td>14.0</td>\n", "      <td>unknown</td>\n", "      <td>gdt</td>\n", "      <td>com.tencent.tmgp.tuyoo.fishqm</td>\n", "      <td>Android_5.508_tyGuest,tyAccount,yidunlogin.wei...</td>\n", "      <td>unknown</td>\n", "      <td>15.0</td>\n", "      <td>unknown</td>\n", "      <td>3dbuyu10010</td>\n", "      <td>***********</td>\n", "      <td>-1.0</td>\n", "      <td>1</td>\n", "      <td>unknown</td>\n", "      <td>-1.0</td>\n", "      <td>bydzz</td>\n", "      <td>1590107.0</td>\n", "      <td>android</td>\n", "      <td>-1.0</td>\n", "      <td>0</td>\n", "      <td>unknown</td>\n", "      <td>-1.0</td>\n", "      <td>unknown</td>\n", "      <td>0.0</td>\n", "      <td>Mozilla/5.0 (Linux; Android 14; PGX110 Build/S...</td>\n", "      <td>unknown</td>\n", "      <td>2025-03-04</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>PGX110</td>\n", "      <td>700.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      sdk_yidun_device_id attribution_day  \\\n", "437330   cwCaRZO/lgFATURREFPBoPJhcRTfw53G      2025-01-03   \n", "1249649  cwCaRZO/lgFATURREFPBoPJhcRTfw53G      2025-03-04   \n", "\n", "        channel_ty_campaign_id    channel_ty_csite_id channel_ty_adgroup_id  \\\n", "437330                 unknown  SITE_SET_MOBILE_UNION           ***********   \n", "1249649                unknown       SITE_SET_MOMENTS           ***********   \n", "\n", "        channel_ty_account_id proj_main_channel  sdk_proj_app_id  \\\n", "437330               ********              qqqm          10010.0   \n", "1249649              ********              qqqm          10010.0   \n", "\n", "        adtrace_attribution_mode sdk_virtual_channel  proj_cloud_id  \\\n", "437330                      oaid                 gdt              3   \n", "1249649                     oaid                 gdt              3   \n", "\n", "        sdk_os_version proj_project_id adtrace_platform sdk_hardware_name  \\\n", "437330              14           10010           gdt_v2            mt6895   \n", "1249649             14           10010           gdt_v2            mt6895   \n", "\n", "        sdk_virtual_channel_version sdk_systemversion sdk_split_ua_result  \\\n", "437330                        1.0.0           unknown           14_PGX110   \n", "1249649                       1.0.0           unknown           14_PGX110   \n", "\n", "        adtrace_assist_platform sdk_carrier_name  sdk_memory sdk_device_name  \\\n", "437330                   gdt_v2      chinaMobile        -1.0          PGX110   \n", "1249649                  gdt_v2      chinaMobile        -1.0          PGX110   \n", "\n", "         channel_ty_video_id  sdk_timezone  adtrace_is_greylist  \\\n", "437330                  -1.0          -1.0                  0.0   \n", "1249649                 -1.0          -1.0                  0.0   \n", "\n", "         proj_game_type proj_virtual_channel  adtrace_click_match_count  \\\n", "437330                2                  gdt                     4410.0   \n", "1249649               2                  gdt                       14.0   \n", "\n", "        sdk_machine adtrace_main_platform               sdk_package_name  \\\n", "437330      unknown                   gdt  com.tencent.tmgp.tuyoo.fishqm   \n", "1249649     unknown                   gdt  com.tencent.tmgp.tuyoo.fishqm   \n", "\n", "                                            proj_client_id sdk_countrycode  \\\n", "437330   Android_5.508_tyG<PERSON>,tyAccount,yidunlogin.wei...         unknown   \n", "1249649  Android_5.508_tyG<PERSON>,tyAccount,yidunlogin.wei...         unknown   \n", "\n", "         adtrace_pay_times sdk_language adtrace_namespace  \\\n", "437330                15.0      unknown       3dbuyu10010   \n", "1249649               15.0      unknown       3dbuyu10010   \n", "\n", "        channel_ty_creative_id  sdk_disk  adtrace_reattributed  \\\n", "437330             ***********      -1.0                     1   \n", "1249649            ***********      -1.0                     1   \n", "\n", "        sdk_carrierinfo  adtrace_device_caid_main_version proj_sub_channel  \\\n", "437330          unknown                              -1.0            bydzz   \n", "1249649         unknown                              -1.0            bydzz   \n", "\n", "         adtrace_ctit sdk_os_name  adtrace_orderid  adtrace_organic_traffic  \\\n", "437330      1642203.0     android             -1.0                        0   \n", "1249649     1590107.0     android             -1.0                        0   \n", "\n", "        sdk_model  adtrace_yidun_validate sdk_mntid  adtrace_is_blacklist  \\\n", "437330    unknown                    -1.0   unknown                   0.0   \n", "1249649   unknown                    -1.0   unknown                   0.0   \n", "\n", "                                                channel_ua sdk_devicename  \\\n", "437330   Mozilla/5.0 (Linux; Android 14; PGX110 Build/S...        unknown   \n", "1249649  Mozilla/5.0 (Linux; Android 14; PGX110 Build/S...        unknown   \n", "\n", "        attribution_day_a  d1_payment  d2_payment  d3_payment device_name  \\\n", "437330         2025-01-03         0.0         0.0         0.0      PGX110   \n", "1249649        2025-03-04         0.0         0.0         0.0      PGX110   \n", "\n", "         device_price  behav.fish_gun_fire_sum_count_0  \\\n", "437330          700.0                             -1.0   \n", "1249649         700.0                             -1.0   \n", "\n", "         behav.gun_level_up_consume_count_0  behav.recharge_count_0  \\\n", "437330                                 -1.0                    -1.0   \n", "1249649                                -1.0                    -1.0   \n", "\n", "         behav.fish_table_enter_count_0  behav.skill_use_count_0  \\\n", "437330                             -1.0                     -1.0   \n", "1249649                            -1.0                     -1.0   \n", "\n", "         behav.bkrpt_count_0  behav.login_count_0  \\\n", "437330                  -1.0                 -1.0   \n", "1249649                 -1.0                 -1.0   \n", "\n", "         behav.shop_center_enter_count_0  behav.achievement_reward_count_0  \\\n", "437330                              -1.0                              -1.0   \n", "1249649                             -1.0                              -1.0   \n", "\n", "         behav.have_checkin_reward_0  behav.startup_quest_finish_game_count_0  \\\n", "437330                          -1.0                                     -1.0   \n", "1249649                         -1.0                                     -1.0   \n", "\n", "         behav.click_120215_count_0  behav.click_120214_count_0  \\\n", "437330                         -1.0                        -1.0   \n", "1249649                        -1.0                        -1.0   \n", "\n", "         behav.click_120093_count_0  behav.click_120092_count_0  \\\n", "437330                         -1.0                        -1.0   \n", "1249649                        -1.0                        -1.0   \n", "\n", "         behav.resource_total_down_count_0  behav.resource_down_count_0  \\\n", "437330                                -1.0                         -1.0   \n", "1249649                               -1.0                         -1.0   \n", "\n", "         behav.activity_midnight_0  behav.activity_morning_0  \\\n", "437330                        -1.0                      -1.0   \n", "1249649                       -1.0                      -1.0   \n", "\n", "         behav.activity_afternoon_0  behav.activity_night_0  \\\n", "437330                         -1.0                    -1.0   \n", "1249649                        -1.0                    -1.0   \n", "\n", "         behav.game_time_in_minutes_0  behav.final_delta_0  behav.max_delta_0  \\\n", "437330                           -1.0                 -1.0               -1.0   \n", "1249649                          -1.0                 -1.0               -1.0   \n", "\n", "         behav.min_delta_0  behav.max_gunlevel_0  behav.min_gunlevel_0  \\\n", "437330                -1.0                  -1.0                  -1.0   \n", "1249649               -1.0                  -1.0                  -1.0   \n", "\n", "         behav.max_boss_rate_0  behav.total_catch_0  behav.total_catch_boss_0  \\\n", "437330                    -1.0                 -1.0                      -1.0   \n", "1249649                   -1.0                 -1.0                      -1.0   \n", "\n", "         behav.fish_gun_fire_sum_count_1  behav.gun_level_up_consume_count_1  \\\n", "437330                              -1.0                                -1.0   \n", "1249649                             -1.0                                -1.0   \n", "\n", "         behav.recharge_count_1  behav.fish_table_enter_count_1  \\\n", "437330                     -1.0                            -1.0   \n", "1249649                    -1.0                            -1.0   \n", "\n", "         behav.skill_use_count_1  behav.bkrpt_count_1  behav.login_count_1  \\\n", "437330                      -1.0                 -1.0                 -1.0   \n", "1249649                     -1.0                 -1.0                 -1.0   \n", "\n", "         behav.shop_center_enter_count_1  behav.achievement_reward_count_1  \\\n", "437330                              -1.0                              -1.0   \n", "1249649                             -1.0                              -1.0   \n", "\n", "         behav.have_checkin_reward_1  behav.startup_quest_finish_game_count_1  \\\n", "437330                          -1.0                                     -1.0   \n", "1249649                         -1.0                                     -1.0   \n", "\n", "         behav.click_120215_count_1  behav.click_120214_count_1  \\\n", "437330                         -1.0                        -1.0   \n", "1249649                        -1.0                        -1.0   \n", "\n", "         behav.click_120093_count_1  behav.click_120092_count_1  \\\n", "437330                         -1.0                        -1.0   \n", "1249649                        -1.0                        -1.0   \n", "\n", "         behav.resource_total_down_count_1  behav.resource_down_count_1  \\\n", "437330                                -1.0                         -1.0   \n", "1249649                               -1.0                         -1.0   \n", "\n", "         behav.activity_midnight_1  behav.activity_morning_1  \\\n", "437330                        -1.0                      -1.0   \n", "1249649                       -1.0                      -1.0   \n", "\n", "         behav.activity_afternoon_1  behav.activity_night_1  \\\n", "437330                         -1.0                    -1.0   \n", "1249649                        -1.0                    -1.0   \n", "\n", "         behav.game_time_in_minutes_1  behav.final_delta_1  behav.max_delta_1  \\\n", "437330                           -1.0                 -1.0               -1.0   \n", "1249649                          -1.0                 -1.0               -1.0   \n", "\n", "         behav.min_delta_1  behav.max_gunlevel_1  behav.min_gunlevel_1  \\\n", "437330                -1.0                  -1.0                  -1.0   \n", "1249649               -1.0                  -1.0                  -1.0   \n", "\n", "         behav.max_boss_rate_1  behav.total_catch_1  behav.total_catch_boss_1  \\\n", "437330                    -1.0                 -1.0                      -1.0   \n", "1249649                   -1.0                 -1.0                      -1.0   \n", "\n", "         behav.fish_gun_fire_sum_count_2  behav.gun_level_up_consume_count_2  \\\n", "437330                              -1.0                                -1.0   \n", "1249649                             -1.0                                -1.0   \n", "\n", "         behav.recharge_count_2  behav.fish_table_enter_count_2  \\\n", "437330                     -1.0                            -1.0   \n", "1249649                    -1.0                            -1.0   \n", "\n", "         behav.skill_use_count_2  behav.bkrpt_count_2  behav.login_count_2  \\\n", "437330                      -1.0                 -1.0                 -1.0   \n", "1249649                     -1.0                 -1.0                 -1.0   \n", "\n", "         behav.shop_center_enter_count_2  behav.achievement_reward_count_2  \\\n", "437330                              -1.0                              -1.0   \n", "1249649                             -1.0                              -1.0   \n", "\n", "         behav.have_checkin_reward_2  behav.startup_quest_finish_game_count_2  \\\n", "437330                          -1.0                                     -1.0   \n", "1249649                         -1.0                                     -1.0   \n", "\n", "         behav.click_120215_count_2  behav.click_120214_count_2  \\\n", "437330                         -1.0                        -1.0   \n", "1249649                        -1.0                        -1.0   \n", "\n", "         behav.click_120093_count_2  behav.click_120092_count_2  \\\n", "437330                         -1.0                        -1.0   \n", "1249649                        -1.0                        -1.0   \n", "\n", "         behav.resource_total_down_count_2  behav.resource_down_count_2  \\\n", "437330                                -1.0                         -1.0   \n", "1249649                               -1.0                         -1.0   \n", "\n", "         behav.activity_midnight_2  behav.activity_morning_2  \\\n", "437330                        -1.0                      -1.0   \n", "1249649                       -1.0                      -1.0   \n", "\n", "         behav.activity_afternoon_2  behav.activity_night_2  \\\n", "437330                         -1.0                    -1.0   \n", "1249649                        -1.0                    -1.0   \n", "\n", "         behav.game_time_in_minutes_2  behav.final_delta_2  behav.max_delta_2  \\\n", "437330                           -1.0                 -1.0               -1.0   \n", "1249649                          -1.0                 -1.0               -1.0   \n", "\n", "         behav.min_delta_2  behav.max_gunlevel_2  behav.min_gunlevel_2  \\\n", "437330                -1.0                  -1.0                  -1.0   \n", "1249649               -1.0                  -1.0                  -1.0   \n", "\n", "         behav.max_boss_rate_2  behav.total_catch_2  behav.total_catch_boss_2  \\\n", "437330                    -1.0                 -1.0                      -1.0   \n", "1249649                   -1.0                 -1.0                      -1.0   \n", "\n", "         target  \n", "437330      0.0  \n", "1249649     0.0  "]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["full_df[full_df['sdk_yidun_device_id'] == 'cwCaRZO/lgFATURREFPBoPJhcRTfw53G']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}