{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(os.path.abspath(\"../src/\"))\n", "\n", "project_root = '/wkspace/qiushi/ltv-prediction/src'\n", "\n", "from datetime import datetime, timedelta\n", "from typing import List, Tuple, Any, Dict, Type\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm import tqdm\n", "from impala.dbapi import connect\n", "from collections import defaultdict\n", "import random\n", "import pytorch_lightning as pl\n", "import pickle\n", "from config import config"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from dataset.generate_any_day import get_any_day_training_data\n", "from dataset.light_data_any_day_prediction import AnyDayDataModule\n", "from pipelines.adp_inference import PipelineADPInference"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import time\n", "import os\n", "import traceback\n", "import torch\n", "from typing import List, Tuple, Any, Dict, Type\n", "import numpy as np\n", "import pandas as pd\n", "import json\n", "import pickle\n", "from tqdm import tqdm\n", "from collections import defaultdict\n", "\n", "from config import config\n", "from utils.mysql import MySQLConnector\n", "from dataset.output import MySQLOutput\n", "from pipelines.base import PipelineBase\n", "\n", "from model.torch_model_any_day_prediction import AnyDayModel\n", "from dataset.generate_any_day import get_any_day_training_data\n", "from dataset.light_data_any_day_prediction import process_behavior_dataframe, process_payment_dataframe"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class PipelineADPInferenceTest(PipelineADPInference):\n", "    def __init__(self, name: str, class_path: str, log_file_path: str, **kwargs):\n", "        super().__init__(name, class_path, log_file_path, **kwargs)\n", "        self.name = name  # 流水线名称 \n", "        self.today = datetime.now().date()  # 当前日期\n", "        self.ds = kwargs.get(\"ds\", (self.today - <PERSON><PERSON>ta(days=1)).strftime(\"%Y-%m-%d\"))\n", "        self.attribution_ds = (datetime.strptime(self.ds, \"%Y-%m-%d\") - timedelta(days=2)).strftime(\"%Y-%m-%d\")  # 数据时间范围为[attribution_ds, ds]\n", "        self.start_day = kwargs.get(\"start_day\", 3)\n", "        self.end_day = kwargs.get(\"end_day\", 7)\n", "        self.max_length = 14\n", "        \n", "        self.model_filepath = kwargs.get(\"model_filepath\")\n", "        if not os.path.isabs(self.model_filepath):\n", "            self.model_filepath = os.path.join(project_root, '..', self.model_filepath)\n", "            \n", "        self.normalize_dict = kwargs.get(\"normalize_dict\")\n", "        if not os.path.isabs(self.normalize_dict):\n", "            self.normalize_dict = os.path.join(project_root, '..', self.normalize_dict)\n", "            \n", "        self.encoding_map = kwargs.get(\"encoding_map\")\n", "        if not os.path.isabs(self.encoding_map):\n", "            self.encoding_map = os.path.join(project_root, '..', self.encoding_map)\n", "            \n", "        self.model = None\n", "        \n", "        self.user_adtrace_platform = {}\n", "        self.ad_adtrace_platform = {}\n", "        self.adtrace_platform_map = lambda x: '1' if x in ('jrtt', 'jrtt_wxgame', 'jrtt_xiansuo_v3', 'jrtt_xiansuo_v2', 'jrtt_dygame') else '2' if x in ('kuaishou', 'kuaishou_wxgame', 'kslive', 'ksunion') else '3' if x in ('gdt', 'gdt_v2', 'gdt_wxgame') else '101'\n", "        \n", "        self.merged_df_raw = None\n", "        self.device = None\n", "        \n", "        self.all_base_df = []\n", "        self.all_behavior_feature = []\n", "        self.all_campaign_feature = []\n", "        self.all_start_feature = []\n", "        self.all_end_feature = []\n", "                \n", "    def _load_model(self):\n", "    # 获取可用设备\n", "        device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "        print(f\"Using device: {device}\")\n", "        \n", "        # 创建模型\n", "        self.model = AnyDayModel()\n", "        \n", "        # 加载模型权重\n", "        if device.type == \"cuda\":\n", "            # 方法1：直接加载到GPU\n", "            state_dict_any_day = torch.load(config[\"path\"][\"adp_model_path\"], map_location=device)\n", "        else:\n", "            # 加载到CPU\n", "            print(\"Warining: using cpu\")\n", "            state_dict_any_day = torch.load(config[\"path\"][\"adp_model_path\"], map_location=\"cpu\")\n", "        \n", "        self.model.load_state_dict(state_dict_any_day)\n", "        \n", "        # 将模型移动到设备上\n", "        self.model = self.model.to(device)\n", "        \n", "        # 设置为评估模式\n", "        self.model.eval()\n", "        \n", "        self.device = device  # 返回设备以便在其他地方使用\n", "    \n", "    def _get_dataframe(self, start_date, end_date):\n", "        self.merged_df_raw = get_any_day_training_data(start_date, end_date, new_data=False) \n", "        return self.merged_df_raw\n", "    \n", "    def update_task_status(self, flag: str):\n", "        \"\"\" 更新任务状态 \"\"\"\n", "        conn = MySQLConnector(user=config['mysql']['user'], \n", "                            password=config['mysql']['password'], \n", "                            host=config['mysql']['host'], \n", "                            port=config['mysql']['port'], \n", "                            db=config['mysql']['database'])\n", "        conn.connect()\n", "        query = f\"insert into service_status (ds, task_name, task_status) values ('{self.ds}', '{self.name}', '{flag}') on duplicate key update task_status = '{flag}'\"\n", "        conn.execute(query)\n", "\n", "    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:\n", "        \"\"\" 加载数据 \"\"\"\n", "        # return self._load_data_from_csv()\n", "        return self._get_dataframe('2025-04-13', '2025-04-27')\n", "    \n", "    def process_base_dataframe(self, merged_df_raw, start_idx, end_idx):\n", "        base_df = merged_df_raw[\n", "            ['sdk_yidun_device_id', 'attribution_day',  'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']\n", "            ].copy()\n", "        sum_cols = [f\"d{i+1}_payment\" for i in range(start_idx, end_idx+1)]\n", "        existing_cols = [col for col in sum_cols if col in merged_df_raw.columns]\n", "        \n", "        numeric_df = merged_df_raw[existing_cols].apply(pd.to_numeric, errors='coerce')\n", "        numeric_df = numeric_df.fillna(0)\n", "\n", "        base_df['base_payment'] = numeric_df.sum(axis=1)\n", "\n", "        base_df['observed_days'] = start_idx + 1\n", "        base_df['predicted_days'] = end_idx + 1\n", "        return base_df\n", "    \n", "    def data_preprocess(self, merged_df_raw) -> Tuple[List[List[str]], pd.DataFrame]:\n", "        \n", "        drop_cols = ['sdk_yidun_device_id', 'attribution_day', 'attribution_day_a', \n", "                    'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']  + [f'd{i}_retention' for i in range(1, 31)]\n", "        payment_cols = [f'd{i}_payment' for i in range(1, 31)]\n", "        \n", "        existing_payment_cols = list(set(payment_cols) & set(merged_df_raw.columns))\n", "        \n", "        negative_mask = (merged_df_raw[existing_payment_cols].apply(pd.to_numeric, errors='coerce') < 0).any(axis=1)\n", "        if negative_mask.any():\n", "            merged_df_raw = merged_df_raw.loc[~negative_mask]\n", "            merged_df_raw.reset_index(drop=True, inplace=True)\n", "            \n", "        payment_df = merged_df_raw[existing_payment_cols].astype(float)\n", "        merged_df = merged_df_raw.drop(columns=drop_cols + payment_cols, errors='ignore')\n", "        \n", "        # print(merged_df_raw.head())\n", "        # print(merged_df_raw.shape)\n", "        # return merged_df\n", "        \n", "        merged_df = merged_df.sort_values(by = 'sdk_device_name')\n", "        \n", "        for _, row in merged_df_raw.iterrows():\n", "            self.user_adtrace_platform[row['sdk_yidun_device_id']] = self.adtrace_platform_map(str(row['adtrace_platform']))\n", "        \n", "        print(\"Data Read!\")\n", "        \n", "        behavior_df = merged_df[[col for col in merged_df.columns if col.startswith('behav')]]\n", "        campagin_df = merged_df[[col for col in merged_df.columns if not col.startswith('behav')]]\n", "        \n", "        # 从文件恢复\n", "        with open('../data/behavior_columns_test.pkl', 'rb') as f:\n", "            behavior_columns_restored = pickle.load(f)\n", "\n", "        with open('../data/campaign_columns_test.pkl', 'rb') as f:\n", "            campaign_columns_restored = pickle.load(f)\n", "            \n", "        behavior_df = behavior_df[behavior_columns_restored]\n", "        campagin_df = campagin_df[campaign_columns_restored]\n", "        \n", "        return behavior_df, campagin_df\n", "        \n", "        # for start_idx in range(0, 14):\n", "        #     print(f\"Processing Start Day = {start_idx}\")\n", "        #     for end_idx in tqdm(range(start_idx + 1, 14)):\n", "        #         base_df = self.process_base_dataframe(merged_df_raw, start_idx, end_idx)\n", "        #         behavior_feature = process_behavior_dataframe(behavior_df, start_idx)\n", "        #         campaign_feature = campagin_df.values.tolist()\n", "        #         start_feature = [start_idx + 1] * len(campaign_feature)\n", "        #         end_feature = [end_idx + 1] * len(campaign_feature)\n", "                \n", "        #         self.all_base_df.append(base_df)\n", "        #         self.all_behavior_feature.extend(behavior_feature)\n", "        #         self.all_campaign_feature.extend(campaign_feature)\n", "        #         self.all_start_feature.extend(start_feature)\n", "        #         self.all_end_feature.extend(end_feature)\n", "\n", "    \n", "    # def predict(self) -> <PERSON><PERSON>[Dict[str, float], Dict[str, float], Dict[str, int]]:\n", "    #     \"\"\" 预测LTV \"\"\"        \n", "    #     # guiyin_features = torch.tensor(self.all_campaign_feature, dtype=torch.float)\n", "    #     # behav_features = torch.tensor(self.all_behavior_feature, dtype=torch.float)\n", "    #     # behav_lengths = torch.tensor(self.all_start_feature, dtype=torch.long)\n", "    #     # target_dates = torch.tensor(self.all_end_feature, dtype=torch.long)\n", "\n", "    #     # # Stack all features into a single large batch\n", "    #     # batch = (guiyin_features, behav_features, behav_lengths, target_dates)\n", "\n", "    #     # with torch.no_grad():\n", "    #     #     predictions = self.model(batch)\n", "        \n", "    #     # # Use predictions directly\n", "    #     # predictions_list = predictions.cpu()  # Move predictions to CPU if needed\n", "        \n", "    #     # 假设 all_campaign_feature, all_behavior_feature, all_start_feature, all_end_feature 是列表或数组\n", "        \n", "    #     predictions_list = []\n", "    #     num_samples = len(self.all_campaign_feature)\n", "    #     batch_size = 1024\n", "        \n", "    #     device = next(self.model.parameters()).device\n", "    #     print(f\"Model is on device: {device}\")\n", "        \n", "    #     # 处理完整批次\n", "    #     for batch_start in tqdm(range(0, num_samples, batch_size)):\n", "    #         batch_end = min(batch_start + batch_size, num_samples)\n", "    #         # 获取模型所在的设备\n", "    #         device = next(self.model.parameters()).device\n", "\n", "    #         # 准备当前批次的数据\n", "    #         guiyin_features = torch.tensor(self.all_campaign_feature[batch_start:batch_end], dtype=torch.float).to(device)\n", "    #         behav_features = torch.tensor(self.all_behavior_feature[batch_start:batch_end], dtype=torch.float).to(device)\n", "    #         behav_lengths = torch.tensor(self.all_start_feature[batch_start:batch_end], dtype=torch.long).to(device)\n", "    #         target_dates = torch.tensor(self.all_end_feature[batch_start:batch_end], dtype=torch.long).to(device)\n", "            \n", "    #         # 组成批次\n", "    #         batch = (guiyin_features, behav_features, behav_lengths, target_dates)\n", "            \n", "    #         # 进行预测\n", "    #         with torch.no_grad():\n", "    #             batch_predictions = self.model(batch)\n", "    #             predictions_list.append(batch_predictions.cpu())\n", "    \n", "                \n", "    #         # 输出预测结果或进一步处理\n", "    #         # print(f\"Prediction for record {i}: {prediction}\")\n", "\n", "    #     # Further processing on predictions_list can be done here\n", "    #     print(predictions_list[0].shape)\n", "    #     predictions_tensor = torch.cat(predictions_list, dim=0) \n", "    #     print(predictions_tensor.shape)       \n", "    #     pay_probabilities = torch.sigmoid(predictions_tensor[:, 0])\n", "\n", "    #     predicted_pay_values = predictions_tensor[:, 1]\n", "    #     final_predictions = torch.zeros_like(predicted_pay_values)\n", "\n", "    #     # 1. 如果付费概率 >= 0.5，使用预测的付费真实值\n", "    #     # 2. 如果付费概率 < 0.5，预测值为0\n", "    #     mask = pay_probabilities >= 0.5\n", "    #     final_predictions[mask] = predicted_pay_values[mask]\n", "    #     final_predictions_np = final_predictions.cpu().numpy()\n", "\n", "    #     # assert final_predictions_np % self.payment_df == 0\n", "        \n", "    #     stacked_df = pd.concat(self.all_base_df, ignore_index=True)\n", "    #     # y_base = self.payment_df[['d1_payment', 'd2_payment', 'd3_payment']].sum(axis=1)\n", "    #     print(np.shape(final_predictions_np))\n", "    #     print(len(stacked_df))\n", "    #     y_pred = np.expm1(final_predictions_np) + stacked_df['base_payment'].to_numpy()\n", "    #     y_pred = np.maximum(y_pred, 0)\n", "    #     y_pred = np.asarray(y_pred)\n", "    #     # y_base = np.asarray(stacked_df['base_payment'])\n", "\n", "    #     predicted_user_ltv = defaultdict(float)\n", "    #     predicted_campaign_ltv = defaultdict(float)\n", "    #     campaign_user_counts = defaultdict(int)\n", "        \n", "    #     for i, row in stacked_df.iterrows():\n", "    #         sdk_yidun_device_id_ = row['sdk_yidun_device_id']\n", "    #         channel_ty_adgroup_id_ = row['channel_ty_adgroup_id']\n", "    #         observed_days_ = row['observed_days']\n", "    #         predicted_days_ = row['predicted_days']\n", "            \n", "    #         # (, attribution_day,  channel_ty_account_id, channel_ty_adgroup_id, channel_ty_campaign_id)\n", "    #         predicted_user_ltv[(sdk_yidun_device_id_, observed_days_, predicted_days_)] += y_pred[i]\n", "    #         if channel_ty_adgroup_id_ not in ['unknown', '__DID__', '-1', 'nan', '']:\n", "    #             predicted_campaign_ltv[(channel_ty_adgroup_id_, observed_days_, predicted_days_)] += y_pred[i]\n", "    #             campaign_user_counts[(channel_ty_adgroup_id_, observed_days_, predicted_days_)] += 1\n", "    #             # 增加广告ADTRACE_PLATFORM映射\n", "    #             self.ad_adtrace_platform[(channel_ty_adgroup_id_, observed_days_, predicted_days_)] = self.user_adtrace_platform[sdk_yidun_device_id_]\n", "                \n", "    #     return predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts\n", "        \n", "            \n", "    # def output(self, predicted_user_ltv: Dict[str, float], \n", "    #             predicted_campaign_ltv: Dict[str, float], \n", "    #             campaign_user_counts: Dict[str, int]): \n", "        \n", "    #     # 输出用户LTV预测值\n", "    #     user_ltv_prediction_data = [\n", "    #         {\n", "    #             \"sdk_yidun_device_id\": sdk_yidun_device_id,\n", "    #             \"attribution_day\": self.attribution_ds,\n", "    #             \"adtrace_platform\": self.user_adtrace_platform[sdk_yidun_device_id],\n", "    #             \"observed_days\": observed_days,\n", "    #             \"predicted_days\": predicted_days,\n", "    #             \"pltv\": pltv,\n", "    #             \"record_partition\": self.ds,\n", "    #         }\n", "    #         for (sdk_yidun_device_id, observed_days, predicted_days), pltv in predicted_user_ltv.items()\n", "    #     ]\n", "    #     user_ltv_df = pd.DataFrame(user_ltv_prediction_data)\n", "    #     print(user_ltv_df.head())\n", "    #     # m.output_user_ltv_prediction(user_ltv_df, self.name)\n", "        \n", "    #     # 汇总用户LTV预测值，输出广告LTV预测值\n", "    #     campaign_ltv_prediction_data = [\n", "    #         {\n", "    #             \"channel_ty_adgroup_id\": channel_ty_adgroup_id,\n", "    #             \"attribution_day\": self.attribution_ds, # （当前仅包含一天）\n", "    #             \"adtrace_platform\": self.ad_adtrace_platform[(channel_ty_adgroup_id, observed_days, predicted_days)],\n", "    #             \"observed_days\": observed_days,\n", "    #             \"predicted_days\": predicted_days,\n", "    #             \"pltv\": pltv,\n", "    #             \"user_count\": campaign_user_counts[channel_ty_adgroup_id],\n", "    #             \"record_partition\": self.ds,\n", "    #         }\n", "    #         for (channel_ty_adgroup_id, observed_days, predicted_days), pltv in predicted_campaign_ltv.items()\n", "    #     ]\n", "    #     campaign_ltv_df = pd.DataFrame(campaign_ltv_prediction_data)\n", "    #     print(campaign_ltv_df.head())\n", "    #     # m.output_ad_ltv_prediction(campaign_ltv_df, self.name)\n", "        \n", "    #     return user_ltv_df, campaign_ltv_df\n", "        \n", "    \n", "    def execute(self) -> Dict[str, Any]:\n", "        \"\"\"执行完整流水线\"\"\"\n", "        try:\n", "            # 模型加载\n", "            self.logger.info(\"model loading...\")\n", "            self._load_model()\n", "            \n", "            # 数据加载\n", "            self.logger.info(\"data loading...\")\n", "            self.load_data()\n", "            \n", "            # 数据预处理\n", "            self.logger.info(\"data preprocessing...\")\n", "            result = self.data_preprocess(self.merged_df_raw)\n", "            # self.verify_data()\n", "            \n", "            # # 执行预测\n", "            # self.logger.info(\"predicting...\")\n", "            # predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts = self.predict()\n", "            \n", "            # # 结果保存\n", "            # self.logger.info(\"saving results...\")\n", "            # self.output(predicted_user_ltv, predicted_campaign_ltv, campaign_user_counts)\n", "            \n", "            # # 更新任务状态\n", "            # self.update_task_status(flag='success')\n", "            \n", "            # # 收集执行结果\n", "            # result = {\n", "            #     \"status\": \"success\",\n", "            #     \"execution_time\": str(timedelta(seconds=time.time() - start_time)),\n", "            #     \"info\": f\"len(predicted_user_ltv): {len(predicted_user_ltv)}, len(predicted_campaign_ltv): {len(predicted_campaign_ltv)}\",\n", "            # }\n", "            \n", "        except Exception as e:\n", "            self.logger.error(f\"pipeline {self.name} failed: {str(e)}\", exc_info=True)\n", "            exc_type, exc_value, exc_traceback = sys.exc_info()\n", "            result = {\n", "                \"status\": \"failed\",\n", "                \"error\": str(e)\n", "            }\n", "            self.update_task_status(flag='failure')\n", "            \n", "        return result\n", "    "]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-19 08:51:28,360 INFO pipeline adp_v202505 initialized\n", "2025-05-19 08:51:28,361 INFO model loading...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Using device: cuda\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-19 08:51:29,042 INFO data loading...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading Impala Data......\n", "['2025-04-13']\n", "Loading Campaign Data\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1/1 [00:04<00:00,  4.61s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading Behavior Data, This May Take a While......\n", "Data Processing......\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/wkspace/sunyi/LTV_prediction/src/utils/impala_utils.py:125: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_filter['day_diff'] = (df_filter['day'] - df_filter['attribution_day']).dt.days\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dropping Nans......\n", "Normalizing Datas......\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 33/33 [00:01<00:00, 18.91it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Encoding Category Features......\n", "Merging Two Dataframes......\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-19 08:54:20,526 INFO data preprocessing...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish!\n", "Data Read!\n"]}], "source": ["df = PipelineADPInferenceTest(**config[\"pipelines\"][0]).execute()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sdk_os_name</th>\n", "      <th>adtrace_organic_traffic</th>\n", "      <th>adtrace_platform</th>\n", "      <th>adtrace_device_caid_main_version</th>\n", "      <th>adtrace_namespace</th>\n", "      <th>sdk_systemversion</th>\n", "      <th>sdk_os_version</th>\n", "      <th>adtrace_is_blacklist</th>\n", "      <th>sdk_countrycode</th>\n", "      <th>sdk_memory</th>\n", "      <th>...</th>\n", "      <th>proj_sub_channel</th>\n", "      <th>proj_game_type</th>\n", "      <th>channel_ty_csite_id</th>\n", "      <th>adtrace_assist_platform</th>\n", "      <th>sdk_split_ua_result</th>\n", "      <th>sdk_carrier_name</th>\n", "      <th>proj_virtual_channel</th>\n", "      <th>sdk_virtual_channel_version</th>\n", "      <th>channel_ty_creative_id</th>\n", "      <th>proj_cloud_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2266</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>46</td>\n", "      <td>53</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>382</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>4322</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3701</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>17</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>7</td>\n", "      <td>260</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>758</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>319</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>910</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>4826</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>808</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>10</td>\n", "      <td>7</td>\n", "      <td>910</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1290</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4626</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>17</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>7</td>\n", "      <td>910</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>272</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>671</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>34</td>\n", "      <td>40</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>299</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>4389</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2875</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>48</td>\n", "      <td>55</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>286</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3648</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>683</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>44</td>\n", "      <td>51</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>385</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>802</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>45</td>\n", "      <td>52</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>220</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2258</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3024</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>33</td>\n", "      <td>39</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>299</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>23</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>4501</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7165 rows × 46 columns</p>\n", "</div>"], "text/plain": ["      sdk_os_name  adtrace_organic_traffic  adtrace_platform  \\\n", "2266            2                        1                 3   \n", "3701            1                        1                 6   \n", "319             1                        1                 4   \n", "808             1                        1                 6   \n", "4626            1                        1                 6   \n", "...           ...                      ...               ...   \n", "671             2                        1                 3   \n", "2875            2                        1                 3   \n", "683             2                        1                 6   \n", "220             2                        1                 3   \n", "3024            2                        1                 3   \n", "\n", "      adtrace_device_caid_main_version  adtrace_namespace  sdk_systemversion  \\\n", "2266                                 2                  2                 46   \n", "3701                                 0                  1                  0   \n", "319                                  0                  4                  0   \n", "808                                  0                  1                  0   \n", "4626                                 0                  1                  0   \n", "...                                ...                ...                ...   \n", "671                                  1                  2                 34   \n", "2875                                 2                  2                 48   \n", "683                                  2                  2                 44   \n", "220                                  2                  2                 45   \n", "3024                                 2                  2                 33   \n", "\n", "      sdk_os_version  adtrace_is_blacklist  sdk_countrycode  sdk_memory  ...  \\\n", "2266              53                     0               10         382  ...   \n", "3701               2                     1                0           0  ...   \n", "319               12                     1                0           0  ...   \n", "808               12                     1                0           0  ...   \n", "4626              12                     1                0           0  ...   \n", "...              ...                   ...              ...         ...  ...   \n", "671               40                     0               10         299  ...   \n", "2875              55                     0               10         286  ...   \n", "683               51                     0               10         385  ...   \n", "220               52                     0               10         220  ...   \n", "3024              39                     0               10         299  ...   \n", "\n", "      proj_sub_channel  proj_game_type  channel_ty_csite_id  \\\n", "2266                 1               1                   16   \n", "3701                17               2                    8   \n", "319                 13               2                    7   \n", "808                 13               2                   10   \n", "4626                17               2                    8   \n", "...                ...             ...                  ...   \n", "671                  1               1                   17   \n", "2875                 1               1                   17   \n", "683                  1               1                    4   \n", "220                  1               1                   16   \n", "3024                 1               1                   23   \n", "\n", "      adtrace_assist_platform  sdk_split_ua_result  sdk_carrier_name  \\\n", "2266                        1                    0                 1   \n", "3701                        7                  260                 2   \n", "319                         5                  910                 1   \n", "808                         7                  910                 2   \n", "4626                        7                  910                 4   \n", "...                       ...                  ...               ...   \n", "671                         1                    0                 1   \n", "2875                        1                    0                 1   \n", "683                         1                    0                 1   \n", "220                         1                    0                 1   \n", "3024                        1                    0                 1   \n", "\n", "      proj_virtual_channel  sdk_virtual_channel_version  \\\n", "2266                     1                            0   \n", "3701                     1                            0   \n", "319                      3                            3   \n", "808                      1                            0   \n", "4626                     1                            0   \n", "...                    ...                          ...   \n", "671                      1                            0   \n", "2875                     1                            0   \n", "683                      1                            0   \n", "220                      1                            0   \n", "3024                     1                            0   \n", "\n", "      channel_ty_creative_id  proj_cloud_id  \n", "2266                    4322              1  \n", "3701                     758              1  \n", "319                     4826              1  \n", "808                     1290              1  \n", "4626                     272              1  \n", "...                      ...            ...  \n", "671                     4389              1  \n", "2875                    3648              1  \n", "683                      802              1  \n", "220                     2258              1  \n", "3024                    4501              1  \n", "\n", "[7165 rows x 46 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df1[1]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sdk_os_name</th>\n", "      <th>adtrace_organic_traffic</th>\n", "      <th>adtrace_platform</th>\n", "      <th>adtrace_device_caid_main_version</th>\n", "      <th>adtrace_namespace</th>\n", "      <th>sdk_systemversion</th>\n", "      <th>sdk_os_version</th>\n", "      <th>adtrace_is_blacklist</th>\n", "      <th>sdk_countrycode</th>\n", "      <th>sdk_memory</th>\n", "      <th>...</th>\n", "      <th>proj_sub_channel</th>\n", "      <th>proj_game_type</th>\n", "      <th>channel_ty_csite_id</th>\n", "      <th>adtrace_assist_platform</th>\n", "      <th>sdk_split_ua_result</th>\n", "      <th>sdk_carrier_name</th>\n", "      <th>proj_virtual_channel</th>\n", "      <th>sdk_virtual_channel_version</th>\n", "      <th>channel_ty_creative_id</th>\n", "      <th>proj_cloud_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2711</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>46</td>\n", "      <td>53</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>382</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>4322</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>678</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>17</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>7</td>\n", "      <td>260</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>758</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1348</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>910</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>4826</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6592</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>17</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>7</td>\n", "      <td>910</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>272</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3748</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>10</td>\n", "      <td>7</td>\n", "      <td>910</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1290</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1632</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>34</td>\n", "      <td>40</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>299</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>4389</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6683</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>48</td>\n", "      <td>55</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>286</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3648</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5499</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>44</td>\n", "      <td>51</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>385</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>802</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5816</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>45</td>\n", "      <td>52</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>220</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2258</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2269</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>33</td>\n", "      <td>39</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>299</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>23</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>4501</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7165 rows × 46 columns</p>\n", "</div>"], "text/plain": ["      sdk_os_name  adtrace_organic_traffic  adtrace_platform  \\\n", "2711            2                        1                 3   \n", "678             1                        1                 6   \n", "1348            1                        1                 4   \n", "6592            1                        1                 6   \n", "3748            1                        1                 6   \n", "...           ...                      ...               ...   \n", "1632            2                        1                 3   \n", "6683            2                        1                 3   \n", "5499            2                        1                 6   \n", "5816            2                        1                 3   \n", "2269            2                        1                 3   \n", "\n", "      adtrace_device_caid_main_version  adtrace_namespace  sdk_systemversion  \\\n", "2711                                 2                  2                 46   \n", "678                                  0                  1                  0   \n", "1348                                 0                  4                  0   \n", "6592                                 0                  1                  0   \n", "3748                                 0                  1                  0   \n", "...                                ...                ...                ...   \n", "1632                                 1                  2                 34   \n", "6683                                 2                  2                 48   \n", "5499                                 2                  2                 44   \n", "5816                                 2                  2                 45   \n", "2269                                 2                  2                 33   \n", "\n", "      sdk_os_version  adtrace_is_blacklist  sdk_countrycode  sdk_memory  ...  \\\n", "2711              53                     0               10         382  ...   \n", "678                2                     1                0           0  ...   \n", "1348              12                     1                0           0  ...   \n", "6592              12                     1                0           0  ...   \n", "3748              12                     1                0           0  ...   \n", "...              ...                   ...              ...         ...  ...   \n", "1632              40                     0               10         299  ...   \n", "6683              55                     0               10         286  ...   \n", "5499              51                     0               10         385  ...   \n", "5816              52                     0               10         220  ...   \n", "2269              39                     0               10         299  ...   \n", "\n", "      proj_sub_channel  proj_game_type  channel_ty_csite_id  \\\n", "2711                 1               1                   16   \n", "678                 17               2                    8   \n", "1348                13               2                    7   \n", "6592                17               2                    8   \n", "3748                13               2                   10   \n", "...                ...             ...                  ...   \n", "1632                 1               1                   17   \n", "6683                 1               1                   17   \n", "5499                 1               1                    4   \n", "5816                 1               1                   16   \n", "2269                 1               1                   23   \n", "\n", "      adtrace_assist_platform  sdk_split_ua_result  sdk_carrier_name  \\\n", "2711                        1                    0                 1   \n", "678                         7                  260                 2   \n", "1348                        5                  910                 1   \n", "6592                        7                  910                 4   \n", "3748                        7                  910                 2   \n", "...                       ...                  ...               ...   \n", "1632                        1                    0                 1   \n", "6683                        1                    0                 1   \n", "5499                        1                    0                 1   \n", "5816                        1                    0                 1   \n", "2269                        1                    0                 1   \n", "\n", "      proj_virtual_channel  sdk_virtual_channel_version  \\\n", "2711                     1                            0   \n", "678                      1                            0   \n", "1348                     3                            3   \n", "6592                     1                            0   \n", "3748                     1                            0   \n", "...                    ...                          ...   \n", "1632                     1                            0   \n", "6683                     1                            0   \n", "5499                     1                            0   \n", "5816                     1                            0   \n", "2269                     1                            0   \n", "\n", "      channel_ty_creative_id  proj_cloud_id  \n", "2711                    4322              1  \n", "678                      758              1  \n", "1348                    4826              1  \n", "6592                     272              1  \n", "3748                    1290              1  \n", "...                      ...            ...  \n", "1632                    4389              1  \n", "6683                    3648              1  \n", "5499                     802              1  \n", "5816                    2258              1  \n", "2269                    4501              1  \n", "\n", "[7165 rows x 46 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df2[1]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'tuple' object has no attribute 'sort_values'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[12], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mdf1\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msort_values\u001b[49m(by \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124msdk_device_name\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[0;31mAttributeError\u001b[0m: 'tuple' object has no attribute 'sort_values'"]}], "source": ["df1.sort_values(by = 'sdk_device_name')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sdk_device_name</th>\n", "      <th>sdk_devicename</th>\n", "      <th>adtrace_act_name</th>\n", "      <th>sdk_virtual_channel</th>\n", "      <th>adtrace_organic_traffic</th>\n", "      <th>sdk_timezone</th>\n", "      <th>adtrace_aid</th>\n", "      <th>proj_sub_channel</th>\n", "      <th>proj_client_id</th>\n", "      <th>sdk_package_name</th>\n", "      <th>...</th>\n", "      <th>behav.activity_night</th>\n", "      <th>behav.game_time_in_minutes</th>\n", "      <th>behav.final_delta</th>\n", "      <th>behav.max_delta</th>\n", "      <th>behav.min_delta</th>\n", "      <th>behav.max_gunlevel</th>\n", "      <th>behav.min_gunlevel</th>\n", "      <th>behav.max_boss_rate</th>\n", "      <th>behav.total_catch</th>\n", "      <th>behav.total_catch_boss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4460</th>\n", "      <td>1</td>\n", "      <td>193</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>67</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>[1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, ...</td>\n", "      <td>[0.07317073170731707, 0.09059233449477352, 0.0...</td>\n", "      <td>[1.4789591847459167e-07, 0.0001053643326815127...</td>\n", "      <td>[5.454545454545455e-07, 0.00040404040404040404...</td>\n", "      <td>[1.4285714285714286e-09, 4.2857142857142857e-0...</td>\n", "      <td>[2.2727272727272726e-05, 0.004090909090909091,...</td>\n", "      <td>[4.545454545454546e-08, 1.3636363636363637e-05...</td>\n", "      <td>[0.03176470588235294, 0.09802941176470588, 0.0...</td>\n", "      <td>[0.014345464282981816, 0.02051720180561577, 0....</td>\n", "      <td>[0.01972386587771203, 0.05128205128205128, 0.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5445</th>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>62</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>45</td>\n", "      <td>12</td>\n", "      <td>...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.041811846689895474, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[5.9248827796083774e-08, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[2.439393939393939e-07, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[1.4285714285714286e-09, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[2.2727272727272726e-05, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.014205882352941176, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.009436127617250262, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.005917159763313609, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5905</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>81</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>13</td>\n", "      <td>24</td>\n", "      <td>7</td>\n", "      <td>...</td>\n", "      <td>[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.06620209059233449, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[1.4332403837491134e-07, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[4.545454545454545e-07, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[1.4285714285714286e-09, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[2.2727272727272726e-05, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.01588235294117647, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[0.017099793425314324, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.01282051282051282, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2753</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>71</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>43</td>\n", "      <td>12</td>\n", "      <td>...</td>\n", "      <td>[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.010452961672473868, 0.006968641114982578, 0...</td>\n", "      <td>[5.726259690321855e-08, 1.2886097755998547e-09...</td>\n", "      <td>[8.181818181818182e-07, 3.367003367003367e-08,...</td>\n", "      <td>[6.428571428571429e-06, 5.7142857142857145e-06...</td>\n", "      <td>[0.00020454545454545454, 0.0004545454545454545...</td>\n", "      <td>[0.00020454545454545454, 0.0001818181818181818...</td>\n", "      <td>[0.003176470588235294, 5.882352941176471e-05, ...</td>\n", "      <td>[0.00024227895233480402, 2.5503047614189897e-0...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6959</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>62</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>45</td>\n", "      <td>12</td>\n", "      <td>...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.010452961672473868, 0.0, 0.0...</td>\n", "      <td>[0.0, 0.0, 0.0, 1.8660680312905397e-08, 0.0, 0...</td>\n", "      <td>[0.0, 0.0, 0.0, 1.8181818181818183e-07, 0.0, 0...</td>\n", "      <td>[0.0, 0.0, 0.0, 4.2857142857142857e-07, 0.0, 0...</td>\n", "      <td>[0.0, 0.0, 0.0, 1.3636363636363637e-05, 0.0, 0...</td>\n", "      <td>[0.0, 0.0, 0.0, 1.3636363636363637e-05, 0.0, 0...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.010588235294117647, 0.0, 0.0...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0028563413327892685, 0.0, 0....</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0019723865877712033, 0.0, 0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5345</th>\n", "      <td>1803</td>\n", "      <td>273</td>\n", "      <td>116</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>67</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3603</th>\n", "      <td>1805</td>\n", "      <td>142</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>67</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.010452961672473868, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[1.1197482029222905e-08, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[8.417508417508417e-08, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[4.2857142857142857e-07, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[0.0009090909090909091, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[1.3636363636363637e-05, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[0.0017647058823529412, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.000739588380811507, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2342</th>\n", "      <td>1808</td>\n", "      <td>25</td>\n", "      <td>94</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>67</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, ...</td>\n", "      <td>[0.2264808362369338, 0.0313588850174216, 0.163...</td>\n", "      <td>[7.590291879243169e-06, 6.972694341807806e-07,...</td>\n", "      <td>[8.720538720538721e-05, 1.0101010101010101e-05...</td>\n", "      <td>[1.4285714285714286e-09, 4.2857142857142857e-0...</td>\n", "      <td>[0.0020454545454545456, 0.0001818181818181818,...</td>\n", "      <td>[4.545454545454546e-08, 1.3636363636363637e-05...</td>\n", "      <td>[0.17058823529411765, 0.04411764705882353, 0.1...</td>\n", "      <td>[0.034033817041136415, 0.0040167299992349085, ...</td>\n", "      <td>[0.06804733727810651, 0.0029585798816568047, 0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6164</th>\n", "      <td>1815</td>\n", "      <td>257</td>\n", "      <td>38</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>67</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.04529616724738676, 0.0, 0.0, 0.0, 0.0, 0.0,...</td>\n", "      <td>[7.275241124892758e-08, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[1.3636363636363637e-07, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[1.4285714285714286e-09, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[1.8181818181818182e-05, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "      <td>[0.007147058823529412, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.014128688378261202, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.008875739644970414, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4696</th>\n", "      <td>1820</td>\n", "      <td>110</td>\n", "      <td>36</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>67</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...</td>\n", "      <td>[0.020905923344947737, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[2.7507845495582136e-07, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[3.0303030303030305e-06, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[1.4285714285714286e-08, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[0.00020454545454545454, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[2.2727272727272726e-07, 0.0, 0.0, 0.0, 0.0, 0...</td>\n", "      <td>[0.052941176470588235, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.004169748284920048, 0.0, 0.0, 0.0, 0.0, 0.0...</td>\n", "      <td>[0.0029585798816568047, 0.0, 0.0, 0.0, 0.0, 0....</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7165 rows × 76 columns</p>\n", "</div>"], "text/plain": ["      sdk_device_name  sdk_devicename  adtrace_act_name  sdk_virtual_channel  \\\n", "4460                1             193                23                    0   \n", "5445                9               0                62                    0   \n", "5905               20               0                81                    2   \n", "2753               20               0                71                    0   \n", "6959               20               0                62                    0   \n", "...               ...             ...               ...                  ...   \n", "5345             1803             273               116                    0   \n", "3603             1805             142                23                    0   \n", "2342             1808              25                94                    0   \n", "6164             1815             257                38                    0   \n", "4696             1820             110                36                    0   \n", "\n", "      adtrace_organic_traffic  sdk_timezone  adtrace_aid  proj_sub_channel  \\\n", "4460                        1            13            2                 1   \n", "5445                        1             0            1                17   \n", "5905                        1             0            4                13   \n", "2753                        1             0            1                13   \n", "6959                        1             0            1                17   \n", "...                       ...           ...          ...               ...   \n", "5345                        1            13            2                 1   \n", "3603                        1            13            2                 1   \n", "2342                        1            13            2                 1   \n", "6164                        1            13            2                 1   \n", "4696                        1            13            2                 1   \n", "\n", "      proj_client_id  sdk_package_name  ...  \\\n", "4460              67                 1  ...   \n", "5445              45                12  ...   \n", "5905              24                 7  ...   \n", "2753              43                12  ...   \n", "6959              45                12  ...   \n", "...              ...               ...  ...   \n", "5345              67                 1  ...   \n", "3603              67                 1  ...   \n", "2342              67                 1  ...   \n", "6164              67                 1  ...   \n", "4696              67                 1  ...   \n", "\n", "                                   behav.activity_night  \\\n", "4460  [1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, ...   \n", "5445  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "5905  [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "2753  [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "6959  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "...                                                 ...   \n", "5345  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3603  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "2342  [0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, ...   \n", "6164  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "4696  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "\n", "                             behav.game_time_in_minutes  \\\n", "4460  [0.07317073170731707, 0.09059233449477352, 0.0...   \n", "5445  [0.041811846689895474, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "5905  [0.06620209059233449, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "2753  [0.010452961672473868, 0.006968641114982578, 0...   \n", "6959  [0.0, 0.0, 0.0, 0.010452961672473868, 0.0, 0.0...   \n", "...                                                 ...   \n", "5345  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3603  [0.010452961672473868, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "2342  [0.2264808362369338, 0.0313588850174216, 0.163...   \n", "6164  [0.04529616724738676, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "4696  [0.020905923344947737, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "\n", "                                      behav.final_delta  \\\n", "4460  [1.4789591847459167e-07, 0.0001053643326815127...   \n", "5445  [5.9248827796083774e-08, 0.0, 0.0, 0.0, 0.0, 0...   \n", "5905  [1.4332403837491134e-07, 0.0, 0.0, 0.0, 0.0, 0...   \n", "2753  [5.726259690321855e-08, 1.2886097755998547e-09...   \n", "6959  [0.0, 0.0, 0.0, 1.8660680312905397e-08, 0.0, 0...   \n", "...                                                 ...   \n", "5345  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3603  [1.1197482029222905e-08, 0.0, 0.0, 0.0, 0.0, 0...   \n", "2342  [7.590291879243169e-06, 6.972694341807806e-07,...   \n", "6164  [7.275241124892758e-08, 0.0, 0.0, 0.0, 0.0, 0....   \n", "4696  [2.7507845495582136e-07, 0.0, 0.0, 0.0, 0.0, 0...   \n", "\n", "                                        behav.max_delta  \\\n", "4460  [5.454545454545455e-07, 0.00040404040404040404...   \n", "5445  [2.439393939393939e-07, 0.0, 0.0, 0.0, 0.0, 0....   \n", "5905  [4.545454545454545e-07, 0.0, 0.0, 0.0, 0.0, 0....   \n", "2753  [8.181818181818182e-07, 3.367003367003367e-08,...   \n", "6959  [0.0, 0.0, 0.0, 1.8181818181818183e-07, 0.0, 0...   \n", "...                                                 ...   \n", "5345  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3603  [8.417508417508417e-08, 0.0, 0.0, 0.0, 0.0, 0....   \n", "2342  [8.720538720538721e-05, 1.0101010101010101e-05...   \n", "6164  [1.3636363636363637e-07, 0.0, 0.0, 0.0, 0.0, 0...   \n", "4696  [3.0303030303030305e-06, 0.0, 0.0, 0.0, 0.0, 0...   \n", "\n", "                                        behav.min_delta  \\\n", "4460  [1.4285714285714286e-09, 4.2857142857142857e-0...   \n", "5445  [1.4285714285714286e-09, 0.0, 0.0, 0.0, 0.0, 0...   \n", "5905  [1.4285714285714286e-09, 0.0, 0.0, 0.0, 0.0, 0...   \n", "2753  [6.428571428571429e-06, 5.7142857142857145e-06...   \n", "6959  [0.0, 0.0, 0.0, 4.2857142857142857e-07, 0.0, 0...   \n", "...                                                 ...   \n", "5345  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3603  [4.2857142857142857e-07, 0.0, 0.0, 0.0, 0.0, 0...   \n", "2342  [1.4285714285714286e-09, 4.2857142857142857e-0...   \n", "6164  [1.4285714285714286e-09, 0.0, 0.0, 0.0, 0.0, 0...   \n", "4696  [1.4285714285714286e-08, 0.0, 0.0, 0.0, 0.0, 0...   \n", "\n", "                                     behav.max_gunlevel  \\\n", "4460  [2.2727272727272726e-05, 0.004090909090909091,...   \n", "5445  [2.2727272727272726e-05, 0.0, 0.0, 0.0, 0.0, 0...   \n", "5905  [2.2727272727272726e-05, 0.0, 0.0, 0.0, 0.0, 0...   \n", "2753  [0.00020454545454545454, 0.0004545454545454545...   \n", "6959  [0.0, 0.0, 0.0, 1.3636363636363637e-05, 0.0, 0...   \n", "...                                                 ...   \n", "5345  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3603  [0.0009090909090909091, 0.0, 0.0, 0.0, 0.0, 0....   \n", "2342  [0.0020454545454545456, 0.0001818181818181818,...   \n", "6164  [1.8181818181818182e-05, 0.0, 0.0, 0.0, 0.0, 0...   \n", "4696  [0.00020454545454545454, 0.0, 0.0, 0.0, 0.0, 0...   \n", "\n", "                                     behav.min_gunlevel  \\\n", "4460  [4.545454545454546e-08, 1.3636363636363637e-05...   \n", "5445  [4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....   \n", "5905  [4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....   \n", "2753  [0.00020454545454545454, 0.0001818181818181818...   \n", "6959  [0.0, 0.0, 0.0, 1.3636363636363637e-05, 0.0, 0...   \n", "...                                                 ...   \n", "5345  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3603  [1.3636363636363637e-05, 0.0, 0.0, 0.0, 0.0, 0...   \n", "2342  [4.545454545454546e-08, 1.3636363636363637e-05...   \n", "6164  [4.545454545454546e-08, 0.0, 0.0, 0.0, 0.0, 0....   \n", "4696  [2.2727272727272726e-07, 0.0, 0.0, 0.0, 0.0, 0...   \n", "\n", "                                    behav.max_boss_rate  \\\n", "4460  [0.03176470588235294, 0.09802941176470588, 0.0...   \n", "5445  [0.014205882352941176, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "5905  [0.01588235294117647, 0.0, 0.0, 0.0, 0.0, 0.0,...   \n", "2753  [0.003176470588235294, 5.882352941176471e-05, ...   \n", "6959  [0.0, 0.0, 0.0, 0.010588235294117647, 0.0, 0.0...   \n", "...                                                 ...   \n", "5345  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3603  [0.0017647058823529412, 0.0, 0.0, 0.0, 0.0, 0....   \n", "2342  [0.17058823529411765, 0.04411764705882353, 0.1...   \n", "6164  [0.007147058823529412, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "4696  [0.052941176470588235, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "\n", "                                      behav.total_catch  \\\n", "4460  [0.014345464282981816, 0.02051720180561577, 0....   \n", "5445  [0.009436127617250262, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "5905  [0.017099793425314324, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "2753  [0.00024227895233480402, 2.5503047614189897e-0...   \n", "6959  [0.0, 0.0, 0.0, 0.0028563413327892685, 0.0, 0....   \n", "...                                                 ...   \n", "5345  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...   \n", "3603  [0.000739588380811507, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "2342  [0.034033817041136415, 0.0040167299992349085, ...   \n", "6164  [0.014128688378261202, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "4696  [0.004169748284920048, 0.0, 0.0, 0.0, 0.0, 0.0...   \n", "\n", "                                 behav.total_catch_boss  \n", "4460  [0.01972386587771203, 0.05128205128205128, 0.0...  \n", "5445  [0.005917159763313609, 0.0, 0.0, 0.0, 0.0, 0.0...  \n", "5905  [0.01282051282051282, 0.0, 0.0, 0.0, 0.0, 0.0,...  \n", "2753  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  \n", "6959  [0.0, 0.0, 0.0, 0.0019723865877712033, 0.0, 0....  \n", "...                                                 ...  \n", "5345  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  \n", "3603  [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, ...  \n", "2342  [0.06804733727810651, 0.0029585798816568047, 0...  \n", "6164  [0.008875739644970414, 0.0, 0.0, 0.0, 0.0, 0.0...  \n", "4696  [0.0029585798816568047, 0.0, 0.0, 0.0, 0.0, 0....  \n", "\n", "[7165 rows x 76 columns]"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["df2.sort_values(by = 'sdk_device_name')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}